'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { authService } from '../../../services/auth.service';
import { AuthLayout, StatusMessage, SuccessState, PageTransition } from '@/components/auth';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [email, setEmail] = useState<string | ''>('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | ''>('');
  const [error, setError] = useState<string | ''>('');
  const [submitted, setSubmitted] = useState(false);
  const [hasMounted, setHasMounted] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    setHasMounted(true);
    if (submitted && !isRedirecting) {
      setIsRedirecting(true);
      sessionStorage.clear();
      // Redirects to login page after 5s
      const timeout = setTimeout(() => {
        router.push('/auth/login');
      }, 5000);

      return () => clearTimeout(timeout);
    }
  }, [submitted, router, isRedirecting]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await authService.forgotPassword({ email });
      setMessage(response.message);
      setSubmitted(true);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to send reset email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state during form submission
  if (loading) {
    return (
      <PageTransition
        isLoading={true}
        loadingMessage="Sending reset link..."
        loadingSubmessage="Please wait while we process your request"
        dynamicMessages={[
          'Verifying email address...',
          'Generating secure reset link...',
          'Sending email...',
          'Almost done...'
        ]}
        showProgress={true}
      >
        <div />
      </PageTransition>
    );
  }

  // Show success state after submission
  if (submitted && hasMounted) {
    return (
      <AuthLayout
        title="Reset Link Sent"
        subtitle="Check your email for further instructions"
        showBackToLogin={false}
        isCustomerPortal={false}
      >
        <SuccessState
          title="Email Sent Successfully!"
          message="If your account exists, you will receive an email with a reset link."
          submessage="Please check your inbox and spam folder. The link will expire in 15 minutes."
          showEmailIcon={true}
          actionText="Back to Login"
          actionHref="/auth/login"
          secondaryActionText="Resend Email"
          secondaryActionHref="/auth/forgot-password"
          autoRedirect={true}
          redirectDelay={5000}
          redirectMessage="Redirecting to login in {countdown} seconds..."
        />
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Forgot your password?"
      subtitle="Enter your email address and we'll send you a reset link"
      showBackToLogin={true}
      isCustomerPortal={false}
    >

      {error && (
        <StatusMessage
          type="error"
          message={error}
          className="mb-4"
          dismissible={true}
          onDismiss={() => setError('')}
        />
      )}

      {message && hasMounted && !submitted && (
        <StatusMessage
          type="success"
          message={message}
          className="mb-4"
        />
      )}

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Email address
          </label>
          <div className="mt-1">
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
              placeholder="Enter your email address"
            />
          </div>
        </div>

        <div>
          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sending...
              </>
            ) : (
              'Send reset link'
            )}
          </button>
        </div>
      </form>
    </AuthLayout>
  );
}
