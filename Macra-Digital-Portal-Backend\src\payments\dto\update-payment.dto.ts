import { PartialType } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsDateString, IsString, IsUUID } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreatePaymentDto } from './create-payment.dto';
import { PaymentStatus } from '../entities/payment.entity';

export class UpdatePaymentDto extends PartialType(CreatePaymentDto) {
  @ApiPropertyOptional({
    description: 'Payment status',
    enum: PaymentStatus,
    example: PaymentStatus.PAID
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({
    description: 'Date when payment was made',
    example: '2024-01-15'
  })
  @IsOptional()
  @IsDateString()
  paid_date?: string;

  @ApiPropertyOptional({
    description: 'Transaction reference number',
    example: 'TXN-123456789'
  })
  @IsOptional()
  @IsString()
  transaction_reference?: string;

  @ApiPropertyOptional({
    description: 'User ID who updated the payment',
    example: 'admin-uuid-here'
  })
  @IsOptional()
  @IsUUID()
  updated_by?: string;
}
