import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>reateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToOne, JoinColumn, BeforeInsert } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { User } from "./user.entity";
import { Applications } from "./applications.entity";
import { IsUUID } from "class-validator";
import { TypeApprovedManufacturer } from "./type_approved_manufacturer.entity";

@Entity('type_approved_devices')
export class TypeApprovedDevice {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  device_id: string;

  //This can be filled if the device is being added as part of a successful application
  @Column({ type: 'uuid', nullable: true })
  @IsUUID()
  application_id?: string;

  @Column({ type: 'uuid' })
  manufacturer_id: string;

  @Column({ type: 'varchar', length: 255 })
  device_type: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  model_name: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  device_serial_number: string;

  @Column({ type: 'varchar', length: 255 })
  device_manufacturer: string;

  @Column({ type: 'varchar', length: 255 })
  device_approval_number: string;

  @Column({ type: 'varchar', length: 255 })
  device_approval_date: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applications, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'application_id' })
  application?: Applications;

  @ManyToOne(() => TypeApprovedManufacturer, { nullable: false })
  @JoinColumn({ name: 'manufacturer_id' })
  manufacturer: TypeApprovedManufacturer;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.device_id) {
      this.device_id = uuidv4();
    }
  }
}
