"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const notifications_entity_1 = require("../entities/notifications.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
let NotificationsService = class NotificationsService {
    notificationsRepository;
    constructor(notificationsRepository) {
        this.notificationsRepository = notificationsRepository;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'sent_at', 'priority', 'status', 'type'],
        nullSort: 'last',
        defaultSortBy: [['created_at', 'DESC']],
        searchableColumns: ['subject', 'message', 'recipient_email', 'recipient_phone'],
        filterableColumns: {
            type: true,
            status: true,
            priority: true,
            recipient_type: true,
            recipient_id: true,
            entity_type: true,
            entity_id: true,
            is_read: true,
            created_by: true,
        },
        defaultLimit: 20,
        maxLimit: 100,
    };
    async create(createNotificationDto, createdBy) {
        const notification = this.notificationsRepository.create({
            ...createNotificationDto,
            created_by: createdBy,
        });
        return this.notificationsRepository.save(notification);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.notificationsRepository, this.paginateConfig);
    }
    async findByRecipient(recipientId, query) {
        const config = {
            ...this.paginateConfig,
            where: { recipient_id: recipientId },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.notificationsRepository, config);
    }
    async findByType(type, query) {
        const config = {
            ...this.paginateConfig,
            where: { type },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.notificationsRepository, config);
    }
    async findByStatus(status, query) {
        const config = {
            ...this.paginateConfig,
            where: { status },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.notificationsRepository, config);
    }
    async findByEntity(entityType, entityId, query) {
        const config = {
            ...this.paginateConfig,
            where: { entity_type: entityType, entity_id: entityId },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.notificationsRepository, config);
    }
    async findOne(id) {
        const notification = await this.notificationsRepository.findOne({
            where: { notification_id: id },
            relations: ['recipient', 'creator', 'updater'],
        });
        if (!notification) {
            throw new common_1.NotFoundException(`Notification with ID ${id} not found`);
        }
        return notification;
    }
    async update(id, updateNotificationDto, updatedBy) {
        const notification = await this.findOne(id);
        Object.assign(notification, updateNotificationDto, { updated_by: updatedBy });
        return this.notificationsRepository.save(notification);
    }
    async markAsRead(id, updatedBy) {
        const notification = await this.findOne(id);
        notification.is_read = true;
        notification.read_at = new Date();
        notification.updated_by = updatedBy;
        return this.notificationsRepository.save(notification);
    }
    async markAsSent(id, externalId) {
        const notification = await this.findOne(id);
        notification.status = notifications_entity_1.NotificationStatus.SENT;
        notification.sent_at = new Date();
        if (externalId) {
            notification.external_id = externalId;
        }
        return this.notificationsRepository.save(notification);
    }
    async markAsDelivered(id) {
        const notification = await this.findOne(id);
        notification.status = notifications_entity_1.NotificationStatus.DELIVERED;
        notification.delivered_at = new Date();
        return this.notificationsRepository.save(notification);
    }
    async markAsFailed(id, errorMessage) {
        const notification = await this.findOne(id);
        notification.status = notifications_entity_1.NotificationStatus.FAILED;
        notification.error_message = errorMessage;
        notification.retry_count = (notification.retry_count || 0) + 1;
        return this.notificationsRepository.save(notification);
    }
    async remove(id) {
        const notification = await this.findOne(id);
        await this.notificationsRepository.softDelete(notification.notification_id);
    }
    async createEmailNotification(recipientId, recipientEmail, subject, message, htmlContent, entityType, entityId, createdBy) {
        const createDto = {
            type: notifications_entity_1.NotificationType.EMAIL,
            recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
            recipient_id: recipientId,
            recipient_email: recipientEmail,
            subject,
            message,
            html_content: htmlContent,
            entity_type: entityType,
            entity_id: entityId,
        };
        return this.create(createDto, createdBy || 'system');
    }
    async createSmsNotification(recipientId, recipientPhone, subject, message, entityType, entityId, createdBy) {
        const createDto = {
            type: notifications_entity_1.NotificationType.SMS,
            recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
            recipient_id: recipientId,
            recipient_phone: recipientPhone,
            subject,
            message,
            entity_type: entityType,
            entity_id: entityId,
        };
        return this.create(createDto, createdBy || 'system');
    }
    async createInAppNotification(recipientId, subject, message, entityType, entityId, actionUrl, createdBy) {
        const createDto = {
            type: notifications_entity_1.NotificationType.IN_APP,
            recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
            recipient_id: recipientId,
            subject,
            message,
            entity_type: entityType,
            entity_id: entityId,
            action_url: actionUrl,
        };
        return this.create(createDto, createdBy || 'system');
    }
    async getStats() {
        const totalNotifications = await this.notificationsRepository.count();
        const sentNotifications = await this.notificationsRepository.count({
            where: { status: notifications_entity_1.NotificationStatus.SENT }
        });
        const failedNotifications = await this.notificationsRepository.count({
            where: { status: notifications_entity_1.NotificationStatus.FAILED }
        });
        const pendingNotifications = await this.notificationsRepository.count({
            where: { status: notifications_entity_1.NotificationStatus.PENDING }
        });
        return {
            total: totalNotifications,
            sent: sentNotifications,
            failed: failedNotifications,
            pending: pendingNotifications,
            success_rate: totalNotifications > 0 ? (sentNotifications / totalNotifications) * 100 : 0
        };
    }
};
exports.NotificationsService = NotificationsService;
exports.NotificationsService = NotificationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(notifications_entity_1.Notifications)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], NotificationsService);
//# sourceMappingURL=notifications.service.js.map