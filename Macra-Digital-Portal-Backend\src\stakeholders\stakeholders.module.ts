import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ShareholderDetails } from 'src/entities/shareholder-details.entity';
import { Stakeholder } from 'src/entities/stakeholders.entity';
import { StakeholdersController } from './stakeholders.controller';
import { StakeholdersService } from './stakeholders.service';

@Module({
    imports: [TypeOrmModule.forFeature([Stakeholder, ShareholderDetails])],
    controllers: [StakeholdersController],
    providers: [StakeholdersService],
    exports: [StakeholdersService],
})
export class StakeholdersModule {}
