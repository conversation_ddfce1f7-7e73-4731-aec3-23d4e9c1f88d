'use client';

export default function TestLoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-green-600 mb-4">
          ✅ Customer Test Login Page Works!
        </h1>
        <p className="text-gray-600">
          If you can see this page, customer portal routing is working correctly.
        </p>
        <div className="mt-4 space-x-4">
          <a href="/customer/auth/login" className="text-blue-600 hover:underline">
            Try customer login page
          </a>
          <a href="/customer/auth/signup" className="text-green-600 hover:underline">
            Try customer signup page
          </a>
        </div>
        <div className="mt-2 text-sm text-gray-500">
          Customer Portal Test Environment
        </div>
      </div>
    </div>
  );
}