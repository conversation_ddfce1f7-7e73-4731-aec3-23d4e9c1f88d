import { PaymentsService } from '../payments.service';
import { PaymentType } from '../entities/payment.entity';
export declare class PaymentExamples {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    createApplicationPayment(applicationId: string, userId: string, createdBy: string): Promise<import("../entities/payment.entity").Payment>;
    createLicenseRenewalPayment(licenseId: string, userId: string, createdBy: string): Promise<import("../entities/payment.entity").Payment>;
    createProcurementPayment(procurementId: string, userId: string, createdBy: string, amount: number): Promise<import("../entities/payment.entity").Payment>;
    createInspectionPayment(inspectionId: string, userId: string, createdBy: string): Promise<import("../entities/payment.entity").Payment>;
    getApplicationPayments(applicationId: string): Promise<import("../payments.service").PaymentQueryResult>;
    getLicensePayments(licenseId: string): Promise<import("../payments.service").PaymentQueryResult>;
    createPaymentForAnyEntity(entityType: string, entityId: string, userId: string, createdBy: string, paymentType: PaymentType, amount: number, description: string): Promise<import("../entities/payment.entity").Payment>;
    handleApplicationSubmission(applicationId: string, userId: string, adminId: string): Promise<import("../entities/payment.entity").Payment>;
    handleLicenseRenewal(licenseId: string, userId: string, adminId: string): Promise<{
        newPayment: import("../entities/payment.entity").Payment;
        paymentHistory: import("../payments.service").PaymentQueryResult;
    }>;
}
export declare class ApplicationService {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    submitApplication(applicationData: any, userId: string): Promise<{
        application: any;
        payment: import("../entities/payment.entity").Payment;
    }>;
}
