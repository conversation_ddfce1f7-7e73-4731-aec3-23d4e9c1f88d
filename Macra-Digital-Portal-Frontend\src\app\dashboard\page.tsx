'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

import { dashboardService, DashboardOverview, RecentApplication, RecentActivity } from '@/services/dashboardService';
import '@/styles/dashboard.css';

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [statsLoading, setStatsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<DashboardOverview | null>(null);
  const [recentApplications, setRecentApplications] = useState<RecentApplication[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [isMounted, setIsMounted] = useState(false);
  const [error, setError] = useState<string>('');

  // Helper function to calculate pending applications
  const getPendingApplicationsCount = () => {
    if (!dashboardData?.applications) return 0;
    return dashboardData.applications.pending || 0;
  };

  // Helper function to get new submissions count
  const getNewSubmissionsCount = () => {
    if (!dashboardData?.applications) return 0;
    return dashboardData.applications.submitted || 0;
  };

  // Set mounted state to prevent hydration errors
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Only run on client side to prevent hydration errors
    if (typeof window === 'undefined') return;

    // Listen for tab changes from header
    const handleTabChange = (event: CustomEvent) => {
      setActiveTab(event.detail.tab);
    };

    window.addEventListener('tabChange', handleTabChange as EventListener);

    return () => {
      window.removeEventListener('tabChange', handleTabChange as EventListener);
    };
  }, []);

  useEffect(() => {
    // Fetch dashboard data
    const fetchDashboardData = async () => {
      try {
        setStatsLoading(true);
        setError('');

        // Fetch overview data and recent items in parallel
        const [overview, applications, activities] = await Promise.all([
          dashboardService.getOverview().catch(() => null),
          dashboardService.getRecentApplications().catch(() => []),
          dashboardService.getRecentActivities().catch(() => [])
        ]);

        setDashboardData(overview);
        setRecentApplications(applications);
        setRecentActivities(activities);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        setError('Failed to load dashboard data. Please try refreshing the page.');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Don't render anything until mounted to prevent hydration errors
  if (!isMounted) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="ri-error-warning-line text-red-400"></i>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error Loading Dashboard</h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={() => window.location.reload()}
                    className="bg-red-100 dark:bg-red-800 px-3 py-2 rounded-md text-sm font-medium text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-700"
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Tab content sections */}

        {/* Overview Tab Content */}
        <div className={`tab-content ${activeTab === 'overview' ? '' : 'hidden'}`}>
          {/* Page header */}
          <div className="mb-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Dashboard Overview</h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Comprehensive view of your licenses, spectrum, users, and financial activities.
                </p>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto">
                <div className="flex space-x-3 place-content-start">
                  <div className="relative">
                    <button
                      type="button"
                      className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap w-full"
                    >
                      <div className="w-4 h-4 flex items-center justify-center mr-2">
                        <i className="ri-calendar-line"></i>
                      </div>
                      May 7, 2025
                    </button>
                  </div>
                  <div className="relative">
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Key Metrics Section */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div className="p-6">
              <h3 className="text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4">Key Metrics</h3>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                {/* License Metrics */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4">
                  <div className="flex place-content-start items-center">
                    <div className="flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3">
                      <div className="w-6 h-6 flex items-center justify-center text-primary">
                        <i className="ri-key-line"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex flex-col">
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Licenses</h4>
                      <div className="mt-1 flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                          {!isMounted || statsLoading ? (
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded"></div>
                          ) : (
                            dashboardData?.licenses?.total || 0
                          )}
                        </div>
                      </div>
                      <div className="mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          <span className="text-yellow-600">{!isMounted || statsLoading ? '...' : dashboardData?.licenses?.expiringSoon || 0}</span> expiring soon
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Link href="/dashboard/licenses" className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                      View More
                    </Link>
                  </div>
                </div>

                {/* User Metrics */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4">
                  <div className="flex place-content-start items-center">
                    <div className="flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3">
                      <div className="w-6 h-6 flex items-center justify-center text-primary">
                        <i className="ri-user-line"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex flex-col">
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Users</h4>
                      <div className="mt-1 flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                          {!isMounted || statsLoading ? (
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded"></div>
                          ) : (
                            dashboardData?.users?.total || 0
                          )}
                        </div>
                      </div>
                      <div className="mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          <span className="text-green-600">{!isMounted || statsLoading ? '...' : dashboardData?.users?.newThisMonth || 0}</span> new this month
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Link href="/users" className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                      View More
                    </Link>
                  </div>
                </div>

                {/* Financial Metrics */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4">
                  <div className="flex place-content-start items-center">
                    <div className="flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3">
                      <div className="w-6 h-6 flex items-center justify-center text-primary">
                        <i className="ri-money-dollar-circle-line"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex flex-col">
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Revenue (<strong>MWK</strong>)</h4>
                      <div className="mt-1 flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                          {!isMounted || statsLoading ? (
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-16 rounded"></div>
                          ) : (
                            `${((dashboardData?.financial?.totalRevenue || 0) / 1000000).toFixed(1)}M`
                          )}
                        </div>
                      </div>
                      <div className="mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          <span className="text-green-600">{!isMounted || statsLoading ? '...' : `${((dashboardData?.financial?.thisMonth || 0) / 1000000).toFixed(1)}M`}</span> this month
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Link href="/dashboard/financial" className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                      View More
                    </Link>
                  </div>
                </div>

                {/* Pending Applications Metrics */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4">
                  <div className="flex place-content-start items-center">
                    <div className="flex-shrink-0 bg-orange-100 dark:bg-orange-900 rounded-md p-3">
                      <div className="w-6 h-6 flex items-center justify-center text-primary">
                        <i className="ri-file-list-3-line"></i>
                      </div>
                    </div>
                    <div className="ml-4 flex flex-col">
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Applications</h4>
                      <div className="mt-1 flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100" title={!isMounted || statsLoading ? 'Loading...' : `Submitted: ${dashboardData?.applications?.submitted || 0}, Under Review: ${dashboardData?.applications?.under_review || 0}, Evaluation: ${dashboardData?.applications?.evaluation || 0}`}>
                          {!isMounted || statsLoading ? (
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-8 rounded"></div>
                          ) : (
                            getPendingApplicationsCount()
                          )}
                        </div>
                      </div>
                      <div className="mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          <span className="text-orange-600">{!isMounted || statsLoading ? '...' : getNewSubmissionsCount()}</span> new submissions
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Link href="/applications" className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                      View Applications
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Licenses Tab Content */}
        <div className={`tab-content ${activeTab === 'licenses' ? '' : 'hidden'}`}>
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">License Management</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Manage and monitor all telecommunications licenses.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <i className="ri-key-line text-xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Licenses</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">1,482</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <i className="ri-check-line text-xl text-green-600 dark:text-green-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">1,425</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <i className="ri-time-line text-xl text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Expiring Soon</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">57</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                  <i className="ri-close-line text-xl text-red-600 dark:text-red-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Expired</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">12</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Recent License Applications</h3>
                <Link href="/dashboard/licenses" className="text-sm text-primary hover:text-primary">
                  View all →
                </Link>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">License ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Company</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Expiry</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {statsLoading ? (
                      // Loading skeleton
                      Array.from({ length: 3 }).map((_, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-24 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-32 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-16 rounded-full"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-20 rounded"></div>
                          </td>
                        </tr>
                      ))
                    ) : recentApplications.length > 0 ? (
                      recentApplications.map((application) => (
                        <tr key={application.application_id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                            {application.application_number}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {application.applicant?.company_name || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {application.license_category?.category_name || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              application.status === 'approved' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                              application.status === 'submitted' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                              application.status === 'under_review' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                              application.status === 'evaluation' ? 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200' :
                              application.status === 'rejected' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
                              'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'
                            }`}>
                              {application.status.charAt(0).toUpperCase() + application.status.slice(1).replace('_', ' ')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {new Date(application.created_at).toLocaleDateString()}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                          No recent applications found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Users Tab Content */}
        <div className={`tab-content ${activeTab === 'users' ? '' : 'hidden'}`}>
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">User Management</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Manage system users and their access permissions.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <i className="ri-user-line text-xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</p>
                  <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {!isMounted || statsLoading ? (
                      <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded"></div>
                    ) : (
                      dashboardData?.users?.total || 0
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <i className="ri-user-check-line text-xl text-green-600 dark:text-green-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Users</p>
                  <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {!isMounted || statsLoading ? (
                      <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded"></div>
                    ) : (
                      dashboardData?.users?.active || 0
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <i className="ri-user-add-line text-xl text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">New This Month</p>
                  <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {!isMounted || statsLoading ? (
                      <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded"></div>
                    ) : (
                      dashboardData?.users?.newThisMonth || 0
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                  <i className="ri-shield-user-line text-xl text-red-600 dark:text-red-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Administrators</p>
                  <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {!isMounted || statsLoading ? (
                      <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-8 rounded"></div>
                    ) : (
                      dashboardData?.users?.administrators || 0
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Recent User Activity</h3>
                <Link href="/users" className="text-sm text-primary hover:text-primary">
                  View all →
                </Link>
              </div>
              <div className="space-y-4">
                {statsLoading ? (
                  // Loading skeleton
                  Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="animate-pulse bg-gray-300 dark:bg-gray-600 w-8 h-8 rounded-full"></div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-48 rounded mb-2"></div>
                        <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-3 w-20 rounded"></div>
                      </div>
                    </div>
                  ))
                ) : recentActivities.length > 0 ? (
                  recentActivities.map((activity) => (
                    <div key={activity.audit_id} className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className={`w-8 h-8 flex items-center justify-center rounded-full ${
                          activity.action === 'create' ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400' :
                          activity.action === 'update' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' :
                          activity.action === 'delete' ? 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400' :
                          'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'
                        }`}>
                          <i className={`${
                            activity.action === 'create' ? 'ri-add-line' :
                            activity.action === 'update' ? 'ri-edit-line' :
                            activity.action === 'delete' ? 'ri-delete-bin-line' :
                            'ri-eye-line'
                          }`}></i>
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {activity.description || `${activity.action} ${activity.resource_type}`}
                          {activity.user && ` by ${activity.user.first_name} ${activity.user.last_name}`}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date(activity.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                    No recent activities found
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Transactions Tab Content */}
        <div className={`tab-content ${activeTab === 'transactions' ? '' : 'hidden'}`}>
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Financial Transactions</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Monitor payments, invoices, and financial activities.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <i className="ri-money-dollar-circle-line text-xl text-green-600 dark:text-green-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue (MWK)</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">115.4M</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <i className="ri-exchange-line text-xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">This Month (MWK)</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">8.7M</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <i className="ri-time-line text-xl text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">23</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <i className="ri-file-list-line text-xl text-purple-600 dark:text-purple-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Transactions</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">4,892</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Recent Transactions</h3>
                <Link href="/dashboard/financial" className="text-sm text-primary hover:text-primary">
                  View all →
                </Link>
              </div>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                      <i className="ri-check-line"></i>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Payment of MWK 2.450M received from Acme Corp</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">3 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                      <i className="ri-file-text-line"></i>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Invoice INV-2025-0234 generated</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">5 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Spectrum Tab Content */}
        <div className={`tab-content ${activeTab === 'spectrum' ? '' : 'hidden'}`}>
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Spectrum Management</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Monitor frequency allocations and spectrum usage.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <i className="ri-radio-line text-xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Allocations</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">1,248</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <i className="ri-signal-tower-line text-xl text-green-600 dark:text-green-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">1,156</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <i className="ri-bar-chart-line text-xl text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Utilization</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">78%</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                  <i className="ri-alert-line text-xl text-red-600 dark:text-red-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Interference Issues</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">5</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Spectrum Bands Overview</h3>
                <Link href="/dashboard/spectrum" className="text-sm text-primary hover:text-primary">
                  View all →
                </Link>
              </div>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">VHF Band</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">30-300 MHz</p>
                  <div className="mt-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Utilization</span>
                      <span className="text-gray-900 dark:text-gray-100">85%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                      <div className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full w-[85%]"></div>
                    </div>
                  </div>
                </div>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">UHF Band</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">300-3000 MHz</p>
                  <div className="mt-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Utilization</span>
                      <span className="text-gray-900 dark:text-gray-100">72%</span>
                    </div>
                    <div className="progress-container">
                      <div className="progress-fill progress-green progress-bar-72"></div>
                    </div>
                  </div>
                </div>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">SHF Band</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">3-30 GHz</p>
                  <div className="mt-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Utilization</span>
                      <span className="text-gray-900 dark:text-gray-100">45%</span>
                    </div>
                    <div className="progress-container">
                      <div className="progress-fill progress-yellow progress-bar-45"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compliance Tab Content */}
        <div className={`tab-content ${activeTab === 'compliance' ? '' : 'hidden'}`}>
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Compliance Overview</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Monitor regulatory compliance and audit information.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <i className="ri-shield-check-line text-xl text-green-600 dark:text-green-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Compliance Rate</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">92.1%</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <i className="ri-file-shield-line text-xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Audits Completed</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">156</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                  <i className="ri-alert-line text-xl text-red-600 dark:text-red-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Open Issues</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">8</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <i className="ri-time-line text-xl text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Reviews</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">23</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Recent Compliance Activities</h3>
                <Link href="/dashboard/audit" className="text-sm text-primary hover:text-primary">
                  View all →
                </Link>
              </div>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                      <i className="ri-check-line"></i>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Compliance audit completed for Global Tech Inc.</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400">
                      <i className="ri-alert-line"></i>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Non-compliance issue detected for Quantum Solutions</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">4 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}