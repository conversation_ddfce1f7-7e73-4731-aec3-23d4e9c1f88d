import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LegalHistoryController } from './legal-history.controller';
import { LegalHistoryService } from './legal-history.service';
import { LegalHistory } from '../entities/legal-history.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LegalHistory])],
  controllers: [LegalHistoryController],
  providers: [LegalHistoryService],
  exports: [LegalHistoryService],
})
export class LegalHistoryModule {}
