import { Repository } from 'typeorm';
import { Payment, PaymentStatus, PaymentType } from './entities/payment.entity';
import { ProofOfPayment, ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
import { User } from '../entities/user.entity';
import { CreateProofOfPaymentDto, UpdateProofOfPaymentStatusDto } from './dto/create-proof-of-payment.dto';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
export interface PaymentFilters {
    status?: PaymentStatus;
    paymentType?: PaymentType;
    dateRange?: 'last-30' | 'last-90' | 'last-year';
    search?: string;
    userId?: string;
}
export interface PaginationOptions {
    page?: number;
    limit?: number;
}
export interface PaymentQueryResult {
    payments: Payment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface ProofOfPaymentQueryResult {
    proofOfPayments: ProofOfPayment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface FileUploadResult {
    filename: string;
    originalname: string;
    path: string;
    size: number;
    mimetype: string;
}
export declare class PaymentsService {
    private paymentsRepository;
    private proofOfPaymentsRepository;
    private usersRepository;
    constructor(paymentsRepository: Repository<Payment>, proofOfPaymentsRepository: Repository<ProofOfPayment>, usersRepository: Repository<User>);
    createPayment(createPaymentDto: CreatePaymentDto): Promise<Payment>;
    getPayments(filters?: PaymentFilters, pagination?: PaginationOptions): Promise<PaymentQueryResult>;
    getPaymentById(paymentId: string): Promise<Payment>;
    updatePayment(paymentId: string, updatePaymentDto: UpdatePaymentDto): Promise<Payment>;
    deletePayment(paymentId: string): Promise<void>;
    uploadProofOfPayment(createProofOfPaymentDto: CreateProofOfPaymentDto, file: Express.Multer.File, userId: string): Promise<any>;
    getProofOfPayments(filters?: {
        status?: ProofOfPaymentStatus;
        userId?: string;
        paymentId?: string;
    }, pagination?: PaginationOptions): Promise<ProofOfPaymentQueryResult>;
    getProofOfPaymentById(proofId: string): Promise<ProofOfPayment>;
    updateProofOfPaymentStatus(proofId: string, updateStatusDto: UpdateProofOfPaymentStatusDto, reviewerId: string): Promise<ProofOfPayment>;
    downloadProofOfPayment(proofId: string, userId: string): Promise<{
        filePath: string;
        filename: string;
    }>;
    getPaymentStatistics(userId?: string): Promise<{
        totalPayments: number;
        paidPayments: number;
        pendingPayments: number;
        overduePayments: number;
        totalAmount: number;
        paidAmount: number;
        pendingAmount: number;
    }>;
    markOverduePayments(): Promise<void>;
}
