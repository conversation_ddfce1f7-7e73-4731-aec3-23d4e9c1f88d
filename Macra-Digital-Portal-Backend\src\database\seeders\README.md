# MACRA License System Seeders

This directory contains seeders for populating the MACRA Digital Portal database with license types and categories.

## Overview

The seeders create a comprehensive license management system with:
- **5 License Types** covering all MACRA regulatory areas
- **21 License Categories** with specific fees and authorizations

## License Types Created

1. **Telecommunications** (5 years validity)
   - Mobile networks, ISPs, fixed networks, satellite services

2. **Postal Services** (3 years validity)
   - Courier services, freight forwarding, mail delivery

3. **Standards Compliance** (2 years validity)
   - Type approval, equipment certification, technical standards

4. **Broadcasting** (5 years validity)
   - Radio, television, community, and campus broadcasting

5. **Spectrum Management** (10 years validity)
   - Spectrum assignment, authorization, temporary permits

## License Categories Created

### Telecommunications (5 categories)
- Mobile Network Operator (MNO) - K500,000
- Internet Service Provider (ISP) - K250,000
- Fixed Network Operator - K300,000
- Virtual Network Operator (MVNO) - K150,000
- Satellite Communication Services - K400,000

### Postal Services (5 categories)
- International Commercial Courier - K100,000
- Domestic Commercial Courier - K50,000
- Intra-City Commercial - K25,000
- District Commercial - K30,000
- Freight Forwarders - K75,000

### Standards Compliance (4 categories)
- Type Approval Certificate - K15,000
- Short Code Allocation - K5,000
- Equipment Certification - K20,000
- Technical Standards Compliance - K10,000

### Broadcasting (4 categories)
- Radio Broadcasting License - K200,000
- Television Broadcasting License - K350,000
- Community Radio License - K50,000
- Campus Radio License - K25,000

### Spectrum Management (3 categories)
- Spectrum Assignment - K1,000,000
- Spectrum Authorization - K500,000
- Temporary Spectrum Permit - K50,000

## Usage

### Running the Seeders

```bash
# Run license types and categories seeders
npm run seed:licenses

# Alternative method using typeorm-extension
npm run seed:licenses:ext
```

### Manual Execution

```bash
# Navigate to the backend directory
cd Macra-Digital-Portal-Backend

# Run the seeder directly
ts-node src/database/seeders/run-seeders.ts

# Or with extension method
ts-node src/database/seeders/run-seeders.ts --extension
```

## Files Structure

```
src/database/seeders/
├── README.md                    # This file
├── main.seeder.ts              # Main seeder orchestrator
├── license-types.seeder.ts     # License types seeder
├── license-categories.seeder.ts # License categories seeder
├── seeder.config.ts            # Seeder configuration
└── run-seeders.ts              # CLI script to run seeders
```

## Prerequisites

1. **Database Setup**: Ensure your database is created and accessible
2. **Environment Variables**: Configure your `.env` file with database credentials
3. **Migrations**: Run database migrations before seeding
4. **Dependencies**: Install required packages

```bash
# Install dependencies
npm install

# Run migrations (if not already done)
npm run typeorm:migration:run
```

## Environment Variables

Ensure these variables are set in your `.env` file:

```env
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_DATABASE=macra_portal
```

## Safety Features

- **Duplicate Prevention**: Seeders check for existing data before inserting
- **Error Handling**: Comprehensive error handling and logging
- **Transaction Safety**: Each seeder runs in a safe transaction context
- **Dependency Management**: License categories seeder waits for license types

## Customization

### Adding New License Types

Edit `license-types.seeder.ts` and add new entries to the `licenseTypes` array:

```typescript
{
  name: 'New License Type',
  description: 'Description of the new license type',
  validity: 5, // years
}
```

### Adding New License Categories

Edit `license-categories.seeder.ts` and add new entries to the `categories` array:

```typescript
{
  name: 'New Category',
  description: 'Description of the new category',
  fee: '100000.00',
  authorizes: 'What this license authorizes',
  license_type_id: licenseType.license_type_id,
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check your database credentials in `.env`
   - Ensure database server is running
   - Verify database exists

2. **License Types Not Found**
   - Run license types seeder first
   - Check if license types were created successfully

3. **Permission Errors**
   - Ensure database user has CREATE, INSERT, SELECT permissions
   - Check file permissions for seeder files

### Logs and Debugging

The seeders provide detailed console output:
- ✅ Success messages for each created item
- ❌ Error messages with details
- 📊 Summary statistics at completion

## Integration with Application

After running the seeders, the license management system will have:
- Populated dropdown menus in the frontend
- Proper license type and category relationships
- Realistic fee structures for applications
- Complete regulatory framework data

The seeded data integrates seamlessly with:
- Application management system
- License category filtering
- Fee calculation system
- Regulatory compliance tracking
