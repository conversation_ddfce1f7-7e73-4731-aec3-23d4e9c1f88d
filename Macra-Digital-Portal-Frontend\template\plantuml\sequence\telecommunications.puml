@startuml
actor Applicant
participant "System" as System
participant "<PERSON>ra Staff - DOT" as DOT
participant "Macra Staff - DDSM" as DDSM
participant "Macra Staff - Spectrum Planning" as SpectrumPlanning
participant "<PERSON>ra Staff - DG" as DG


title TeleCommunications Application Submission
Applicant -> System: Accesses online portal
Applicant -> System: Submits request

alt Submission valid?
    System -> System: Save submission
    System -> DOT: Routes submission to DOT

    DOT -> DOT: Reviews submission
    alt DOT approves?
        DOT -> DDSM: Forward submission
        DDSM -> DDSM: Reviews submission
        
        alt DDSM approves?
            DDSM -> SpectrumPlanning: Forward submission
            SpectrumPlanning -> SpectrumPlanning: Reviews submission
            
            alt Spectrum Planning approves?
                System -> System: Auto-generate invoice
                System -> Applicant: Send invoice via email/portal
                
                Applicant -> System: Make payment via integrated finance module
                
                alt Payment validated?
                    System -> System: Update status to "Approved"
                    System -> Applicant: Notify applicant via email/SMS/portal
                    
                    System -> DG: Request DG to digitally sign license
                    DG -> DG: Digitally signs license
                    
                    System -> Applicant: Dispatch license via email
                    System -> System: Update WebCP backend for license lifecycle
                    
                    Applicant -> System: View final status on portal
                    
                else Payment failed
                    System -> Applicant: Notify payment failure
                    Applicant -> System: Retry payment
                end
                
            else Spectrum Planning rejects
                System -> System: Update status to "Rejected"
                System -> Applicant: Notify applicant via email/SMS/portal
            end
            
        else DDSM rejects
            System -> System: Update status to "Rejected"
            System -> Applicant: Notify applicant via email/SMS/portal
        end
        
    else DOT rejects
        System -> System: Update status to "Rejected"
        System -> Applicant: Notify applicant via email/SMS/portal
    end
    
else Submission invalid
    System -> Applicant: Notify invalid submission
    Applicant -> System: Revise and resubmit
end
@enduml