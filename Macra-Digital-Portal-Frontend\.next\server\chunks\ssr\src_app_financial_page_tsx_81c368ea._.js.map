{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/financial/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\n\r\nexport default function FinancialPage() {\r\n  const { user } = useAuth();\r\n  const [transactions, setTransactions] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [stats, setStats] = useState({\r\n    totalRevenue: 0,\r\n    monthlyRevenue: 0,\r\n    pendingPayments: 0,\r\n    overduePayments: 0,\r\n  });\r\n\r\n  const isAdmin = user?.role?.name === 'ADMINISTRATOR';\r\n  const isAccountant = user?.role?.name === 'ACCOUNTANT';\r\n  const hasAccess = isAdmin || isAccountant;\r\n\r\n  // Redirect users without access\r\n  useEffect(() => {\r\n    if (!loading && !hasAccess) {\r\n      window.location.href = '/dashboard';\r\n    }\r\n  }, [hasAccess, loading]);\r\n\r\n  useEffect(() => {\r\n    // Simulate loading financial data\r\n    setTimeout(() => {\r\n      setStats({\r\n        totalRevenue: 2450000,\r\n        monthlyRevenue: 185000,\r\n        pendingPayments: 45000,\r\n        overduePayments: 12000,\r\n      });\r\n\r\n      setTransactions([\r\n        {\r\n          id: 'TXN-2025-001',\r\n          company: 'Global Technologies Inc.',\r\n          amount: 50000,\r\n          type: 'License Fee',\r\n          status: 'Completed',\r\n          date: '2025-06-05',\r\n          method: 'Bank Transfer',\r\n        },\r\n        {\r\n          id: 'TXN-2025-002',\r\n          company: 'Quantum Solutions Ltd.',\r\n          amount: 25000,\r\n          type: 'Renewal Fee',\r\n          status: 'Pending',\r\n          date: '2025-06-04',\r\n          method: 'Credit Card',\r\n        },\r\n        {\r\n          id: 'TXN-2025-003',\r\n          company: 'Horizon Dynamics',\r\n          amount: 15000,\r\n          type: 'Application Fee',\r\n          status: 'Failed',\r\n          date: '2025-06-03',\r\n          method: 'Bank Transfer',\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n    }, 1000);\r\n  }, []);\r\n\r\n  const formatCurrency = (amount: number) => {\r\n    return new Intl.NumberFormat('en-MW', {\r\n      style: 'currency',\r\n      currency: 'MWK',\r\n    }).format(amount);\r\n  };\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    const statusClasses = {\r\n      'Completed': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',\r\n      'Pending': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',\r\n      'Failed': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',\r\n      'Refunded': 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',\r\n    };\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || statusClasses['Pending']}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  if (!hasAccess) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"text-center\">\r\n          <i className=\"ri-error-warning-line text-4xl text-red-600 dark:text-red-500 mb-4\"></i>\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">Access Denied</h3>\r\n          <p className=\"text-gray-500 dark:text-gray-400\">You don't have permission to view financial data.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 dark:border-red-500\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-6 bg-gray-50 dark:bg-gray-900 min-h-screen\">\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Financial Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor revenue, transactions, and financial performance\r\n            </p>\r\n          </div>\r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\r\n            >\r\n              <i className=\"ri-download-line mr-2\"></i>\r\n              Export Report\r\n            </button>\r\n            {isAdmin && (\r\n              <button\r\n                type=\"button\"\r\n                className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800\"\r\n              >\r\n                <i className=\"ri-add-line mr-2\"></i>\r\n                Manual Entry\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Financial Statistics */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n              <i className=\"ri-money-dollar-circle-line text-2xl text-green-600 dark:text-green-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Revenue</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.totalRevenue)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n              <i className=\"ri-calendar-line text-2xl text-blue-600 dark:text-blue-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Monthly Revenue</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.monthlyRevenue)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n              <i className=\"ri-time-line text-2xl text-yellow-600 dark:text-yellow-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Payments</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.pendingPayments)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n              <i className=\"ri-error-warning-line text-2xl text-red-600 dark:text-red-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Overdue Payments</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.overduePayments)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recent Transactions */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\r\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Transactions</h3>\r\n        </div>\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n              <tr>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Transaction\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Company\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Amount\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Type\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Method\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Status\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Date\r\n                </th>\r\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Actions\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {transactions.map((transaction: any) => (\r\n                <tr key={transaction.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{transaction.id}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">{transaction.company}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{formatCurrency(transaction.amount)}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">{transaction.type}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">{transaction.method}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {getStatusBadge(transaction.status)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                    {new Date(transaction.date).toLocaleDateString()}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    <div className=\"flex items-center justify-end space-x-2\">\r\n                      <button\r\n                        type=\"button\"\r\n                        title=\"View transaction\"\r\n                        className=\"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900\"\r\n                      >\r\n                        <i className=\"ri-eye-line\"></i>\r\n                      </button>\r\n                      {isAdmin && (\r\n                        <button\r\n                          type=\"button\"\r\n                          title=\"Edit transaction\"\r\n                          className=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900\"\r\n                        >\r\n                          <i className=\"ri-edit-line\"></i>\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,cAAc;QACd,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,UAAU,MAAM,MAAM,SAAS;IACrC,MAAM,eAAe,MAAM,MAAM,SAAS;IAC1C,MAAM,YAAY,WAAW;IAE7B,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,WAAW;YAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF,GAAG;QAAC;QAAW;KAAQ;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,WAAW;YACT,SAAS;gBACP,cAAc;gBACd,gBAAgB;gBAChB,iBAAiB;gBACjB,iBAAiB;YACnB;YAEA,gBAAgB;gBACd;oBACE,IAAI;oBACJ,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,MAAM;oBACN,QAAQ;gBACV;aACD;YACD,WAAW;QACb,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAgB;YACpB,aAAa;YACb,WAAW;YACX,UAAU;YACV,YAAY;QACd;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,CAAC,OAAqC,IAAI,aAAa,CAAC,UAAU,EAAE;sBAC1K;;;;;;IAGP;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;;;;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAE,WAAU;;;;;;wCAA4B;;;;;;;gCAG1C,yBACC,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAE,WAAU;;;;;;wCAAuB;;;;;;;;;;;;;;;;;;;;;;;;0BAS9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2D,eAAe,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;kCAK/G,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2D,eAAe,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;kCAKjH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2D,eAAe,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;kCAKlH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2D,eAAe,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAuD;;;;;;;;;;;kCAEvE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAqG;;;;;;;;;;;;;;;;;8CAKvH,8OAAC;oCAAM,WAAU;8CACd,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;4CAAwB,WAAU;;8DACjC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;kEAAwD,YAAY,EAAE;;;;;;;;;;;8DAEvF,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;kEAA4C,YAAY,OAAO;;;;;;;;;;;8DAEhF,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;kEAAwD,eAAe,YAAY,MAAM;;;;;;;;;;;8DAE1G,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;kEAA4C,YAAY,IAAI;;;;;;;;;;;8DAE7E,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;kEAA4C,YAAY,MAAM;;;;;;;;;;;8DAE/E,8OAAC;oDAAG,WAAU;8DACX,eAAe,YAAY,MAAM;;;;;;8DAEpC,8OAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;;;;;;8DAEhD,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,OAAM;gEACN,WAAU;0EAEV,cAAA,8OAAC;oEAAE,WAAU;;;;;;;;;;;4DAEd,yBACC,8OAAC;gEACC,MAAK;gEACL,OAAM;gEACN,WAAU;0EAEV,cAAA,8OAAC;oEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CArCd,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDvC", "debugId": null}}]}