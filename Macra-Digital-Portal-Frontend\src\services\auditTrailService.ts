import axios, { AxiosError } from 'axios';
import { createAuthenticatedAxios, getAuthToken } from '../lib/auth';
import { auditApiClient } from '../lib/apiClient';
import { PaginateQuery } from './userService';

// Cache for audit trail data
const auditTrailCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes for audit trails (they don't change frequently)

// Request deduplication for audit trails
const pendingRequests = new Map<string, Promise<any>>();

// Rate limiting for audit trail requests
const requestTimestamps = new Map<string, number[]>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const MAX_REQUESTS_PER_WINDOW = 10; // Max 10 requests per minute per endpoint

// Helper functions for caching and rate limiting
const getCacheKey = (endpoint: string, params?: any): string => {
  return `audit_trail_${endpoint}_${params ? JSON.stringify(params) : ''}`;
};

const isRateLimited = (endpoint: string): boolean => {
  const now = Date.now();
  const timestamps = requestTimestamps.get(endpoint) || [];

  // Remove old timestamps outside the window
  const validTimestamps = timestamps.filter(ts => now - ts < RATE_LIMIT_WINDOW);

  if (validTimestamps.length >= MAX_REQUESTS_PER_WINDOW) {
    console.warn(`Rate limit exceeded for audit trail endpoint: ${endpoint}`);
    return true;
  }

  // Add current timestamp and update
  validTimestamps.push(now);
  requestTimestamps.set(endpoint, validTimestamps);
  return false;
};

const getCachedData = (cacheKey: string): any | null => {
  const cached = auditTrailCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    console.log(`Cache hit for audit trail: ${cacheKey}`);
    return cached.data;
  }
  if (cached) {
    auditTrailCache.delete(cacheKey); // Remove expired cache
  }
  return null;
};

const setCachedData = (cacheKey: string, data: any, ttl: number = CACHE_TTL): void => {
  auditTrailCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
    ttl
  });
};

// Error handling utility
export class AuditTrailError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'AuditTrailError';
  }
}

// Handle API errors consistently
const handleApiError = (error: any): never => {
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    const message = error.response?.data?.message || error.message;
    const code = error.code;

    if (status === 401) {
      throw new AuditTrailError('Authentication required', 'UNAUTHORIZED', status);
    } else if (status === 403) {
      throw new AuditTrailError('Access denied', 'FORBIDDEN', status);
    } else if (status === 404) {
      throw new AuditTrailError('Audit trail not found', 'NOT_FOUND', status);
    } else if (status === 429) {
      throw new AuditTrailError('Too many requests', 'RATE_LIMITED', status);
    } else if (status && status >= 500) {
      throw new AuditTrailError('Server error occurred', 'SERVER_ERROR', status);
    } else if (code === 'ERR_NETWORK' || error.message === 'Network Error') {
      throw new AuditTrailError('Network error - please check your connection', 'NETWORK_ERROR');
    } else {
      throw new AuditTrailError(message || 'An unexpected error occurred', code, status, error.response?.data);
    }
  }

  throw new AuditTrailError(error.message || 'An unexpected error occurred');
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';


export interface AuditTrail {
  audit_id: string;
  action: string;
  module: string;
  status: string;
  resource_type: string;
  resource_id?: string;
  description?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  error_message?: string;
  user_id?: string;
  user?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  created_at: string;
}

export interface AuditTrailFilters {
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  action?: string;
  module?: string;
  status?: string;
  ipAddress?: string;
  resourceType?: string;
  resourceId?: string;
}

// Standard API response format (matches backend)
export interface StandardResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  meta?: any;
  timestamp: string;
  path: string;
  statusCode: number;
}

// Paginated response format
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}

export type AuditTrailResponse = PaginatedResponse<AuditTrail>;

export const auditTrailService = {

  async getAuditTrails(query: PaginateQuery & AuditTrailFilters): Promise<AuditTrailResponse> {
    const endpoint = 'audit_trails_list';
    const cacheKey = getCacheKey('list', query);

    try {
      // Check cache first (shorter TTL for list data)
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Check rate limiting
      if (isRateLimited(endpoint)) {
        throw new AuditTrailError(
          'Too many requests. Please wait before trying again.',
          'RATE_LIMITED',
          429
        );
      }

      // Check for pending request (deduplication)
      const requestKey = `${endpoint}_${cacheKey}`;
      if (pendingRequests.has(requestKey)) {
        console.log(`Deduplicating request for audit trails list`);
        return await pendingRequests.get(requestKey)!;
      }

      // Create new request
      const requestPromise = (async () => {
        const params = new URLSearchParams();

        // Add pagination parameters
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());

        // Add search parameters
        if (query.search) params.set('search', query.search);

        // Add sorting parameters
        if (query.sortBy && query.sortBy.length > 0) {
          query.sortBy.forEach(sort => params.append('sortBy', sort));
        }

        // Add searchBy parameters
        if (query.searchBy && query.searchBy.length > 0) {
          query.searchBy.forEach(search => params.append('searchBy', search));
        }

        // Add filter parameters
        if (query.filter) {
          Object.entries(query.filter).forEach(([key, value]) => {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(`filter.${key}`, v));
            } else {
              params.set(`filter.${key}`, value);
            }
          });
        }

        // Add audit trail specific filters (only if they have values)
        if (query.dateFrom) params.set('dateFrom', query.dateFrom);
        if (query.dateTo) params.set('dateTo', query.dateTo);
        if (query.userId) params.set('userId', query.userId);
        if (query.action) params.set('action', query.action);
        if (query.module) params.set('module', query.module);
        if (query.status) params.set('status', query.status);
        if (query.ipAddress) params.set('ipAddress', query.ipAddress);
        if (query.resourceType) params.set('resourceType', query.resourceType);
        if (query.resourceId) params.set('resourceId', query.resourceId);

        console.log('Audit trail request params:', params.toString());
        const response = await auditApiClient.get(`?${params.toString()}`);

        // Handle both standard response format and direct data format
        if (response.data && typeof response.data === 'object') {
          let result;
          if (response.data.success !== undefined) {
            console.log('Audit trail response:', response.data);
            // Standard response format
            result = response.data;
          } else if (response.data.data && response.data.meta) {
            // Direct paginated format
            result = response.data;
          } else {
            // Fallback: assume it's direct data
            result = response.data;
          }

          // Cache the result with shorter TTL for list data (2 minutes)
          setCachedData(cacheKey, result, 2 * 60 * 1000);
          return result;
        }

        // Fallback for unexpected response format
        throw new AuditTrailError('Invalid response format from server');
      })();

      // Store pending request
      pendingRequests.set(requestKey, requestPromise);

      try {
        const result = await requestPromise;
        return result;
      } finally {
        // Clean up pending request
        pendingRequests.delete(requestKey);
      }
    } catch (error) {
      console.error('Error fetching audit trails:', error);

      // Clean up pending request on error
      const requestKey = `${endpoint}_${cacheKey}`;
      pendingRequests.delete(requestKey);

      handleApiError(error);
    }
  },

  async getAuditTrail(id: string): Promise<AuditTrail> {
    const endpoint = `audit_trail_${id}`;
    const cacheKey = getCacheKey('single', { id });

    try {
      // Check cache first
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Check rate limiting
      if (isRateLimited(endpoint)) {
        throw new AuditTrailError(
          'Too many requests. Please wait before trying again.',
          'RATE_LIMITED',
          429
        );
      }

      // Check for pending request (deduplication)
      if (pendingRequests.has(endpoint)) {
        console.log(`Deduplicating request for audit trail: ${id}`);
        return await pendingRequests.get(endpoint)!;
      }

      // Create new request
      const requestPromise = (async () => {
        const response = await auditApiClient.get(`/${id}`);

        // Handle both standard response format and direct data format
        if (response.data && typeof response.data === 'object') {
          let result;
          if (response.data.success !== undefined) {
            // Standard response format
            result = response.data.data;
          } else {
            // Direct data format (backward compatibility)
            result = response.data;
          }

          // Cache the result
          setCachedData(cacheKey, result);
          return result;
        }

        throw new AuditTrailError('Invalid response format from server');
      })();

      // Store pending request
      pendingRequests.set(endpoint, requestPromise);

      try {
        const result = await requestPromise;
        return result;
      } finally {
        // Clean up pending request
        pendingRequests.delete(endpoint);
      }
    } catch (error) {
      console.error(`Error fetching audit trail ${id}:`, error);

      // Clean up pending request on error
      pendingRequests.delete(endpoint);

      handleApiError(error);
    }
  },

  // Export audit trails (if needed)
  async exportAuditTrails(query: PaginateQuery & AuditTrailFilters, format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    try {
      const params = new URLSearchParams();

      // Add all query parameters
      if (query.search) params.set('search', query.search);
      if (query.dateFrom) params.set('dateFrom', query.dateFrom);
      if (query.dateTo) params.set('dateTo', query.dateTo);
      if (query.userId) params.set('userId', query.userId);
      if (query.action) params.set('action', query.action);
      if (query.module) params.set('module', query.module);
      if (query.status) params.set('status', query.status);
      if (query.ipAddress) params.set('ipAddress', query.ipAddress);
      if (query.resourceType) params.set('resourceType', query.resourceType);
      if (query.resourceId) params.set('resourceId', query.resourceId);

      params.set('format', format);

      const response = await auditApiClient.get(`/export?${params.toString()}`, {
        responseType: 'blob',
      });

      if (response.data instanceof Blob) {
        return response.data;
      }

      throw new AuditTrailError('Invalid export response format');
    } catch (error) {
      console.error('Error exporting audit trails:', error);
      handleApiError(error);
    }
  },

  // Get audit trail statistics
  async getAuditStats(dateFrom?: string, dateTo?: string): Promise<{
    totalEntries: number;
    successfulActions: number;
    failedActions: number;
    topActions: Array<{ action: string; count: number }>;
    topModules: Array<{ module: string; count: number }>;
    topUsers: Array<{ user: string; count: number }>;
  }> {
    try {
      const params = new URLSearchParams();

      if (dateFrom) params.set('dateFrom', dateFrom);
      if (dateTo) params.set('dateTo', dateTo);

      const response = await auditApiClient.get(`/stats?${params.toString()}`);

      // Handle both standard response format and direct data format
      if (response.data && typeof response.data === 'object') {
        if (response.data.success !== undefined) {
          return response.data.data;
        } else {
          return response.data;
        }
      }

      throw new AuditTrailError('Invalid stats response format');
    } catch (error) {
      console.error('Error fetching audit stats:', error);
      handleApiError(error);
    }
  },

  // Helper methods for filter options
  getActionOptions(): { value: string; label: string }[] {
    return [
      { value: 'login', label: 'Login' },
      { value: 'logout', label: 'Logout' },
      { value: 'create', label: 'Create' },
      { value: 'update', label: 'Update' },
      { value: 'delete', label: 'Delete' },
      { value: 'view', label: 'View' },
      { value: 'export', label: 'Export' },
      { value: 'import', label: 'Import' },
    ];
  },

  getModuleOptions(): { value: string; label: string }[] {
    return [
      { value: 'authentication', label: 'Authentication' },
      { value: 'user_management', label: 'User Management' },
      { value: 'role_management', label: 'Role Management' },
      { value: 'permission_management', label: 'Permission Management' },
      { value: 'license_management', label: 'License Management' },
      { value: 'spectrum_management', label: 'Spectrum Management' },
      { value: 'transaction_management', label: 'Transaction Management' },
      { value: 'system_settings', label: 'System Settings' },
    ];
  },

  getStatusOptions(): { value: string; label: string }[] {
    return [
      { value: 'success', label: 'Success' },
      { value: 'failure', label: 'Failure' },
      { value: 'warning', label: 'Warning' },
    ];
  },

  getResourceTypeOptions(): { value: string; label: string }[] {
    return [
      { value: 'User', label: 'User' },
      { value: 'Role', label: 'Role' },
      { value: 'Permission', label: 'Permission' },
      { value: 'License', label: 'License' },
      { value: 'Spectrum', label: 'Spectrum' },
      { value: 'Transaction', label: 'Transaction' },
      { value: 'Authentication', label: 'Authentication' },
    ];
  },

  // Utility functions
  formatAuditAction(action: string): string {
    const actionMap: Record<string, string> = {
      'login': 'Login',
      'logout': 'Logout',
      'create': 'Create',
      'update': 'Update',
      'delete': 'Delete',
      'view': 'View',
      'export': 'Export',
      'import': 'Import',
    };
    return actionMap[action] || action.charAt(0).toUpperCase() + action.slice(1);
  },

  formatAuditModule(module: string): string {
    const moduleMap: Record<string, string> = {
      'authentication': 'Authentication',
      'user_management': 'User Management',
      'role_management': 'Role Management',
      'permission_management': 'Permission Management',
      'license_management': 'License Management',
      'spectrum_management': 'Spectrum Management',
      'transaction_management': 'Transaction Management',
      'system_settings': 'System Settings',
    };
    return moduleMap[module] || module.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  },

  formatAuditStatus(status: string): { text: string; color: string } {
    const statusMap: Record<string, { text: string; color: string }> = {
      'success': { text: 'Success', color: 'green' },
      'failure': { text: 'Failed', color: 'red' },
      'warning': { text: 'Warning', color: 'yellow' },
    };
    return statusMap[status] || { text: status.charAt(0).toUpperCase() + status.slice(1), color: 'gray' };
  },

  // Validate date range
  validateDateRange(dateFrom?: string, dateTo?: string): { isValid: boolean; error?: string } {
    if (!dateFrom && !dateTo) {
      return { isValid: true };
    }

    if (dateFrom && dateTo) {
      const fromDate = new Date(dateFrom);
      const toDate = new Date(dateTo);

      if (fromDate > toDate) {
        return { isValid: false, error: 'Start date must be before end date' };
      }

      // Check if date range is too large (more than 1 year)
      const oneYear = 365 * 24 * 60 * 60 * 1000;
      if (toDate.getTime() - fromDate.getTime() > oneYear) {
        return { isValid: false, error: 'Date range cannot exceed 1 year' };
      }
    }

    return { isValid: true };
  },

  // Get user display name from audit trail entry
  getUserDisplayName(auditTrail: AuditTrail): string {
    if (auditTrail.user) {
      const firstName = auditTrail.user.first_name || '';
      const lastName = auditTrail.user.last_name || '';
      const fullName = `${firstName} ${lastName}`.trim();
      return fullName || auditTrail.user.email || 'Unknown User';
    }
    return auditTrail.user_id || 'System';
  },

  // Format IP address for display
  formatIpAddress(ipAddress?: string): string {
    if (!ipAddress || ipAddress === 'unknown') {
      return 'Unknown';
    }
    return ipAddress;
  },

  // Check if audit entry has changes (for updates)
  hasChanges(auditTrail: AuditTrail): boolean {
    return !!(auditTrail.old_values || auditTrail.new_values);
  },

  // Get changes summary for display
  getChangesSummary(auditTrail: AuditTrail): string[] {
    const changes: string[] = [];

    if (auditTrail.old_values && auditTrail.new_values) {
      const oldValues = auditTrail.old_values;
      const newValues = auditTrail.new_values;

      Object.keys(newValues).forEach(key => {
        if (oldValues[key] !== newValues[key] && key !== 'updated_at') {
          changes.push(`${key}: ${oldValues[key]} → ${newValues[key]}`);
        }
      });
    }

    return changes;
  },

  // Cache management utilities
  clearCache(): void {
    auditTrailCache.clear();
    pendingRequests.clear();
    requestTimestamps.clear();
    console.log('Audit trail cache cleared');
  },

  getCacheStats(): { cacheSize: number; pendingRequests: number; rateLimitEntries: number } {
    return {
      cacheSize: auditTrailCache.size,
      pendingRequests: pendingRequests.size,
      rateLimitEntries: requestTimestamps.size
    };
  },

  // Force refresh by clearing cache for specific queries
  refreshData(query?: any): void {
    if (query) {
      const cacheKey = getCacheKey('list', query);
      auditTrailCache.delete(cacheKey);
    } else {
      // Clear all list caches
      for (const [key] of auditTrailCache) {
        if (key.includes('audit_trail_list')) {
          auditTrailCache.delete(key);
        }
      }
    }
    console.log('Audit trail data refreshed');
  },
};
