# Application Evaluation System

This directory contains the application evaluation system that allows admin users to review and evaluate submitted applications step by step.

## Structure

```
/applications/[license-type]/evaluate/
├── page.tsx                        # Main evaluation router
├── applicant-info/page.tsx         # Applicant information evaluation
├── address-info/page.tsx           # Address information evaluation
├── contact-info/page.tsx           # Contact information evaluation
├── service-scope/page.tsx          # Service scope evaluation
├── legal-history/page.tsx          # Legal history evaluation
├── management/page.tsx             # Management team evaluation
├── professional-services/page.tsx  # Professional services evaluation
├── documents/page.tsx              # Documents evaluation
├── review-submit/page.tsx          # Final review and submission
└── README.md                       # Complete documentation
```

## Features

### 🔍 **Application Preview**
- **Read-only display** of submitted application data
- **Step-by-step navigation** following the same flow as customer application
- **License type-specific steps** loaded from configuration
- **Responsive layout** with progress tracking

### 📝 **Evaluation Form**
- **Comment system** for each step with rich text support
- **Status updates** (Under Review, In Evaluation, Approved, Rejected)
- **File attachments** for supporting documents
- **Step-specific feedback** tracking

### 🚀 **Navigation System**
- **Dynamic routing** based on license type configuration
- **Next/Previous buttons** with proper state management
- **Progress tracking** showing current step and completion
- **Breadcrumb navigation** for easy step jumping

### 🔐 **Access Control**
- **Role-based access** (admin, evaluator, staff only)
- **Application ownership** validation
- **Authentication checks** on all routes
- **Permission-based UI** elements

## Usage

### URL Structure
```
/applications/{license-type}/evaluate/{step}?application_id={id}
```

**Examples:**
- `/applications/telecommunications/evaluate/applicant-info?application_id=123`
- `/applications/broadcasting/evaluate/service-scope?application_id=456`

### Integration with LicenseManagementTable

The **View** button in `LicenseManagementTable` now automatically opens the evaluation system:

```typescript
// When user clicks "View" button in the table
handleViewApplication(applicationId) {
  // Automatically determines license type from application data
  const licenseTypeCode = application?.license_category?.license_type?.code || 'telecommunications';

  // Opens evaluation page with application_id as query parameter
  router.push(`/applications/${licenseTypeCode}/evaluate?application_id=${applicationId}`);
}
```

**Benefits:**
- ✅ **Seamless Integration** - Direct navigation from applications table to evaluation
- ✅ **Automatic License Type Detection** - Uses application's license type for routing
- ✅ **Query Parameter Handling** - Passes application_id for data loading
- ✅ **Fallback Support** - Uses departmentType or default if license type not found

### Navigation Flow
1. **Entry Point**: `/applications/[license-type]/evaluate/page.tsx`
   - Validates application and user permissions
   - Redirects to first step (usually `applicant-info`)

2. **Step Pages**: Each step follows the same pattern:
   - Load application and step-specific data
   - Display read-only form data
   - Provide evaluation form for comments/status
   - Handle navigation to next/previous steps

3. **Dynamic Steps**: Steps are loaded based on license type configuration
   - Uses `useDynamicNavigation` hook
   - Supports different step sequences per license type
   - Graceful handling of missing steps

## Components

### EvaluationLayout
**Location**: `/src/components/evaluation/EvaluationLayout.tsx`

**Features:**
- Wraps all evaluation pages
- Provides consistent layout and navigation
- Integrates with ApplicationLayout for progress tracking
- Handles responsive design and dark mode

**Props:**
```typescript
interface EvaluationLayoutProps {
  applicationId: string;
  licenseTypeCode: string;
  currentStepRoute: string;
  onNext?: () => void;
  onPrevious?: () => void;
  onStatusUpdate?: (status: ApplicationStatus, comment: string) => void;
  onCommentSave?: (comment: string) => void;
  onAttachmentUpload?: (file: File) => void;
  isSubmitting?: boolean;
  showNextButton?: boolean;
  showPreviousButton?: boolean;
}
```

### EvaluationForm
**Location**: `/src/components/evaluation/EvaluationForm.tsx`

**Features:**
- Comment input with validation
- Status selection dropdown
- File upload with drag-and-drop
- Action buttons (Save Comment, Update Status)
- Loading states and error handling

**Props:**
```typescript
interface EvaluationFormProps {
  applicationId: string;
  currentStep: string;
  onStatusUpdate?: (status: ApplicationStatus, comment: string) => void;
  onCommentSave?: (comment: string) => void;
  onAttachmentUpload?: (file: File) => void;
  isSubmitting?: boolean;
}
```

## Implementation Pattern

Each evaluation step page follows this pattern:

```typescript
const EvaluateStepPage: React.FC<Props> = ({ params }) => {
  // 1. Extract parameters
  const licenseType = params['license-type'];
  const applicationId = searchParams.get('application_id');

  // 2. State management
  const [loading, setLoading] = useState(true);
  const [application, setApplication] = useState(null);
  const [stepData, setStepData] = useState(null);

  // 3. Load data
  useEffect(() => {
    // Load application and step-specific data
  }, [applicationId]);

  // 4. Navigation handlers
  const handleNext = () => { /* Navigate to next step */ };
  const handlePrevious = () => { /* Navigate to previous step */ };

  // 5. Evaluation handlers
  const handleStatusUpdate = async (status, comment) => { /* Update status */ };
  const handleCommentSave = async (comment) => { /* Save comment */ };
  const handleAttachmentUpload = async (file) => { /* Upload file */ };

  // 6. Render with EvaluationLayout
  return (
    <EvaluationLayout {...props}>
      {/* Display step data in read-only format */}
    </EvaluationLayout>
  );
};
```

## Configuration

### License Type Steps
Steps are configured in `/src/config/licenseTypeSteps.ts`:

```typescript
export const licenseTypeSteps = {
  telecommunications: [
    'applicant-info',
    'address-info', 
    'contact-info',
    'service-scope',
    'technical-details',
    'documents',
    'review'
  ],
  broadcasting: [
    'applicant-info',
    'address-info',
    'contact-info', 
    'service-scope',
    'content-policy',
    'documents',
    'review'
  ]
  // ... other license types
};
```

### Status Options
Available status transitions:
- `submitted` → `under_review`
- `under_review` → `evaluation`
- `evaluation` → `approved` | `rejected`

## API Integration

### Required API Endpoints
```typescript
// Application management
applicationService.getApplication(id)
applicationService.updateApplicationStatus(id, status, comment)

// Evaluation-specific (to be implemented)
applicationService.addEvaluationComment(id, step, comment)
applicationService.uploadEvaluationAttachment(id, step, file)
applicationService.getEvaluationHistory(id)

// Step-specific data loading
applicantService.getApplicant(id)
addressService.getAddressesByEntity(type, id)
contactPersonService.getContactPersonsByApplication(id)
scopeOfServiceService.getScopeOfServiceByApplication(id)
```

## Future Enhancements

1. **Real-time collaboration** - Multiple evaluators working on same application
2. **Evaluation templates** - Pre-defined comment templates for common scenarios
3. **Approval workflows** - Multi-stage approval process with routing
4. **Notification system** - Email/SMS notifications for status changes
5. **Audit trail** - Complete history of evaluation actions
6. **Bulk operations** - Evaluate multiple applications simultaneously
7. **Custom fields** - License type-specific evaluation criteria

## Testing

To test the evaluation system:

1. **Create a test application** through the customer portal
2. **Submit the application** to change status to 'submitted'
3. **Access evaluation** via `/applications/{license-type}/evaluate?application_id={id}`
4. **Navigate through steps** and test evaluation features
5. **Verify data display** and form functionality
6. **Test status updates** and comment saving

## Notes

- All evaluation actions are currently logged to console for development
- Actual API integration for evaluation-specific features needs implementation
- File upload functionality requires backend endpoint implementation
- Status updates require proper backend validation and workflow logic
