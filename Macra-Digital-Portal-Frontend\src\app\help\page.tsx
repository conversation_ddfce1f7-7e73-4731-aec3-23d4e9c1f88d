'use client';

import { useState } from 'react';
import HelpCategories from '../../components/help/HelpCategories';
import HelpContent from '../../components/help/HelpContent';
import ContactSupport from '../../components/help/ContactSupport';

export default function HelpPage() {
  const [activeCategory, setActiveCategory] = useState('getting-started');

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 md:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                <i className="ri-question-line text-red-600 dark:text-red-400 text-xl"></i>
              </div>
            </div>
            <div className="ml-4">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Help & Support</h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Find answers to common questions and get support for MACRA Digital Portal
              </p>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="max-w-2xl">
            <label htmlFor="help-search" className="sr-only">Search help articles</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="ri-search-line text-gray-400 dark:text-gray-500"></i>
              </div>
              <input
                id="help-search"
                name="help-search"
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm text-gray-900 dark:text-gray-100"
                placeholder="Search help articles, FAQs, and guides..."
                type="search"
              />
            </div>
          </div>
        </div>

        {/* Help & Support Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left sidebar with categories */}
          <div className="lg:col-span-1">
            <HelpCategories 
              activeCategory={activeCategory}
              onCategoryChange={setActiveCategory}
            />
            <div className="mt-6">
              <ContactSupport />
            </div>
          </div>

          {/* Main content area */}
          <div className="lg:col-span-3">
            <HelpContent activeCategory={activeCategory} />
          </div>
        </div>

        {/* Quick Links Section */}
        <div className="mt-12">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">Popular Help Topics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <button
              type="button"
              onClick={() => setActiveCategory('getting-started')}
              className="text-left p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <i className="ri-rocket-line text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Getting Started Guide</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Learn the basics of using MACRA Portal</p>
                </div>
              </div>
            </button>

            <button
              type="button"
              onClick={() => setActiveCategory('license-management')}
              className="text-left p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <i className="ri-key-line text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">License Applications</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">How to apply for new licenses</p>
                </div>
              </div>
            </button>

            <button
              type="button"
              onClick={() => setActiveCategory('account-settings')}
              className="text-left p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <i className="ri-settings-line text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Account Settings</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Manage your profile and preferences</p>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}