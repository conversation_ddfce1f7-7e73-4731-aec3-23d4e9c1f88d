import { DataSource, DataSourceOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';

export type DatabaseType = 'postgres' | 'mysql' | 'mariadb' | 'mssql';

export function getDatabaseType(driver: string): DatabaseType {
  switch (driver?.toLowerCase()) {
    case 'mysql':
      return 'mysql';
    case 'mariadb':
      return 'mariadb';
    case 'postgres':
    case 'postgresql':
      return 'postgres';
    case 'mssql':
    case 'sqlserver':
      return 'mssql';
    default:
      return 'postgres'; // Default fallback
  }
}

export function getUuidGenerationStrategy(dbType: DatabaseType): string {
  switch (dbType) {
    case 'postgres':
      // Use gen_random_uuid() which is available in PostgreSQL 13+ without extensions
      // For older versions, falls back to uuid_generate_v4() if uuid-ossp is available
      return 'gen_random_uuid()';
    case 'mysql':
    case 'mariadb':
      // Use UUID() function available in MySQL/MariaDB
      return 'UUID()';
    case 'mssql':
      // Use NEWID() function available in SQL Server
      return 'NEWID()';
    default:
      return 'gen_random_uuid()';
  }
}

export function getDatabaseSpecificOptions(
  dbType: DatabaseType,
  config: ConfigService,
): Partial<DataSourceOptions> {
  const baseOptions = {
    host: config.get<string>('DB_HOST'),
    port: config.get<number>('DB_PORT'),
    username: config.get<string>('DB_USERNAME'),
    password: config.get<string>('DB_PASSWORD'),
    database: config.get<string>('DB_NAME'),
    synchronize: config.get<string>('NODE_ENV') !== 'production',
    logging: config.get<string>('NODE_ENV') === 'development',
    retryAttempts: 3,
    retryDelay: 3000,
  };

  switch (dbType) {
    case 'postgres':
      return {
        ...baseOptions,
        type: 'postgres',
        ssl: config.get<string>('DB_SSL', 'false') === 'true'
          ? { rejectUnauthorized: false }
          : false,
        // PostgreSQL specific options
        extra: {
          // Disable automatic extension installation
          installExtensions: false,
        },
      };

    case 'mysql':
      return {
        ...baseOptions,
        type: 'mysql',
        charset: 'utf8mb4',
        timezone: '+00:00',
        ssl: config.get<string>('DB_SSL', 'false') === 'true'
          ? { rejectUnauthorized: false }
          : false,
      };

    case 'mariadb':
      return {
        ...baseOptions,
        type: 'mariadb',
        charset: 'utf8mb4',
        timezone: '+00:00',
        ssl: config.get<string>('DB_SSL', 'false') === 'true'
          ? { rejectUnauthorized: false }
          : false,
      };

    case 'mssql':
      return {
        ...baseOptions,
        type: 'mssql',
        options: {
          encrypt: config.get<string>('DB_SSL', 'false') === 'true',
          trustServerCertificate: true,
        },
      };

    default:
      return baseOptions;
  }
}

/**
 * Initialize database with required functions/extensions based on database type
 */
export async function initializeDatabase(dataSource: DataSource): Promise<void> {
  const dbType = dataSource.options.type as DatabaseType;
  
  try {
    switch (dbType) {
      case 'postgres':
        // Try to enable uuid-ossp extension, but don't fail if it's not available
        try {
          await dataSource.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
          console.log('✅ PostgreSQL uuid-ossp extension enabled');
        } catch (error) {
          console.log('⚠️  uuid-ossp extension not available, using gen_random_uuid() instead');
          // Check if gen_random_uuid is available (PostgreSQL 13+)
          try {
            await dataSource.query('SELECT gen_random_uuid();');
            console.log('✅ PostgreSQL gen_random_uuid() function available');
          } catch (genRandomError) {
            console.warn('⚠️  Neither uuid-ossp nor gen_random_uuid() available. UUID generation may fail.');
          }
        }
        break;

      case 'mysql':
      case 'mariadb':
        // Check if UUID() function is available
        try {
          await dataSource.query('SELECT UUID();');
          console.log('✅ MySQL/MariaDB UUID() function available');
        } catch (error) {
          console.warn('⚠️  MySQL/MariaDB UUID() function not available');
        }
        break;

      case 'mssql':
        // Check if NEWID() function is available
        try {
          await dataSource.query('SELECT NEWID();');
          console.log('✅ SQL Server NEWID() function available');
        } catch (error) {
          console.warn('⚠️  SQL Server NEWID() function not available');
        }
        break;
    }
  } catch (error) {
    console.warn('Database initialization warning:', error.message);
  }
}

/**
 * Get the appropriate UUID column definition for the database type
 */
export function getUuidColumnDefinition(dbType: DatabaseType): any {
  switch (dbType) {
    case 'postgres':
      return {
        type: 'uuid',
        generatedType: 'uuid',
        default: () => 'gen_random_uuid()',
      };
    case 'mysql':
    case 'mariadb':
      return {
        type: 'varchar',
        length: 36,
        default: () => 'UUID()',
      };
    case 'mssql':
      return {
        type: 'uniqueidentifier',
        default: () => 'NEWID()',
      };
    default:
      return {
        type: 'uuid',
        generatedType: 'uuid',
        default: () => 'gen_random_uuid()',
      };
  }
}
