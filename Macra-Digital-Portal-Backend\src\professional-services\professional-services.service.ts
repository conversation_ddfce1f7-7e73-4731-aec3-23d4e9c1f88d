import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { ProfessionalServices } from '../entities/professional-services.entity';
import { CreateProfessionalServicesDto } from '../dto/professional-services/create-professional-services.dto';
import { UpdateProfessionalServicesDto } from '../dto/professional-services/update-professional-services.dto';

@Injectable()
export class ProfessionalServicesService {
  constructor(
    @InjectRepository(ProfessionalServices)
    private professionalServicesRepository: Repository<ProfessionalServices>,
  ) {}

  async create(dto: CreateProfessionalServicesDto, createdBy: string): Promise<ProfessionalServices> {
    const professionalServices = this.professionalServicesRepository.create({
      ...dto,
      professional_services_id: uuidv4(),
      created_by: createdBy,
    });
    return await this.professionalServicesRepository.save(professionalServices);
  }

  async findAll(): Promise<ProfessionalServices[]> {
    return await this.professionalServicesRepository.find({
      where: { deleted_at: undefined },
      order: { created_at: 'DESC' }
    });
  }

  async findOne(id: string): Promise<ProfessionalServices> {
    const professionalServices = await this.professionalServicesRepository.findOne({
      where: { professional_services_id: id, deleted_at: undefined }
    });

    if (!professionalServices) {
      throw new NotFoundException(`Professional services with ID ${id} not found`);
    }

    return professionalServices;
  }

  async findByApplication(applicationId: string): Promise<ProfessionalServices | null> {
    return await this.professionalServicesRepository.findOne({
      where: { application_id: applicationId, deleted_at: undefined },
      order: { created_at: 'DESC' }
    });
  }

  async update(id: string, dto: UpdateProfessionalServicesDto, updatedBy: string): Promise<ProfessionalServices> {
    const professionalServices = await this.findOne(id);
    
    Object.assign(professionalServices, dto, { updated_by: updatedBy });
    return await this.professionalServicesRepository.save(professionalServices);
  }

  async softDelete(id: string): Promise<void> {
    const professionalServices = await this.findOne(id);
    professionalServices.deleted_at = new Date();
    await this.professionalServicesRepository.save(professionalServices);
  }

  async createOrUpdate(applicationId: string, dto: Omit<CreateProfessionalServicesDto, 'application_id'>, userId: string): Promise<ProfessionalServices> {
    // Check if professional services already exists for this application
    const existing = await this.findByApplication(applicationId);
    
    if (existing) {
      // Update existing professional services
      return await this.update(existing.professional_services_id, dto, userId);
    } else {
      // Create new professional services
      return await this.create({
        application_id: applicationId,
        ...dto
      }, userId);
    }
  }
}
