'use client';

import Link from 'next/link';

interface ExpirationItem {
  id: string;
  licenseNumber: string;
  company: string;
  daysUntilExpiry: number;
  status: 'critical' | 'warning' | 'info';
  type: string;
}

interface UpcomingExpirationsProps {
  className?: string;
}

const UpcomingExpirations = ({ className = '' }: UpcomingExpirationsProps) => {
  const expirations: ExpirationItem[] = [
    {
      id: '1',
      licenseNumber: 'LIC-2022-1845',
      company: 'Airtel Malawi',
      daysUntilExpiry: 5,
      status: 'critical',
      type: 'Mobile Network'
    },
    {
      id: '2',
      licenseNumber: 'LIC-2023-0234',
      company: 'TNM Plc',
      daysUntilExpiry: 12,
      status: 'warning',
      type: 'Internet Service'
    },
    {
      id: '3',
      licenseNumber: 'LIC-2022-0876',
      company: 'Malawi Broadcasting Corporation',
      daysUntilExpiry: 28,
      status: 'info',
      type: 'Broadcasting'
    },
    {
      id: '4',
      licenseNumber: 'LIC-2023-0445',
      company: 'Access Communications',
      daysUntilExpiry: 35,
      status: 'info',
      type: 'Data Services'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical':
        return 'bg-red-50 border-red-100 text-red-600';
      case 'warning':
        return 'bg-yellow-50 border-yellow-100 text-yellow-600';
      case 'info':
        return 'bg-blue-50 border-blue-100 text-blue-600';
      default:
        return 'bg-gray-50 border-gray-100 text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'critical':
        return 'ri-error-warning-line';
      case 'warning':
        return 'ri-time-line';
      case 'info':
        return 'ri-information-line';
      default:
        return 'ri-time-line';
    }
  };

  const getStatusBadge = (daysUntilExpiry: number) => {
    if (daysUntilExpiry <= 7) {
      return 'bg-red-100 text-red-800';
    } else if (daysUntilExpiry <= 30) {
      return 'bg-yellow-100 text-yellow-800';
    } else {
      return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium leading-4 text-gray-900">
            Upcoming Expirations
          </h3>
          <Link 
            href="/dashboard/licenses?filter=expiring"
            className="text-sm text-primary hover:text-primary font-medium"
          >
            View All
          </Link>
        </div>
        
        <div className="space-y-4">
          {expirations.map((item) => (
            <div 
              key={item.id}
              className={`${getStatusColor(item.status)} border rounded-lg p-4`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-8 h-8 flex items-center justify-center rounded-full ${
                    item.status === 'critical' ? 'bg-red-100' : 
                    item.status === 'warning' ? 'bg-yellow-100' : 'bg-blue-100'
                  }`}>
                    <i className={`${getStatusIcon(item.status)} ${
                      item.status === 'critical' ? 'text-red-600' : 
                      item.status === 'warning' ? 'text-yellow-600' : 'text-blue-600'
                    }`}></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">{item.licenseNumber}</p>
                    <p className="text-xs text-gray-500">{item.company}</p>
                    <p className="text-xs text-gray-400">{item.type}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(item.daysUntilExpiry)}`}>
                    {item.daysUntilExpiry} days
                  </span>
                  <p className="text-xs text-gray-500 mt-1">
                    {item.daysUntilExpiry <= 7 ? 'Critical' : 
                     item.daysUntilExpiry <= 30 ? 'Warning' : 'Upcoming'}
                  </p>
                </div>
              </div>
              
              {/* Action buttons for critical items */}
              {item.status === 'critical' && (
                <div className="mt-3 flex space-x-2">
                  <button className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i className="ri-refresh-line mr-1"></i>
                    Renew Now
                  </button>
                  <button className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i className="ri-mail-line mr-1"></i>
                    Send Reminder
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Summary Stats */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-red-600">
                {expirations.filter(item => item.daysUntilExpiry <= 7).length}
              </div>
              <div className="text-xs text-gray-500">Critical</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-yellow-600">
                {expirations.filter(item => item.daysUntilExpiry > 7 && item.daysUntilExpiry <= 30).length}
              </div>
              <div className="text-xs text-gray-500">Warning</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {expirations.filter(item => item.daysUntilExpiry > 30).length}
              </div>
              <div className="text-xs text-gray-500">Upcoming</div>
            </div>
          </div>
        </div>

        <div className="mt-4">
          <Link
            href="/dashboard/licenses"
            className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
          >
            Manage All Licenses
          </Link>
        </div>
      </div>
    </div>
  );
};

export default UpcomingExpirations;
