"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedProofOfPaymentResponseDto = exports.PaginatedPaymentResponseDto = exports.PaymentStatisticsDto = exports.UserBasicInfoDto = exports.ProofOfPaymentResponseDto = exports.PaymentResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const payment_entity_1 = require("../entities/payment.entity");
const proof_of_payment_entity_1 = require("../entities/proof-of-payment.entity");
class PaymentResponseDto {
    payment_id;
    invoice_number;
    amount;
    currency;
    status;
    payment_type;
    description;
    due_date;
    issue_date;
    paid_date;
    payment_method;
    notes;
    transaction_reference;
    user_id;
    application_id;
    created_at;
    updated_at;
    user;
    proof_of_payments;
}
exports.PaymentResponseDto = PaymentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "payment_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "invoice_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentResponseDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: payment_entity_1.Currency }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: payment_entity_1.PaymentStatus }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: payment_entity_1.PaymentType }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "payment_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], PaymentResponseDto.prototype, "due_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], PaymentResponseDto.prototype, "issue_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", Date)
], PaymentResponseDto.prototype, "paid_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "payment_method", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "transaction_reference", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "user_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "application_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], PaymentResponseDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], PaymentResponseDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => UserBasicInfoDto }),
    __metadata("design:type", UserBasicInfoDto)
], PaymentResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => ProofOfPaymentResponseDto, isArray: true }),
    __metadata("design:type", Array)
], PaymentResponseDto.prototype, "proof_of_payments", void 0);
class ProofOfPaymentResponseDto {
    proof_id;
    transaction_reference;
    amount;
    currency;
    payment_method;
    payment_date;
    original_filename;
    file_size;
    mime_type;
    status;
    notes;
    review_notes;
    reviewed_by;
    reviewed_at;
    payment_id;
    submitted_by;
    created_at;
    updated_at;
    user;
    reviewer;
}
exports.ProofOfPaymentResponseDto = ProofOfPaymentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "proof_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "transaction_reference", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], ProofOfPaymentResponseDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "payment_method", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], ProofOfPaymentResponseDto.prototype, "payment_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "original_filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], ProofOfPaymentResponseDto.prototype, "file_size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "mime_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: proof_of_payment_entity_1.ProofOfPaymentStatus }),
    __metadata("design:type", typeof (_a = typeof proof_of_payment_entity_1.ProofOfPaymentStatus !== "undefined" && proof_of_payment_entity_1.ProofOfPaymentStatus) === "function" ? _a : Object)
], ProofOfPaymentResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "review_notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "reviewed_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", Date)
], ProofOfPaymentResponseDto.prototype, "reviewed_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "payment_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ProofOfPaymentResponseDto.prototype, "submitted_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], ProofOfPaymentResponseDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], ProofOfPaymentResponseDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => UserBasicInfoDto }),
    __metadata("design:type", UserBasicInfoDto)
], ProofOfPaymentResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => UserBasicInfoDto, nullable: true }),
    __metadata("design:type", UserBasicInfoDto)
], ProofOfPaymentResponseDto.prototype, "reviewer", void 0);
class UserBasicInfoDto {
    user_id;
    email;
    first_name;
    last_name;
    middle_name;
}
exports.UserBasicInfoDto = UserBasicInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], UserBasicInfoDto.prototype, "user_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], UserBasicInfoDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], UserBasicInfoDto.prototype, "first_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], UserBasicInfoDto.prototype, "last_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", String)
], UserBasicInfoDto.prototype, "middle_name", void 0);
class PaymentStatisticsDto {
    totalPayments;
    paidPayments;
    pendingPayments;
    overduePayments;
    totalAmount;
    paidAmount;
    pendingAmount;
}
exports.PaymentStatisticsDto = PaymentStatisticsDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentStatisticsDto.prototype, "totalPayments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentStatisticsDto.prototype, "paidPayments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentStatisticsDto.prototype, "pendingPayments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentStatisticsDto.prototype, "overduePayments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentStatisticsDto.prototype, "totalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentStatisticsDto.prototype, "paidAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaymentStatisticsDto.prototype, "pendingAmount", void 0);
class PaginatedPaymentResponseDto {
    payments;
    total;
    page;
    limit;
    totalPages;
}
exports.PaginatedPaymentResponseDto = PaginatedPaymentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => PaymentResponseDto, isArray: true }),
    __metadata("design:type", Array)
], PaginatedPaymentResponseDto.prototype, "payments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedPaymentResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedPaymentResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedPaymentResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedPaymentResponseDto.prototype, "totalPages", void 0);
class PaginatedProofOfPaymentResponseDto {
    proofOfPayments;
    total;
    page;
    limit;
    totalPages;
}
exports.PaginatedProofOfPaymentResponseDto = PaginatedProofOfPaymentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => ProofOfPaymentResponseDto, isArray: true }),
    __metadata("design:type", Array)
], PaginatedProofOfPaymentResponseDto.prototype, "proofOfPayments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedProofOfPaymentResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedProofOfPaymentResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedProofOfPaymentResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PaginatedProofOfPaymentResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=payment-response.dto.js.map