'use client';

import { useECharts } from "@/lib/echats";


const UserActivity = () => {
  const chartRef = useECharts({
    animation: false,
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      borderColor: '#e5e7eb',
      textStyle: { color: '#1f2937' },
    },
    grid: {
      left: 0,
      right: 0,
      top: 10,
      bottom: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#1f2937' },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#1f2937' },
    },
    series: [
      {
        data: [2500, 2800, 3100, 2950, 3200, 3000, 3649],
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: 'rgba(141, 211, 199, 1)',
          borderRadius: 4,
        },
      },
    ],
  });

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">User Activity</h3>
          <select className="block pl-3 pr-10 py-2 text-sm border-gray-300 focus:outline-none focus:ring-primary focus:border-primary rounded-md">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
          </select>
        </div>
        <div ref={chartRef} style={{ height: '250px' }} />
        <div className="mt-5 grid grid-cols-3 gap-5 text-center">
          <div>
            <p className="text-2xl font-semibold text-gray-900">87%</p>
            <p className="text-sm text-gray-500">Active Rate</p>
          </div>
          <div>
            <p className="text-2xl font-semibold text-gray-900">42 min</p>
            <p className="text-sm text-gray-500">Avg. Session</p>
          </div>
          <div>
            <p className="text-2xl font-semibold text-gray-900">3,649</p>
            <p className="text-sm text-gray-500">Total Users</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserActivity;