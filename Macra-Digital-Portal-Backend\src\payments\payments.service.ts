import { 
  Injectable, 
  NotFoundException, 
  ConflictException, 
  BadRequestException,
  UnauthorizedException,
  InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like, Between } from 'typeorm';
import { Payment, PaymentStatus, PaymentType } from './entities/payment.entity';
import { ProofOfPayment, ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
import { User } from '../entities/user.entity';
import { CreateProofOfPaymentDto, UpdateProofOfPaymentStatusDto } from './dto/create-proof-of-payment.dto';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { FileUploadUtils } from './utils/file-upload.utils';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

export interface PaymentFilters {
  status?: PaymentStatus;
  paymentType?: PaymentType;
  dateRange?: 'last-30' | 'last-90' | 'last-year';
  search?: string;
  userId?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

export interface PaymentQueryResult {
  payments: Payment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProofOfPaymentQueryResult {
  proofOfPayments: ProofOfPayment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface FileUploadResult {
  filename: string;
  originalname: string;
  path: string;
  size: number;
  mimetype: string;
}

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
    @InjectRepository(ProofOfPayment)
    private proofOfPaymentsRepository: Repository<ProofOfPayment>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  /**
   * Create a new payment record
   */
  async createPayment(createPaymentDto: CreatePaymentDto): Promise<Payment> {
    try {
      // Validate user exists
      const user = await this.usersRepository.findOne({
        where: { user_id: createPaymentDto.user_id }
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if invoice number already exists
      const existingPayment = await this.paymentsRepository.findOne({
        where: { invoice_number: createPaymentDto.invoice_number }
      });

      if (existingPayment) {
        throw new ConflictException('Invoice number already exists');
      }

      const payment = this.paymentsRepository.create(createPaymentDto);
      return await this.paymentsRepository.save(payment);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create payment');
    }
  }

  /**
   * Get all payments with optional filters and pagination
   */
  async getPayments(
    filters: PaymentFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<PaymentQueryResult> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const query = this.paymentsRepository.createQueryBuilder('payment')
        .leftJoinAndSelect('payment.user', 'user')
        .leftJoinAndSelect('payment.application', 'application')
        .leftJoinAndSelect('payment.proof_of_payments', 'proof_of_payments');

      // Apply filters
      if (filters.status) {
        query.andWhere('payment.status = :status', { status: filters.status });
      }

      if (filters.paymentType) {
        query.andWhere('payment.payment_type = :paymentType', { paymentType: filters.paymentType });
      }

      if (filters.userId) {
        query.andWhere('payment.user_id = :userId', { userId: filters.userId });
      }

      if (filters.dateRange) {
        const now = new Date();
        let startDate: Date;

        switch (filters.dateRange) {
          case 'last-30':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case 'last-90':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          case 'last-year':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date('1970-01-01');
        }

        query.andWhere('payment.issue_date >= :startDate', { startDate });
      }

      if (filters.search) {
        query.andWhere('(payment.invoice_number ILIKE :search OR payment.description ILIKE :search)', {
          search: `%${filters.search}%`
        });
      }

      // Get total count
      const total = await query.getCount();

      // Apply pagination and get results
      const payments = await query
        .orderBy('payment.created_at', 'DESC')
        .skip(skip)
        .take(limit)
        .getMany();

      return {
        payments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch payments');
    }
  }

  /**
   * Get payment by ID
   */
  async getPaymentById(paymentId: string): Promise<Payment> {
    try {
      const payment = await this.paymentsRepository.findOne({
        where: { payment_id: paymentId },
        relations: ['user', 'application', 'proof_of_payments']
      });

      if (!payment) {
        throw new NotFoundException('Payment not found');
      }

      return payment;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to fetch payment');
    }
  }

  /**
   * Update payment
   */
  async updatePayment(paymentId: string, updatePaymentDto: UpdatePaymentDto): Promise<Payment> {
    try {
      const payment = await this.getPaymentById(paymentId);

      // Check if updating invoice number and if it conflicts
      if (updatePaymentDto.invoice_number && updatePaymentDto.invoice_number !== payment.invoice_number) {
        const existingPayment = await this.paymentsRepository.findOne({
          where: { invoice_number: updatePaymentDto.invoice_number }
        });

        if (existingPayment && existingPayment.payment_id !== paymentId) {
          throw new ConflictException('Invoice number already exists');
        }
      }

      await this.paymentsRepository.update(paymentId, updatePaymentDto);
      return await this.getPaymentById(paymentId);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update payment');
    }
  }

  /**
   * Delete payment
   */
  async deletePayment(paymentId: string): Promise<void> {
    try {
      const payment = await this.getPaymentById(paymentId);
      await this.paymentsRepository.remove(payment);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete payment');
    }
  }

  /**
   * Upload proof of payment
   */
  async uploadProofOfPayment(
    createProofOfPaymentDto: CreateProofOfPaymentDto,
    file: Express.Multer.File,
    userId: string
  ): Promise<any> {
    try {
      // Validate payment exists
      const payment = await this.getPaymentById(createProofOfPaymentDto.payment_id);

      // Validate user exists and has permission
      const user = await this.usersRepository.findOne({
        where: { user_id: userId }
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if user owns the payment or has admin privileges
      if (payment.user_id !== userId && !user.roles?.some(role => role.name === 'admin')) {
        throw new UnauthorizedException('You can only upload proof of payment for your own payments');
      }

      // Use file upload utility to save file
      const { filename, filepath } = FileUploadUtils.saveFile(file);

      // Create proof of payment record
      const proofOfPayment = this.proofOfPaymentsRepository.create({
        ...createProofOfPaymentDto,
        submitted_by: userId,
        document_path: filepath,
        original_filename: file.originalname,
        file_size: file.size,
        mime_type: file.mimetype,
        status: ProofOfPaymentStatus.PENDING,
        payment_date: new Date(createProofOfPaymentDto.payment_date),
      });

      const savedProof = await this.proofOfPaymentsRepository.save(proofOfPayment);

      return await this.proofOfPaymentsRepository.findOne({
        where: { proof_id: savedProof.proof_id },
        relations: ['payment', 'user', 'reviewer']
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to upload proof of payment');
    }
  }

  /**
   * Get proof of payments with pagination
   */
  async getProofOfPayments(
    filters: { 
      status?: ProofOfPaymentStatus; 
      userId?: string;
      paymentId?: string;
    } = {},
    pagination: PaginationOptions = {}
  ): Promise<ProofOfPaymentQueryResult> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const query = this.proofOfPaymentsRepository.createQueryBuilder('proof')
        .leftJoinAndSelect('proof.payment', 'payment')
        .leftJoinAndSelect('proof.user', 'user')
        .leftJoinAndSelect('proof.reviewer', 'reviewer');

      // Apply filters
      if (filters.status) {
        query.andWhere('proof.status = :status', { status: filters.status });
      }

      if (filters.userId) {
        query.andWhere('proof.submitted_by = :userId', { userId: filters.userId });
      }

      if (filters.paymentId) {
        query.andWhere('proof.payment_id = :paymentId', { paymentId: filters.paymentId });
      }

      // Get total count
      const total = await query.getCount();

      // Apply pagination and get results
      const proofOfPayments = await query
        .orderBy('proof.created_at', 'DESC')
        .skip(skip)
        .take(limit)
        .getMany();

      return {
        proofOfPayments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch proof of payments');
    }
  }

  /**
   * Get proof of payment by ID
   */
  async getProofOfPaymentById(proofId: string): Promise<ProofOfPayment> {
    try {
      const proof = await this.proofOfPaymentsRepository.findOne({
        where: { proof_id: proofId },
        relations: ['payment', 'user', 'reviewer']
      });

      if (!proof) {
        throw new NotFoundException('Proof of payment not found');
      }

      return proof;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to fetch proof of payment');
    }
  }

  /**
   * Update proof of payment status (admin only)
   */
  async updateProofOfPaymentStatus(
    proofId: string,
    updateStatusDto: UpdateProofOfPaymentStatusDto,
    reviewerId: string
  ): Promise<ProofOfPayment> {
    try {
      const proof = await this.getProofOfPaymentById(proofId);

      // Validate reviewer exists
      const reviewer = await this.usersRepository.findOne({
        where: { user_id: reviewerId }
      });

      if (!reviewer) {
        throw new NotFoundException('Reviewer not found');
      }

      // Update proof of payment status
      await this.proofOfPaymentsRepository.update(proofId, {
        status: updateStatusDto.status as ProofOfPaymentStatus,
        review_notes: updateStatusDto.review_notes,
        reviewed_by: reviewerId,
        reviewed_at: new Date(),
      });

      // If approved, update payment status to paid
      if (updateStatusDto.status === 'approved') {
        await this.paymentsRepository.update(proof.payment_id, {
          status: PaymentStatus.PAID,
          paid_date: new Date(),
          payment_method: proof.payment_method,
          transaction_reference: proof.transaction_reference,
        });
      }

      return await this.getProofOfPaymentById(proofId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update proof of payment status');
    }
  }

  /**
   * Download proof of payment document
   */
  async downloadProofOfPayment(proofId: string, userId: string): Promise<{ filePath: string; filename: string }> {
    try {
      const proof = await this.getProofOfPaymentById(proofId);

      // Validate user has permission to download
      const user = await this.usersRepository.findOne({
        where: { user_id: userId }
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if user owns the proof of payment or has admin privileges
      if (proof.submitted_by !== userId && !user.roles?.some(role => role.name === 'admin')) {
        throw new UnauthorizedException('You can only download your own proof of payment documents');
      }

      // Check if file exists
      if (!FileUploadUtils.fileExists(proof.document_path)) {
        throw new NotFoundException('Document file not found');
      }

      return {
        filePath: proof.document_path,
        filename: proof.original_filename
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to download proof of payment');
    }
  }

  /**
   * Get payment statistics
   */
  async getPaymentStatistics(userId?: string) {
    try {
      const query = this.paymentsRepository.createQueryBuilder('payment');

      if (userId) {
        query.where('payment.user_id = :userId', { userId });
      }

      const [
        totalPayments,
        paidPayments,
        pendingPayments,
        overduePayments,
        totalAmount,
        paidAmount,
        pendingAmount
      ] = await Promise.all([
        query.getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PAID }).getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PENDING }).getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.OVERDUE }).getCount(),
        query.clone().select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PAID }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PENDING }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
      ]);

      return {
        totalPayments,
        paidPayments,
        pendingPayments,
        overduePayments,
        totalAmount: parseFloat(totalAmount.total || '0'),
        paidAmount: parseFloat(paidAmount.total || '0'),
        pendingAmount: parseFloat(pendingAmount.total || '0'),
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch payment statistics');
    }
  }

  /**
   * Mark overdue payments
   */
  async markOverduePayments(): Promise<void> {
    try {
      const today = new Date();
      await this.paymentsRepository
        .createQueryBuilder()
        .update(Payment)
        .set({ status: PaymentStatus.OVERDUE })
        .where('due_date < :today', { today })
        .andWhere('status = :status', { status: PaymentStatus.PENDING })
        .execute();
    } catch (error) {
      throw new InternalServerErrorException('Failed to mark overdue payments');
    }
  }
}