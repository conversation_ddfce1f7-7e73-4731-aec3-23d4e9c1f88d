import { 
  Injectable, 
  NotFoundException, 
  ConflictException, 
  BadRequestException,
  UnauthorizedException,
  InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like, Between } from 'typeorm';
import { Payment, PaymentStatus, PaymentType } from './entities/payment.entity';
import { User } from '../entities/user.entity';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';


export interface PaymentFilters {
  status?: PaymentStatus;
  paymentType?: PaymentType;
  dateRange?: 'last-30' | 'last-90' | 'last-year';
  search?: string;
  userId?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

export interface PaymentQueryResult {
  payments: Payment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}


export interface FileUploadResult {
  filename: string;
  originalname: string;
  path: string;
  size: number;
  mimetype: string;
}

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  /**
   * Create a new payment record
   */
  async createPayment(createPaymentDto: CreatePaymentDto): Promise<Payment> {
    try {
      // Validate user exists
      const user = await this.usersRepository.findOne({
        where: { user_id: createPaymentDto.user_id }
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Validate creator exists
      const creator = await this.usersRepository.findOne({
        where: { user_id: createPaymentDto.created_by }
      });

      if (!creator) {
        throw new NotFoundException('Creator user not found');
      }

      // Check if invoice number already exists
      const existingPayment = await this.paymentsRepository.findOne({
        where: { invoice_number: createPaymentDto.invoice_number }
      });

      if (existingPayment) {
        throw new ConflictException('Invoice number already exists');
      }

      const payment = this.paymentsRepository.create(createPaymentDto);
      return await this.paymentsRepository.save(payment);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create payment');
    }
  }

  /**
   * Get all payments with optional filters and pagination
   */
  async getPayments(
    filters: PaymentFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<PaymentQueryResult> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const query = this.paymentsRepository.createQueryBuilder('payment')
        .leftJoinAndSelect('payment.user', 'user')
        .leftJoinAndSelect('payment.application', 'application')
        .leftJoinAndSelect('payment.proof_of_payments', 'proof_of_payments');

      // Apply filters
      if (filters.status) {
        query.andWhere('payment.status = :status', { status: filters.status });
      }

      if (filters.paymentType) {
        query.andWhere('payment.payment_type = :paymentType', { paymentType: filters.paymentType });
      }

      if (filters.userId) {
        query.andWhere('payment.user_id = :userId', { userId: filters.userId });
      }

      if (filters.dateRange) {
        const now = new Date();
        let startDate: Date;

        switch (filters.dateRange) {
          case 'last-30':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case 'last-90':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          case 'last-year':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date('1970-01-01');
        }

        query.andWhere('payment.issue_date >= :startDate', { startDate });
      }

      if (filters.search) {
        query.andWhere('(payment.invoice_number ILIKE :search OR payment.description ILIKE :search)', {
          search: `%${filters.search}%`
        });
      }

      // Get total count
      const total = await query.getCount();

      // Apply pagination and get results
      const payments = await query
        .orderBy('payment.created_at', 'DESC')
        .skip(skip)
        .take(limit)
        .getMany();

      return {
        payments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch payments');
    }
  }

  /**
   * Get payment by ID
   */
  async getPaymentById(paymentId: string): Promise<Payment> {
    try {
      const payment = await this.paymentsRepository.findOne({
        where: { payment_id: paymentId },
        relations: ['user', 'application', 'proof_of_payments']
      });

      if (!payment) {
        throw new NotFoundException('Payment not found');
      }

      return payment;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to fetch payment');
    }
  }

  /**
   * Update payment
   */
  async updatePayment(paymentId: string, updatePaymentDto: UpdatePaymentDto): Promise<Payment> {
    try {
      const payment = await this.getPaymentById(paymentId);

      // Check if updating invoice number and if it conflicts
      if (updatePaymentDto.invoice_number && updatePaymentDto.invoice_number !== payment.invoice_number) {
        const existingPayment = await this.paymentsRepository.findOne({
          where: { invoice_number: updatePaymentDto.invoice_number }
        });

        if (existingPayment && existingPayment.payment_id !== paymentId) {
          throw new ConflictException('Invoice number already exists');
        }
      }

      await this.paymentsRepository.update(paymentId, updatePaymentDto);
      return await this.getPaymentById(paymentId);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update payment');
    }
  }

  /**
   * Delete payment
   */
  async deletePayment(paymentId: string): Promise<void> {
    try {
      const payment = await this.getPaymentById(paymentId);
      await this.paymentsRepository.remove(payment);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete payment');
    }
  }


  /**
   * Get payment statistics
   */
  async getPaymentStatistics(userId?: string) {
    try {
      const query = this.paymentsRepository.createQueryBuilder('payment');

      if (userId) {
        query.where('payment.user_id = :userId', { userId });
      }

      const [
        totalPayments,
        paidPayments,
        pendingPayments,
        overduePayments,
        totalAmount,
        paidAmount,
        pendingAmount
      ] = await Promise.all([
        query.getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PAID }).getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PENDING }).getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.OVERDUE }).getCount(),
        query.clone().select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PAID }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PENDING }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
      ]);

      return {
        totalPayments,
        paidPayments,
        pendingPayments,
        overduePayments,
        totalAmount: parseFloat(totalAmount.total || '0'),
        paidAmount: parseFloat(paidAmount.total || '0'),
        pendingAmount: parseFloat(pendingAmount.total || '0'),
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch payment statistics');
    }
  }

  /**
   * Mark overdue payments
   */
  async markOverduePayments(): Promise<void> {
    try {
      const today = new Date();
      await this.paymentsRepository
        .createQueryBuilder()
        .update(Payment)
        .set({ status: PaymentStatus.OVERDUE })
        .where('due_date < :today', { today })
        .andWhere('status = :status', { status: PaymentStatus.PENDING })
        .execute();
    } catch (error) {
      throw new InternalServerErrorException('Failed to mark overdue payments');
    }
  }

  /**
   * Get payments by entity (polymorphic relationship)
   */
  async getPaymentsByEntity(
    entityType: string,
    entityId: string,
    pagination: PaginationOptions = {}
  ): Promise<PaymentQueryResult> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const [payments, total] = await this.paymentsRepository.findAndCount({
        where: {
          entity_type: entityType,
          entity_id: entityId,
        },
        relations: ['user', 'creator'],
        skip,
        take: limit,
        order: { created_at: 'DESC' },
      });

      return {
        payments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to get payments by entity');
    }
  }

  /**
   * Create payment for a specific entity (polymorphic)
   */
  async createPaymentForEntity(
    entityType: string,
    entityId: string,
    paymentData: Omit<CreatePaymentDto, 'entity_type' | 'entity_id'>
  ): Promise<Payment> {
    const createPaymentDto: CreatePaymentDto = {
      ...paymentData,
      entity_type: entityType,
      entity_id: entityId,
    };

    return this.createPayment(createPaymentDto);
  }
}