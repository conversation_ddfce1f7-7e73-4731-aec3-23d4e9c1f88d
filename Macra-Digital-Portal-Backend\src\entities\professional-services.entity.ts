import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Applications } from './applications.entity';

@Entity('professional_services')
export class ProfessionalServices {
  @PrimaryGeneratedColumn('uuid')
  professional_services_id: string;

  @Column({ type: 'varchar', length: 36 })
  application_id: string;

  @Column({ type: 'text' })
  consultants: string;

  @Column({ type: 'text' })
  service_providers: string;

  @Column({ type: 'text' })
  technical_support: string;

  @Column({ type: 'text' })
  maintenance_arrangements: string;

  @Column({ type: 'text', nullable: true })
  professional_partnerships?: string;

  @Column({ type: 'text', nullable: true })
  outsourced_services?: string;

  @Column({ type: 'text', nullable: true })
  quality_assurance?: string;

  @Column({ type: 'text', nullable: true })
  training_programs?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'varchar', length: 36 })
  created_by: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  updated_by?: string;

  @Column({ type: 'timestamp', nullable: true })
  deleted_at?: Date;

  // Relationships
  @ManyToOne(() => Applications, { onDelete: 'CASCADE'})
  @JoinColumn({ name: 'application_id' })
  application: Applications;
}
