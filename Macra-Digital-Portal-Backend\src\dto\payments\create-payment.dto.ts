import { IsString, IsEnum, IsOptional, IsU<PERSON>D, IsN<PERSON>ber, IsDateString, Min, Length } from 'class-validator';
import { TransactionType, PaymentStatus, PaymentMethod } from '../../entities/payments.entity';

export class CreatePaymentDto {
  @IsString()
  @Length(1, 255)
  transaction_number: string;

  @IsOptional()
  @IsUUID()
  application_id?: string;

  @IsOptional()
  @IsUUID()
  license_id?: string;

  @IsUUID()
  applicant_id: string;

  @IsEnum(TransactionType)
  transaction_type: TransactionType;

  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  amount: number;

  @IsOptional()
  @IsString()
  @Length(3, 3)
  currency?: string;

  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @IsOptional()
  @IsEnum(PaymentMethod)
  payment_method?: PaymentMethod;

  @IsOptional()
  @IsString()
  @Length(1, 255)
  reference_number?: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsDateString()
  completed_at?: string;
}
