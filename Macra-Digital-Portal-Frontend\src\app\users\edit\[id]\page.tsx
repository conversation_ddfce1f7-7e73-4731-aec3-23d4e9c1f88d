'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { userService, UpdateUserDto, User } from '../../../../services/userService';
import { roleService, Role } from '../../../../services/roleService';

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;

  const [user, setUser] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    phone: '',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    role_ids: [] as string[],
  });
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingUser, setLoadingUser] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (userId) {
      loadUser();
      loadRoles();
    }
  }, [userId]);

  const loadUser = async () => {
    try {
      setLoadingUser(true);
      const userData = await userService.getUserById(userId);
      setUser(userData);
      
      // Populate form with user data
      setFormData({
        email: userData.email,
        password: '',
        confirmPassword: '',
        first_name: userData.first_name,
        last_name: userData.last_name,
        middle_name: userData.middle_name || '',
        phone: userData.phone || '',
        status: userData.status,
        role_ids: userData.roles?.map(role => role.role_id) || [],
      });
    } catch (err) {
      console.error('Error loading user:', err);
      setError('Failed to load user data');
    } finally {
      setLoadingUser(false);
    }
  };

  const loadRoles = async () => {
    try {
      const rolesData = await roleService.getRoles({ page: 1, limit: 100 });
      setRoles(rolesData.data || []);
    } catch (err) {
      console.error('Error loading roles:', err);
      setError('Failed to load roles');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate passwords match if password is being changed
    if (formData.password && formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    // Validate required fields
    if (!formData.email || !formData.first_name || !formData.last_name) {
      setError('Please fill in all required fields');
      setLoading(false);
      return;
    }

    try {
      const updateData: UpdateUserDto = {
        email: formData.email,
        first_name: formData.first_name,
        last_name: formData.last_name,
        middle_name: formData.middle_name || undefined,
        phone: formData.phone,
        status: formData.status,
        role_ids: formData.role_ids.length > 0 ? formData.role_ids : undefined,
      };

      // Only include password if it's provided
      if (formData.password.trim()) {
        updateData.password = formData.password;
      }

      await userService.updateUser(userId, updateData);
      setSuccess('User updated successfully!');
      
      // Reload user data
      await loadUser();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update user');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRoleChange = (roleId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      role_ids: checked
        ? [...prev.role_ids, roleId]
        : prev.role_ids.filter(id => id !== roleId)
    }));
  };

  if (loadingUser) {
    return (
      <main className="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
          </div>
        </div>
      </main>
    );
  }

  if (!user) {
    return (
      <main className="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-2xl font-semibold text-gray-900">User Not Found</h1>
            <p className="mt-2 text-gray-600">The user you're looking for doesn't exist.</p>
            <Link href="/users" className="mt-4 main-button inline-flex">
              Back to Users
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="tab-heading">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Edit User</h1>
            <p className="mt-1 text-sm text-gray-500">
              Update user information, roles, and permissions for {user.first_name} {user.last_name}.
            </p>
          </div>
          <div className="relative">
            <Link href="/users" className="main-button" role="button">
              <div className="w-5 h-5 flex items-center justify-center mr-2">
                <i className="ri-arrow-left-line"></i>
              </div>
              Back to Users
            </Link>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
            {success}
          </div>
        )}

        {/* Edit User Form */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <form onSubmit={handleSubmit} className="grid gap-6 sm:grid-cols-2 lg:grid-cols-2 sm:gap-x-6">
              {/* Basic Information Section */}
              <div className="form-section">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Basic Information</h3>
                <div className="inner-form-section">
                  {/* First Name */}
                  <div>
                    <label htmlFor="first_name" className="custom-form-label">First Name *</label>
                    <input
                      type="text"
                      name="first_name"
                      id="first_name"
                      value={formData.first_name}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                  </div>

                  {/* Last Name */}
                  <div>
                    <label htmlFor="last_name" className="custom-form-label">Last Name *</label>
                    <input
                      type="text"
                      name="last_name"
                      id="last_name"
                      value={formData.last_name}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                  </div>

                  {/* Middle Name */}
                  <div>
                    <label htmlFor="middle_name" className="custom-form-label">Middle Name</label>
                    <input
                      type="text"
                      name="middle_name"
                      id="middle_name"
                      value={formData.middle_name}
                      onChange={handleChange}
                      className="custom-input"
                    />
                  </div>

                  {/* Email */}
                  <div>
                    <label htmlFor="email" className="custom-form-label">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                  </div>

                  {/* Phone Number */}
                  <div className="sm:col-span-2">
                    <label htmlFor="phone" className="custom-form-label">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      id="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="custom-input"
                      placeholder="+265..."
                    />
                  </div>
                </div>
              </div>

              {/* Account Information Section */}
              <div className="form-section">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Account Information</h3>
                <div className="inner-form-section">
                  {/* Status */}
                  <div>
                    <label htmlFor="status" className="custom-form-label">Status *</label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="suspended">Suspended</option>
                    </select>
                  </div>

                  {/* Password */}
                  <div>
                    <label htmlFor="password" className="custom-form-label">New Password</label>
                    <input
                      type="password"
                      name="password"
                      id="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="custom-input"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Leave blank to keep current password. Must be at least 8 characters if changing.
                    </p>
                  </div>

                  {/* Confirm Password */}
                  <div className="sm:col-span-2">
                    <label htmlFor="confirmPassword" className="custom-form-label">Confirm New Password</label>
                    <input
                      type="password"
                      name="confirmPassword"
                      id="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="custom-input"
                    />
                  </div>
                </div>
              </div>

              {/* Role & Permissions Section */}
              <div className="form-section border-none sm:col-span-2">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Role & Permissions</h3>
                <div className="mt-4">
                  <label className="custom-form-label mb-2">User Roles</label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                    {roles.map((role) => (
                      <label
                        key={role.role_id}
                        className="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-red-50 transition"
                      >
                        <input
                          type="checkbox"
                          checked={formData.role_ids.includes(role.role_id)}
                          onChange={(e) => handleRoleChange(role.role_id, e.target.checked)}
                          className="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                        />
                        <span className="text-sm text-gray-700 capitalize">
                          {role.name.replace(/_/g, ' ')}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="sm:col-span-2 flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <Link
                  href="/users"
                  className="secondary-main-button"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={loading}
                  className="main-button disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Updating...' : 'Update User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  );
}
