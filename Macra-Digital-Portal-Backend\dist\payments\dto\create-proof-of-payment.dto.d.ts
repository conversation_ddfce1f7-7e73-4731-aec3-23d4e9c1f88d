import { PaymentMethod } from '../entities/proof-of-payment.entity';
export declare class CreateProofOfPaymentDto {
    transaction_reference: string;
    amount: number;
    currency: string;
    payment_method: PaymentMethod;
    payment_date: string;
    notes?: string;
    payment_id: string;
    submitted_by: string;
}
export declare class UpdateProofOfPaymentStatusDto {
    status: 'pending' | 'approved' | 'rejected';
    review_notes?: string;
    reviewed_by: string;
}
