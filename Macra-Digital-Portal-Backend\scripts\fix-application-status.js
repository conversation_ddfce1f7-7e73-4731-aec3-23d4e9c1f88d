// Quick script to fix the application status in the database
const { DataSource } = require('typeorm');

async function fixApplicationStatus() {
  console.log('🔧 Fixing application status in database...');

  // Create database connection
  const dataSource = new DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'macra_digital_portal',
    synchronize: false,
    logging: true,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connected');

    // Update applications with empty status to 'submitted'
    const result = await dataSource.query(`
      UPDATE applications 
      SET status = 'submitted' 
      WHERE status = '' OR status IS NULL
    `);

    console.log(`✅ Updated ${result.affectedRows} applications`);

    // Show current status distribution
    const statusDistribution = await dataSource.query(`
      SELECT status, COUNT(*) as count 
      FROM applications 
      GROUP BY status
    `);

    console.log('📊 Current status distribution:');
    statusDistribution.forEach(row => {
      console.log(`   ${row.status || '(empty)'}: ${row.count}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run the fix
fixApplicationStatus().catch(console.error);
