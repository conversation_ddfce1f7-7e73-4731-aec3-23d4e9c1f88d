import { useState } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { apiClient } from '../lib/apiClient';
import { taskAssignmentService, Task } from '@/services/task-assignment';

interface TaskNavigationInfo {
  task: {
    task_id: string;
    title: string;
    description?: string;
    entity_type?: string;
    entity_id?: string;
    task_type: string;
    status: string;
  };
  canNavigateToEntity: boolean;
}

export const useTaskNavigation = () => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  /**
   * Get task navigation information
   */
  const getTaskNavigationInfo = async (taskId: string): Promise<Task | null> => {
    setIsLoading(true);

    try {
      const task = await taskAssignmentService.getTaskById(taskId);

      console.log('Fetched task:', task);

      if (task) {
        return task;
      }
      return null;
    } catch (error: any) {
      console.error('Error fetching task navigation info:', error);
      toast.error('Failed to load task information');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Navigate to the appropriate view based on task type and entity
   */
  const navigateToTaskView = async (taskId: string) => {
    const taskInfo = await getTaskNavigationInfo(taskId);

    console.log('🔍 Task Navigation Debug:', {
      taskId,
      entity_type: taskInfo?.entity_type,
      entity_id: taskInfo?.entity_id,
      task_type: taskInfo?.task_type
    });

    // If task is related to an application, navigate to evaluation
    // Check both entity_type and task_type for application/evaluation tasks
    if ((taskInfo?.entity_type === 'application' || taskInfo?.task_type === 'application' || taskInfo?.task_type === 'evaluation') && taskInfo.entity_id) {
      console.log('📱 Application task detected, fetching application details...');

      // For application tasks, we need to get the application details to build the evaluation URL
      try {
        const appResponse = await apiClient.get(`/applications/${taskInfo.entity_id}`);
        const application = appResponse.data;
        const licenseTypeCode = application.license_category?.license_type?.code;

        console.log('📋 Application details:', {
          application_id: application.application_id,
          license_category_id: application.license_category_id,
          licenseTypeCode
        });

        if (licenseTypeCode && application.application_id && application.license_category_id) {
          const evaluationUrl = `/applications/${licenseTypeCode}/evaluate/applicant-info?application_id=${application.application_id}&license_category_id=${application.license_category_id}`;
          console.log(`✅ Navigating to evaluation: ${evaluationUrl}`);
          router.push(evaluationUrl);
          return;
        } else {
          console.warn('❌ Missing required data for evaluation URL:', {
            licenseTypeCode,
            application_id: application.application_id,
            license_category_id: application.license_category_id
          });
        }
      } catch (error: any) {
        console.error('❌ Error fetching application details:', error);
        console.error('Error response:', error.response?.data);
        console.error('Error status:', error.response?.status);
      }

      // Fallback to general task view if application fetch fails
      console.log('⚠️ Falling back to task view');
      router.push(`/admin/tasks/${taskId}`);
      toast.error('Unable to load application details, showing task details');
      return;
    } else {
      console.log('ℹ️ Not an application task or missing entity_id', {
        entity_type: taskInfo?.entity_type,
        task_type: taskInfo?.task_type,
        entity_id: taskInfo?.entity_id,
        isApplicationType: taskInfo?.entity_type === 'application',
        isApplicationTaskType: taskInfo?.task_type === 'application',
        isEvaluationTaskType: taskInfo?.task_type === 'evaluation'
      });
    }

    // For other task types
    switch (taskInfo?.task_type) {
      case 'complaint':
        // Navigate to complaint management
        if (taskInfo?.entity_id) {
          router.push(`/admin/complaints/${taskInfo?.entity_id}`);
        } else {
          router.push(`/admin/tasks/${taskId}`);
        }
        break;

      case 'data_breach':
        // Navigate to data breach management
        if (taskInfo?.entity_id) {
          router.push(`/admin/data-breaches/${taskInfo?.entity_id}`);
        } else {
          router.push(`/admin/tasks/${taskId}`);
        }
        break;

      case 'inspection':
        // Navigate to inspection management
        if (taskInfo?.entity_id) {
          router.push(`/admin/inspections/${taskInfo?.entity_id}`);
        } else {
          router.push(`/admin/tasks/${taskId}`);
        }
        break;

      case 'document_review':
        // Navigate to document review
        if (taskInfo.entity_id) {
          router.push(`/admin/documents/${taskInfo.entity_id}`);
        } else {
          router.push(`/admin/tasks/${taskId}`);
        }
        break;

      default:
        // Default to task details page
        router.push(`/admin/tasks/${taskId}`);
        break;
    }
  };

  /**
   * Get the appropriate view URL for a task without navigating
   */
  const getTaskViewUrl = async (taskId: string): Promise<string | null> => {
    const taskInfo = await getTaskNavigationInfo(taskId);

    if (!taskInfo) {
      return null;
    }

    const task  = taskInfo;

    // If task is related to an application, try to build evaluation URL
    if (task.entity_type === 'application' && task.entity_id) {
      try {
        const appResponse = await apiClient.get(`/applications/${task.entity_id}`);
        const application = appResponse.data;
        const licenseTypeCode = application.license_category?.license_type?.code;

        if (licenseTypeCode) {
          return `/applications/${licenseTypeCode}/evaluate/applicant-info?application_id=${application.application_id}&license_category_id=${application.license_category_id}`;
        }
      } catch (error) {
        console.error('Error fetching application details for URL:', error);
      }

      // Fallback to task details if application fetch fails
      return `/admin/tasks/${taskId}`;
    }

    // For other task types
    switch (task.task_type) {
      case 'complaint':
        return task.entity_id ? `/admin/complaints/${task.entity_id}` : `/admin/tasks/${taskId}`;

      case 'data_breach':
        return task.entity_id ? `/admin/data-breaches/${task.entity_id}` : `/admin/tasks/${taskId}`;

      case 'inspection':
        return task.entity_id ? `/admin/inspections/${task.entity_id}` : `/admin/tasks/${taskId}`;

      case 'document_review':
        return task.entity_id ? `/admin/documents/${task.entity_id}` : `/admin/tasks/${taskId}`;

      default:
        return `/admin/tasks/${taskId}`;
    }
  };

  /**
   * Open task view in a new tab
   */
  const openTaskViewInNewTab = async (taskId: string) => {
    const url = await getTaskViewUrl(taskId);
    
    if (url) {
      window.open(url, '_blank');
    } else {
      toast.error('Unable to determine task view URL');
    }
  };

  /**
   * Get display information for task navigation
   */
  const getTaskDisplayInfo = (taskInfo: TaskNavigationInfo) => {
    const { task } = taskInfo;

    let title = task.title;
    let subtitle = '';
    let icon = '📋';

    // For application tasks, the title usually contains the application number
    if (task.entity_type === 'application') {
      icon = '📄';
      subtitle = 'Application Evaluation';
    }

    switch (task.task_type) {
      case 'application':
      case 'evaluation':
        icon = '📄';
        break;
      case 'complaint':
        icon = '⚠️';
        break;
      case 'data_breach':
        icon = '🔒';
        break;
      case 'inspection':
        icon = '🔍';
        break;
      case 'document_review':
        icon = '📑';
        break;
      default:
        icon = '📋';
        break;
    }

    return {
      title,
      subtitle,
      icon,
      taskType: task.task_type,
      status: task.status,
    };
  };

  return {
    navigateToTaskView,
    getTaskViewUrl,
    openTaskViewInNewTab,
    getTaskNavigationInfo,
    getTaskDisplayInfo,
    isLoading,
  };
};
