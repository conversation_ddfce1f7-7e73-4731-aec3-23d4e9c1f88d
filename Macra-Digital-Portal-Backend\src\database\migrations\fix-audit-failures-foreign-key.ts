import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAuditFailuresForeignKey1704718471000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log('Checking for orphaned audit_failures records...');
        
        // First, let's check how many orphaned records exist
        const orphanedCount = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM audit_failures af
            LEFT JOIN users u ON af.user_id = u.user_id
            WHERE af.user_id IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`Found ${orphanedCount[0].count} orphaned audit_failures records`);
        
        if (orphanedCount[0].count > 0) {
            console.log('Fixing orphaned records by setting user_id to NULL...');
            
            // Set orphaned user_id to NULL instead of deleting the records
            // This preserves the audit trail while fixing the constraint issue
            await queryRunner.query(`
                UPDATE audit_failures 
                SET user_id = NULL 
                WHERE user_id IS NOT NULL 
                AND user_id NOT IN (SELECT user_id FROM users)
            `);
            
            console.log('Orphaned records fixed - user_id set to NULL');
        }
        
        // Verify the fix
        const remainingOrphaned = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM audit_failures af
            LEFT JOIN users u ON af.user_id = u.user_id
            WHERE af.user_id IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`Remaining orphaned records: ${remainingOrphaned[0].count}`);
        
        if (remainingOrphaned[0].count === 0) {
            console.log('✅ Ready to add foreign key constraint');
        } else {
            throw new Error('❌ Still have orphaned records, cannot proceed with foreign key constraint');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // This migration is data cleanup, no rollback needed
        console.log('This migration is for data cleanup and cannot be rolled back');
    }
}