import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { TypeApprovedManufacturer } from 'src/entities/type_approved_manufacturer.entity';
import { CreateTypeApprovedManufacturerDto } from 'src/dto/type_approval/create.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UpdateTypeApprovedManufacturerDto } from 'src/dto/type_approval/update.dto';
import { TypeApprovedDevice } from 'src/entities/type_approved_device.entity';

@Injectable()
export class StandardsService {
  constructor(
    @InjectRepository(TypeApprovedManufacturer)
    private readonly manufacturerRepo: Repository<TypeApprovedManufacturer>,
    @InjectRepository(TypeApprovedDevice)
    private readonly deviceRepo: Repository<TypeApprovedDevice>,
  ) {}

  async createManufacturer(dto: CreateTypeApprovedManufacturerDto): Promise<TypeApprovedManufacturer> {
    const entity = this.manufacturerRepo.create(dto);
    return this.manufacturerRepo.save(entity);
  }

  async findAllManufacturers(): Promise<TypeApprovedManufacturer[]> {
    return this.manufacturerRepo.find({ order: { created_at: 'DESC' } });
  }

  async findOneManufacturer(id: string): Promise<TypeApprovedManufacturer> {
    const entity = await this.manufacturerRepo.findOne({ where: { manufacturer_id: id } });
    if (!entity) throw new NotFoundException(`Manufacturer with id ${id} not found`);
    return entity;
  }

  async updateManufacturer(
    id: string,
    dto: UpdateTypeApprovedManufacturerDto,
  ): Promise<TypeApprovedManufacturer> {
    const entity = await this.findOneManufacturer(id);
    const updated = this.manufacturerRepo.merge(entity, dto);
    return this.manufacturerRepo.save(updated);
  }

  async removeManufacturer(id: string): Promise<void> {
    const entity = await this.findOneManufacturer(id);
    await this.manufacturerRepo.softRemove(entity);
  }
}

