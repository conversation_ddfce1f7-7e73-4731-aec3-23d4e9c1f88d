'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { authService } from '@/services/auth.service';
import { XCircleIcon } from '@heroicons/react/24/solid';
import { useAuth } from '@/contexts/AuthContext';
import Loader from '@/components/Loader';

export default function CustomerSetupTwoFactorPage() {
  const router = useRouter();
  const { user, token: access_token, loading: authLoading } = useAuth();

  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [secret, setSecret] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loadingMessage, setLoadingMessage] = useState('Initializing 2FA setup...');
  const [alreadyEnabled, setAlreadyEnabled] = useState(false);
  const [setUpComplete, setSetUpComplete] = useState(false);
  const [loading, setLoading] = useState(true);
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);

  const redirectLogin = () => router.replace('/customer/auth/login');
  const redirectDashboard = () => router.push('/customer');

  useEffect(() => {
    if (authLoading) return;

    const setupUser = sessionStorage.getItem('2fa_setup_user');
    const setupToken = sessionStorage.getItem('2fa_setup_token');

    let currentUser = user;
    let currentToken = access_token;

    if (setupUser && setupToken) {
      try {
        currentUser = JSON.parse(setupUser);
        currentToken = setupToken;
        authService.setAuthToken(setupToken);
      } catch (err) {
        console.error('Failed to parse 2FA setup session data:', err);
      }
    }

    if (!currentUser || !currentToken) {
      setLoading(false);
      const hasSetupData = setupUser && setupToken;
      const hasAuthContext = user && access_token;

      if (!hasSetupData && !hasAuthContext) {
        setUnauthorizedAccess(true);
        setError('Unauthorized access. This page can only be accessed during 2FA setup after login.');
        setLoadingMessage('Please login to continue.');
      } else {
        setError('Your session has expired or is invalid. Please login again to continue.');
        setLoadingMessage('Session expired. Redirecting...');
      }

      setLoading(true);
      setTimeout(redirectLogin, 5000);
      return;
    }

    const initiate2FA = async () => {
      try {
        const { qr_code_data_url, secret, message } = await authService.setupTwoFactorAuth({
          access_token: currentToken,
          user_id: currentUser.user_id,
        });

        setQrCodeUrl(qr_code_data_url);
        setSecret(secret);
        setSuccess(message || '2FA setup successful');
        setSetUpComplete(true);

        sessionStorage.removeItem('2fa_setup_user');
        sessionStorage.removeItem('2fa_setup_token');

        setTimeout(redirectLogin, 7000);
      } catch (err: any) {
        const msg: string =
          err?.response?.data?.message ||
          err?.message ||
          'Failed to initiate 2FA setup. Redirecting...';

        const isEnabled = msg.toLowerCase().includes('enabled');
        const isInitiated = msg.toLowerCase().includes('initiation');

        setAlreadyEnabled(isEnabled);
        setSetUpComplete(isInitiated);

        if (isEnabled) {
          setSuccess(msg);
          setTimeout(redirectDashboard, 2000);
        } else {
          setError(msg);
          setTimeout(redirectLogin, 5000);
        }
      } finally {
        setLoading(false);
      }
    };

    initiate2FA();
  }, [authLoading, user, access_token]);

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={loadingMessage} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image
          src="/images/macra-logo.png"
          alt="MACRA Logo"
          width={50}
          height={50}
          className="mx-auto h-16 w-auto"
        />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {setUpComplete && !alreadyEnabled ? (
            <span className="text-green-600 dark:text-green-300">2FA Setup Complete</span>
          ) : alreadyEnabled ? (
            <span className="text-green-600 dark:text-green-300">2FA Already Enabled</span>
          ) : (
            'Two Factor Authentication Setup'
          )}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          {error && !alreadyEnabled && (
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                <XCircleIcon className="w-10 h-10 animate-pulse shadow-md text-red-600 dark:text-red-300" />
              </div>
              <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center">
                {error}
              </div>
              {unauthorizedAccess && (
                <div className="mt-4">
                  <button
                    onClick={redirectLogin}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          )}

          {(success || alreadyEnabled || setUpComplete) && (
            <div className="flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400">
              <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                <svg
                  className="w-8 h-8 text-green-600 dark:text-green-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                {success}
                {alreadyEnabled && (
                  <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                    Two-Factor Authentication is already enabled. Please contact support if you need to reset it.
                  </p>
                )}
                {setUpComplete && (
                  <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                    This link is valid for 5 minutes and can only be used once.
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
