import { PaginateQuery } from './userService';

export interface Payment {
  payment_id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  payment_type: string;
  description: string;
  status: 'PENDING' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  due_date: string;
  paid_date?: string;
  issue_date: string;
  payment_method?: string;
  transaction_reference?: string;
  notes?: string;
  entity_type?: string;
  entity_id?: string;
  user_id: string;
  created_by: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  user?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface Invoice {
  payment_id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  payment_type: string;
  description: string;
  status: 'PENDING' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  due_date: string;
  issue_date: string;
  entity_type?: string;
  entity_id?: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentFilters {
  status?: string;
  payment_type?: string;
  dateRange?: string;
  search?: string;
  entity_type?: string;
}

export interface PaymentsResponse {
  data: Payment[];
  meta: {
    itemsPerPage: number;
    totalItems: number;
    currentPage: number;
    totalPages: number;
    sortBy: string[];
    searchBy: string[];
    search: string;
    select: string[];
  };
  links: {
    current: string;
    next?: string;
    previous?: string;
    first?: string;
    last?: string;
  };
}

export interface PaymentStatistics {
  total: number;
  pending: number;
  paid: number;
  overdue: number;
  cancelled: number;
  totalAmount: number;
  pendingAmount: number;
  paidAmount: number;
  overdueAmount: number;
}

class PaymentService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('auth_token');
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getPayments(query: PaginateQuery & PaymentFilters): Promise<PaymentsResponse> {
    const params = new URLSearchParams();
    
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    if (query.search) params.append('search', query.search);
    if (query.status) params.append('status', query.status);
    if (query.payment_type) params.append('paymentType', query.payment_type);
    if (query.dateRange) params.append('dateRange', query.dateRange);
    if (query.entity_type) params.append('entityType', query.entity_type);

    return this.request<PaymentsResponse>(`/payments?${params.toString()}`);
  }

  async getInvoices(query: PaginateQuery & PaymentFilters): Promise<PaymentsResponse> {
    // For invoices, we get payments but filter for pending/unpaid ones
    const params = new URLSearchParams();
    
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    if (query.search) params.append('search', query.search);
    if (query.payment_type) params.append('paymentType', query.payment_type);
    if (query.dateRange) params.append('dateRange', query.dateRange);
    if (query.entity_type) params.append('entityType', query.entity_type);
    
    // Filter for unpaid invoices
    params.append('status', 'PENDING,OVERDUE');

    return this.request<PaymentsResponse>(`/payments?${params.toString()}`);
  }

  async getPaymentById(id: string): Promise<Payment> {
    return this.request<Payment>(`/payments/${id}`);
  }

  async getPaymentStatistics(): Promise<PaymentStatistics> {
    return this.request<PaymentStatistics>('/payments/statistics');
  }

  async getPaymentsByEntity(entityType: string, entityId: string, query: PaginateQuery): Promise<PaymentsResponse> {
    const params = new URLSearchParams();
    
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    if (query.search) params.append('search', query.search);

    return this.request<PaymentsResponse>(`/payments/entity/${entityType}/${entityId}?${params.toString()}`);
  }

  async updatePayment(id: string, data: Partial<Payment>): Promise<Payment> {
    return this.request<Payment>(`/payments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async createPayment(data: Omit<Payment, 'payment_id' | 'created_at' | 'updated_at'>): Promise<Payment> {
    return this.request<Payment>('/payments', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async deletePayment(id: string): Promise<void> {
    await this.request(`/payments/${id}`, {
      method: 'DELETE',
    });
  }

  // Proof of payment upload (if needed in the future)
  async uploadProofOfPayment(paymentId: string, file: File, data: any): Promise<any> {
    const token = localStorage.getItem('auth_token');
    const formData = new FormData();
    
    formData.append('file', file);
    Object.keys(data).forEach(key => {
      formData.append(key, data[key]);
    });

    const response = await fetch(`${this.baseUrl}/payments/${paymentId}/proof-of-payment`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }
}

export const paymentService = new PaymentService();
