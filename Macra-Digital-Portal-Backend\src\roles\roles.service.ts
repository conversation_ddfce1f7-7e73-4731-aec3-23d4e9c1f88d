import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { CreateRoleDto } from '../dto/role/create-role.dto';
import { UpdateRoleDto } from '../dto/role/update-role.dto';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private rolesRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionsRepository: Repository<Permission>,
  ) {}

  async create(createRoleDto: CreateRoleDto): Promise<Role> {
    // Check if role already exists
    const existingRole = await this.rolesRepository.findOne({
      where: { name: createRoleDto.name },
    });

    if (existingRole) {
      throw new ConflictException('Role with this name already exists');
    }

    // Get permissions if provided
    let permissions: Permission[] = [];
    if (createRoleDto.permission_ids && createRoleDto.permission_ids.length > 0) {
      permissions = await this.permissionsRepository.findBy({
        permission_id: In(createRoleDto.permission_ids),
      });
    }

    // Create role
    const role = this.rolesRepository.create({
      name: createRoleDto.name,
      description: createRoleDto.description,
      permissions,
    });

    return this.rolesRepository.save(role);
  }

  async findAll(query: PaginateQuery): Promise<PaginatedResult<Role>> {
    console.log('RolesService: findAll called with query:', JSON.stringify(query, null, 2));

    const config: PaginateConfig<Role> = {
      sortableColumns: ['name', 'created_at'],
      searchableColumns: ['name', 'description'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      relations: ['permissions', 'users'],
    };

    console.log('RolesService: Using config:', JSON.stringify(config, null, 2));

    const result = await paginate(query, this.rolesRepository, config);
    console.log('RolesService: Raw pagination result:', JSON.stringify(result, null, 2));

    const transformedResult = PaginationTransformer.transform<Role>(result);
    console.log('RolesService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));

    return transformedResult;
  }

  async findOne(id: string): Promise<Role> {
    const role = await this.rolesRepository.findOne({
      where: { role_id: id },
      relations: ['permissions', 'users'],
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async findByName(name: string): Promise<Role | null> {
    return this.rolesRepository.findOne({
      where: { name: name as any },
      relations: ['permissions'],
    });
  }

  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<Role> {
    const role = await this.findOne(id);

    // Update permissions if provided
    if (updateRoleDto.permission_ids !== undefined) {
      if (updateRoleDto.permission_ids.length > 0) {
        const permissions = await this.permissionsRepository.findBy({
          permission_id: In(updateRoleDto.permission_ids),
        });
        role.permissions = permissions;
      } else {
        role.permissions = [];
      }
    }

    // Update other fields
    if (updateRoleDto.name) role.name = updateRoleDto.name;
    if (updateRoleDto.description !== undefined) role.description = updateRoleDto.description;

    return this.rolesRepository.save(role);
  }

  async remove(id: string): Promise<void> {
    const role = await this.findOne(id);
    
    // Check if role has users
    if (role.users && role.users.length > 0) {
      throw new ConflictException('Cannot delete role that has assigned users');
    }

    await this.rolesRepository.softDelete(id);
  }

  async assignPermissions(roleId: string, permissionIds: string[]): Promise<Role> {
    const role = await this.findOne(roleId);
    const permissions = await this.permissionsRepository.findBy({
      permission_id: In(permissionIds),
    });

    role.permissions = permissions;
    return this.rolesRepository.save(role);
  }

  async removePermissions(roleId: string, permissionIds: string[]): Promise<Role> {
    const role = await this.findOne(roleId);
    
    role.permissions = role.permissions.filter(
      permission => !permissionIds.includes(permission.permission_id)
    );

    return this.rolesRepository.save(role);
  }
}
