'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { userService, User } from '../../services/userService';
import ProfileForm from '../../components/profile/ProfileForm';
import PasswordChangeForm from '../../components/profile/PasswordChangeForm';
import AvatarUpload from '../../components/profile/AvatarUpload';
import DisplayPreferences from '../../components/profile/DisplayPreferences';
import NotificationPreferences from '../../components/profile/NotificationPreferences';
import { getUserInitials } from '../../utils/imageUtils';

// Utility function to convert userService User to AuthContext UpdateUserData format
const convertUserForAuth = (user: User) => {
  // Extract role names from Role objects, fallback to empty array
  const roles = user.roles?.map(role => role.name) || [];
  
  return {
    user_id: user.user_id, // Keep as string
    email: user.email,
    first_name: user.first_name,
    last_name: user.last_name,
    middle_name: user.middle_name,
    phone: user.phone, // Can be undefined
    status: user.status, // Can be undefined
    profile_image: user.profile_image,
    roles: roles, // Array of role names
    isAdmin: roles.includes('administrator') || user.isAdmin || false
  };
};

export default function ProfilePage() {
  const { updateUser } = useAuth();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('profile');

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const profileData = await userService.getProfile();
      setUser(profileData);
    } catch (err) {
      setError('Failed to load profile');
      console.error('Error loading profile:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (updatedUser: User) => {
    console.log('ProfilePage: Handling profile update', updatedUser);
    setUser(updatedUser);
    if (updateUser) {
      console.log('ProfilePage: Calling AuthContext updateUser');
      // Convert the userService User to the format expected by AuthContext
      const authUser = convertUserForAuth(updatedUser);
      updateUser(authUser);
    } else {
      console.warn('ProfilePage: updateUser function not available');
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile Information', icon: 'ri-user-line' },
    { id: 'security', label: 'Security', icon: 'ri-shield-line' },
    { id: 'avatar', label: 'Profile Picture', icon: 'ri-image-line' },
    { id: 'preferences', label: 'Display Preferences', icon: 'ri-palette-line' },
  ];

  if (loading) {
    return (
      <div className="flex-1 overflow-y-auto bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 overflow-y-auto bg-gray-50 p-6">
        <div className="max-w-md mx-auto">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <div className="flex">
              <i className="ri-error-warning-line text-red-400 mr-2"></i>
              <span>{error}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
      {/* Header Section */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="md:flex md:items-center md:justify-between">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:text-3xl sm:truncate">
                  Profile Settings
                </h1>
                <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
                  <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <i className="ri-user-line mr-1.5 h-5 w-5 text-gray-400"></i>
                    Manage your account settings and preferences
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 px-4 sm:px-6 lg:px-8 py-4">
        <div className="max-w-6xl mx-auto">
          {/* Profile Header Card */}
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-4">
            <div className="bg-gradient-to-r from-red-500 to-red-600 px-6 py-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-20 w-20 rounded-full border-4 border-white overflow-hidden bg-white">
                    {user?.profile_image ? (
                      <img
                        className="h-full w-full object-cover"
                        src={user.profile_image}
                        alt={`${user.first_name} ${user.last_name}`}
                      />
                    ) : (
                      <div className="h-full w-full bg-red-600 flex items-center justify-center">
                        <span className="text-2xl font-bold text-white">
                          {getUserInitials(user?.first_name, user?.last_name)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="ml-6">
                  <h2 className="text-2xl font-bold text-white">
                    {user?.first_name} {user?.last_name}
                  </h2>
                  <p className="text-red-100">{user?.email}</p>
                  <div className="mt-2 flex items-center">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white">
                      <i className="ri-shield-user-line mr-1"></i>
                      {/* {user?.role?.name?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'No Role'} */}
                    </span>
                    <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <i className="ri-check-line mr-1"></i>
                      {/* {user?.status?.charAt(0).toUpperCase() + user?.status?.slice(1)} */}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-calendar-line text-gray-400 text-lg"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Member Since</p>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{new Date(user?.created_at || '').toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-time-line text-gray-400 text-lg"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Login</p>
                    <p className="text-sm text-gray-900 dark:text-gray-100">
                      {user?.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-phone-line text-gray-400 text-lg"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</p>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{user?.phone}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                      activeTab === tab.id
                        ? 'border-red-500 text-red-600 dark:text-red-400'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    aria-current={activeTab === tab.id ? 'page' : undefined}
                  >
                    <div className="flex items-center space-x-2">
                      <i className={`${tab.icon} text-lg`}></i>
                      <span>{tab.label}</span>
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-4 lg:p-6">
              {activeTab === 'profile' && user && (
                <div className="max-w-4xl">
                  <ProfileForm
                    user={user}
                    onUpdate={handleProfileUpdate}
                  />
                </div>
              )}

              {activeTab === 'security' && user && (
                <div className="max-w-2xl">
                  <PasswordChangeForm
                    userId={user.user_id}
                  />
                </div>
              )}

              {activeTab === 'avatar' && user && (
                <div className="max-w-2xl">
                  <AvatarUpload
                    user={user}
                    onUpdate={handleProfileUpdate}
                  />
                </div>
              )}

              {activeTab === 'preferences' && (
                <div className="max-w-4xl">
                  <DisplayPreferences />
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Bottom spacing to ensure content is not cut off */}
        <div className="h-8"></div>
      </div>
    </div>
  );
}
