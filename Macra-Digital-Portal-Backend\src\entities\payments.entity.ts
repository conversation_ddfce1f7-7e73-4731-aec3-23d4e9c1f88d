import {
  <PERSON><PERSON>ty,
  Column,
  <PERSON>reateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsNumber, IsDateString, Min, Length } from 'class-validator';
import { User } from './user.entity';
import { Applications } from './applications.entity';
import { Licenses } from './licenses.entity';
import { Applicants } from './applicant.entity';

export enum TransactionType {
  APPLICATION_FEE = 'application_fee',
  LICENSE_FEE = 'license_fee',
  RENEWAL_FEE = 'renewal_fee',
  PENALTY = 'penalty',
  REFUND = 'refund',
}

export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  MOBILE_MONEY = 'mobile_money',
  CASH = 'cash',
  CHEQUE = 'cheque',
  ONLINE = 'online',
}

@Entity('payments')
export class Payments {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  payment_id: string;

  @Column({ type: 'varchar', unique: true })
  @IsString()
  @Length(1, 255)
  transaction_number: string;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  application_id?: string;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  license_id?: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  applicant_id: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  @IsEnum(TransactionType)
  transaction_type: TransactionType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  amount: number;

  @Column({ type: 'varchar', length: 3, default: 'MWK' })
  @IsString()
  @Length(3, 3)
  currency: string;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
    nullable: true,
  })
  payment_method?: PaymentMethod;

  @Column({ type: 'varchar', length: 255, nullable: true })
  reference_number?: string;

  @Column({ type: 'text' })
  description: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at?: Date;

  // Relations
  @ManyToOne(() => Applications, { nullable: true })
  @JoinColumn({ name: 'application_id' })
  application?: Applications;

  @ManyToOne(() => Licenses, { nullable: true })
  @JoinColumn({ name: 'license_id' })
  license?: Licenses;

  @ManyToOne(() => Applicants)
  @JoinColumn({ name: 'applicant_id' })
  applicant: Applicants;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.payment_id) {
      this.payment_id = uuidv4();
    }
  }
}
