'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { authService } from '@/services/auth.service';
import {
  XCircleIcon,
} from '@heroicons/react/24/solid';
import { useAuth } from '@/contexts/AuthContext';
import Loader from '@/components/Loader';

export default function SetupTwoFactorPage() {
  const router = useRouter();
  const { user, token: access_token, loading: authLoading } = useAuth();

  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [secret, setSecret] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [message, setMessage] = useState('');
  const [loadingMessage, setLoadingMessage] = useState('Initializing 2FA setup...');
  const [alreadyEnabled, setAlreadyEnabled] = useState(false);
  const [setUp, setSetUp] = useState(false);
  const [loading, setLoading] = useState(true);
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);

  useEffect(() => {
    if (authLoading) return;

    // Check for 2FA setup session data first
    const setupUser = sessionStorage.getItem('2fa_setup_user');
    const setupToken = sessionStorage.getItem('2fa_setup_token');

    let currentUser = user;
    let currentToken = access_token;

    // If we have setup session data, use that instead
    if (setupUser && setupToken) {
      try {
        currentUser = JSON.parse(setupUser);
        currentToken = setupToken;

        // Set the token in localStorage so the API client can access it
        authService.setAuthToken(setupToken);
      } catch (error) {
        console.error('Failed to parse 2FA setup session data:', error);
      }
    }

    if (!currentUser || !currentToken) {
      setLoading(false);

      // Check if this is a direct access (no session data and no auth context)
      const hasSessionData = setupUser && setupToken;
      const hasAuthContext = user && access_token;

      if (!hasSessionData && !hasAuthContext) {
        // Direct access to the route - show comprehensive error
        setUnauthorizedAccess(true);
        setError('Unauthorized access. This page can only be accessed during the 2FA setup process after login.');
        setLoadingMessage('Please login first to access 2FA setup.');
        setLoading(true);
      } else {
        // Session expired or corrupted
        setError('Your session has expired or is invalid. Please login again to continue with 2FA setup.');
        setLoadingMessage('Session expired. Redirecting to login...');
        setLoading(true);
      }

      setTimeout(() => {
        router.replace('/auth/login');
      }, 5000);
      return;
    }

    const initiate2FA = async () => {
      console.log('Now setting up 2FA');
      try {
        const { qr_code_data_url, secret, message } = await authService.setupTwoFactorAuth({
          access_token: currentToken,
          user_id: currentUser.user_id,
        });

        setQrCodeUrl(qr_code_data_url);
        setSecret(secret);
        setSuccess(message || 'Two Factor Authentication setup successful');
        setLoading(false);

        // Clear session storage after successful setup
        sessionStorage.removeItem('2fa_setup_user');
        sessionStorage.removeItem('2fa_setup_token');
      } catch (err: any) {
        const message: string =
          err?.response?.data?.message ||
          err?.message ||
          'Failed to initiate 2FA setup. Redirecting to login...';

        const isAlreadyEnabled = message.toLowerCase().includes('enabled');
        const isSetUp = message.toLowerCase().includes('initiation');

        setAlreadyEnabled(isAlreadyEnabled);
        setSetUp(isSetUp);

        if (isAlreadyEnabled) {
          setSuccess(message);
          setTimeout(() => {
            router.push('/dashboard');
          }, 7000);
          return;
        }

        setError(message);
        setTimeout(() => {
          router.replace('/auth/login');
        }, 7000);
      }
    };

    initiate2FA();
  }, [user, access_token, authLoading]);

  // Update derived state when success message changes
  useEffect(() => {
    setAlreadyEnabled(success.toLowerCase().includes('enabled'));
    setSetUp(success.toLowerCase().includes('initiation'));
  }, [success]);

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={loadingMessage} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image
          src="/images/macra-logo.png"
          alt="MACRA Logo"
          width={50}
          height={50}
          className="mx-auto h-16 w-auto"
        />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {setUp && !alreadyEnabled && (
            <span className="text-green-600 dark:text-green-300">Success</span>
          )}
          {!setUp && alreadyEnabled && (
            <span className="text-green-600 dark:text-green-300">
              Two Factor Authentication Enabled
            </span>
          )}
          {!setUp && !alreadyEnabled && 'Two Factor Authentication Setup'}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          {error && !alreadyEnabled && (
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                <XCircleIcon className="w-10 h-10 animate-pulse shadow-md text-red-600 dark:text-red-300" />
              </div>
              <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center">
                {error}
              </div>
              {unauthorizedAccess && (
                <div className="mt-4">
                  <button
                    onClick={() => router.replace('/auth/login')}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          )}

          {(success || alreadyEnabled || setUp) && (
            <div className="flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400">
              <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                <svg
                  className="w-8 h-8 text-green-600 dark:text-green-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                {success}
                {alreadyEnabled && (
                  <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                    Two-Factor Authentication is already enabled. Please contact support to reset.
                  </p>
                )}
                {setUp && (
                  <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                    This link is valid for 5 minutes and can only be used once.
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
