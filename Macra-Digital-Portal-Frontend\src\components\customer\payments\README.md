# Customer Payments UI Implementation

This document describes the implementation of the customer payments UI with separate tabs for invoices and payments, integrated with the backend API.

## Overview

The customer payments page has been redesigned to follow the same pattern as the user management system, with independent tabs for different functionalities:

1. **Invoices Tab** - Shows outstanding invoices (pending/overdue payments)
2. **Payments Tab** - Shows complete payment history

## Components Structure

### 1. PaymentService (`/services/paymentService.ts`)
- Handles all API communication with the backend payments endpoints
- Provides methods for fetching payments, invoices, and statistics
- Includes proper TypeScript interfaces for type safety
- Supports filtering, pagination, and search functionality

**Key Methods:**
- `getPayments()` - Fetch all payments with filters
- `getInvoices()` - Fetch outstanding invoices (pending/overdue)
- `getPaymentById()` - Get specific payment details
- `getPaymentStatistics()` - Get payment statistics
- `updatePayment()` - Update payment information
- `uploadProofOfPayment()` - Upload proof of payment files

### 2. PaymentTabs Component (`/components/customer/payments/PaymentTabs.tsx`)
- Reusable tab component similar to UserTabs
- Handles tab navigation and content switching
- Supports icons and proper accessibility

### 3. InvoicesTab Component (`/components/customer/payments/InvoicesTab.tsx`)
- Displays outstanding invoices (pending/overdue payments)
- Includes filtering by status, payment type, and date range
- Provides actions: View, Pay Now, Download
- Uses DataTable component for consistent UI

**Features:**
- Status filtering (Pending, Overdue)
- Payment type filtering (License Fee, Application Fee, etc.)
- Date range filtering (Last 30 days, 90 days, year)
- Search functionality
- Pagination support
- Color-coded status and type indicators

### 4. PaymentsTab Component (`/components/customer/payments/PaymentsTab.tsx`)
- Displays complete payment history
- Includes all payment statuses (Paid, Pending, Overdue, Cancelled)
- Provides actions: View, Upload Proof, Download
- Shows payment method and paid date information

**Features:**
- Full status filtering (All statuses)
- Payment type filtering
- Date range filtering
- Search functionality
- Pagination support
- Upload proof of payment functionality

### 5. Main Payments Page (`/app/customer/payments/page.tsx`)
- Orchestrates the tab system
- Handles authentication and loading states
- Provides callback handlers for actions
- Clean, simplified structure

## Backend Integration

### API Endpoints Used
- `GET /payments` - Get all payments with filters
- `GET /payments/statistics` - Get payment statistics
- `GET /payments/:id` - Get specific payment
- `PUT /payments/:id` - Update payment
- `POST /payments/:id/proof-of-payment` - Upload proof of payment
- `GET /payments/entity/:entityType/:entityId` - Get payments for specific entity

### Filtering Support
- **Status**: PENDING, PAID, OVERDUE, CANCELLED
- **Payment Type**: LICENSE_FEE, APPLICATION_FEE, PROCUREMENT_FEE, RENEWAL_FEE, PENALTY_FEE
- **Date Range**: last-30, last-90, last-year
- **Search**: By invoice number, description, payment type
- **Entity Type**: Filter by related entity (application, license, etc.)

## Features

### 1. Invoices Tab
- Shows only unpaid invoices (PENDING, OVERDUE)
- "Pay Now" button for pending/overdue invoices
- Clear due date highlighting
- Download invoice functionality

### 2. Payments Tab
- Shows complete payment history
- Payment method information
- Paid date tracking
- Upload proof of payment for pending payments
- Transaction reference tracking

### 3. Common Features
- **Responsive Design**: Works on desktop and mobile
- **Dark Mode Support**: Full dark theme compatibility
- **Loading States**: Proper loading indicators
- **Error Handling**: User-friendly error messages
- **Pagination**: Efficient data loading
- **Search & Filter**: Advanced filtering options
- **Color Coding**: Visual status and type indicators

## Usage Examples

### Viewing Invoices
```typescript
// The InvoicesTab automatically filters for pending/overdue payments
const invoices = await paymentService.getInvoices({
  page: 1,
  limit: 10,
  status: 'PENDING,OVERDUE'
});
```

### Viewing Payment History
```typescript
// The PaymentsTab shows all payment records
const payments = await paymentService.getPayments({
  page: 1,
  limit: 10,
  search: 'INV-2024'
});
```

### Filtering by Entity
```typescript
// Get payments for a specific application
const applicationPayments = await paymentService.getPaymentsByEntity(
  'application',
  'app-uuid',
  { page: 1, limit: 10 }
);
```

## Styling

The components use Tailwind CSS classes and follow the existing design system:
- Consistent with other admin/customer pages
- Proper spacing and typography
- Color-coded status indicators
- Responsive grid layouts
- Hover effects and transitions

## Future Enhancements

1. **Payment Processing**: Integration with payment gateways
2. **Bulk Actions**: Select multiple invoices for bulk payment
3. **Export Functionality**: Export payment history to CSV/PDF
4. **Payment Reminders**: Automated reminder system
5. **Payment Plans**: Support for installment payments
6. **Receipt Generation**: Automatic receipt generation
7. **Payment Analytics**: Charts and graphs for payment trends

## Testing

To test the implementation:

1. **Authentication**: Ensure user is logged in as customer
2. **API Connection**: Verify backend API is running and accessible
3. **Data Loading**: Check that payments and invoices load correctly
4. **Filtering**: Test all filter combinations
5. **Pagination**: Verify pagination works with large datasets
6. **Actions**: Test view, pay, and download actions
7. **Responsive**: Test on different screen sizes
8. **Error Handling**: Test with network errors and empty states

## Dependencies

- React 18+
- Next.js 13+
- Tailwind CSS
- TypeScript
- DataTable component
- Select component
- CustomerLayout component
- Authentication context
