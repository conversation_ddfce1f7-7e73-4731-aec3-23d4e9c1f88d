/* Performance optimizations for customer portal */

/* Optimize page transitions */
.page-transitioning {
  pointer-events: none;
}

.page-transitioning * {
  transition: none !important;
  animation: none !important;
}

/* Optimize scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Optimize images */
img {
  content-visibility: auto;
}

/* Optimize large lists */
.virtual-list {
  contain: layout style paint;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize rendering */
.customer-layout {
  contain: layout;
}

.customer-sidebar {
  contain: layout style;
}

.customer-main {
  contain: layout;
}

/* Optimize hover states */
.nav-item {
  will-change: background-color, color;
}

.nav-item:hover {
  transform: translateZ(0);
}

/* Optimize cards */
.dashboard-card {
  contain: layout style paint;
  will-change: transform;
}

.dashboard-card:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Optimize buttons */
.btn-primary {
  will-change: background-color, transform;
}

.btn-primary:hover {
  transform: translateZ(0);
}

/* Critical CSS for above-the-fold content */
.critical-content {
  contain: layout style paint;
}

/* Optimize table rendering */
.data-table {
  contain: layout;
  table-layout: fixed;
}

/* Optimize form rendering */
.form-container {
  contain: layout style;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Optimize modal rendering */
.modal-overlay {
  backdrop-filter: blur(4px);
  will-change: opacity;
}

.modal-content {
  contain: layout style paint;
  will-change: transform, opacity;
}

/* Optimize dropdown menus */
.dropdown-menu {
  contain: layout style paint;
  will-change: opacity, transform;
}

/* Optimize sidebar */
.sidebar-nav {
  contain: layout style;
}

/* Optimize main content area */
.main-content {
  contain: layout;
  isolation: isolate;
}/* Performance optimizations for customer portal */

/* Optimize page transitions */
.page-transitioning {
  pointer-events: none;
}

.page-transitioning * {
  transition: none !important;
  animation: none !important;
}

/* Optimize scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Optimize images */
img {
  content-visibility: auto;
}

/* Optimize large lists */
.virtual-list {
  contain: layout style paint;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize rendering */
.customer-layout {
  contain: layout;
}

.customer-sidebar {
  contain: layout style;
}

.customer-main {
  contain: layout;
}

/* Optimize hover states */
.nav-item {
  will-change: background-color, color;
}

.nav-item:hover {
  transform: translateZ(0);
}

/* Optimize cards */
.dashboard-card {
  contain: layout style paint;
  will-change: transform;
}

.dashboard-card:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Optimize buttons */
.btn-primary {
  will-change: background-color, transform;
}

.btn-primary:hover {
  transform: translateZ(0);
}

/* Critical CSS for above-the-fold content */
.critical-content {
  contain: layout style paint;
}

/* Optimize table rendering */
.data-table {
  contain: layout;
  table-layout: fixed;
}

/* Optimize form rendering */
.form-container {
  contain: layout style;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Optimize modal rendering */
.modal-overlay {
  backdrop-filter: blur(4px);
  will-change: opacity;
}

.modal-content {
  contain: layout style paint;
  will-change: transform, opacity;
}

/* Optimize dropdown menus */
.dropdown-menu {
  contain: layout style paint;
  will-change: opacity, transform;
}

/* Optimize sidebar */
.sidebar-nav {
  contain: layout style;
}

/* Optimize main content area */
.main-content {
  contain: layout;
  isolation: isolate;
}