<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Documents - Customer Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      /* Modal styles */
      body.overflow-hidden {
        overflow: hidden;
      }

      .modal-open {
        overflow: hidden;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="my-licenses.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              My Licenses
            </a>
            <a
              href="new-application.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-list-3-line"></i>
              </div>
              New Application
            </a>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
            <a
              href="request-resource.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-hand-heart-line"></i>
              </div>
              Request Resource
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div class="flex-1 flex items-center justify-between">
              <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
                <h1 class="text-xl font-medium text-gray-900">Document Management</h1>
              </div>
              <div class="flex items-center">
                <button
                  type="button"
                  class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
                >
                  <span class="sr-only">View notifications</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-notification-3-line ri-lg"></i>
                  </div>
                  <span
                    class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                  ></span>
                </button>
                <div class="dropdown relative">
                  <button
                    type="button"
                    onclick="toggleDropdown()"
                    class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Open user menu</span>
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdown"
                    class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                  >
                    <div class="py-1">
                      <a
                        href="profile.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Your Profile</a
                      >
                      <a
                        href="account-settings.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Settings</a
                      >
                      <a
                        href="../auth/login.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Sign out</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header with breadcrumbs -->
            <div class="mb-6">
              <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                  <li>
                    <a href="../index.html" class="text-gray-500 hover:text-gray-700 text-sm">
                      <div class="w-4 h-4 flex items-center justify-center">
                        <i class="ri-home-line"></i>
                      </div>
                      <span class="sr-only">Home</span>
                    </a>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                      </svg>
                      <span class="ml-2 text-sm font-medium text-gray-900">Documents</span>
                    </div>
                  </li>
                </ol>
              </nav>
              
              <div class="mt-4 flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">My Documents</h1>
                <button 
                  onclick="openUploadModal()"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-upload-line"></i>
                  </div>
                  Upload New Document
                </button>
              </div>
            </div>

            <!-- Document Categories -->
            <div class="mb-8">
              <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                  <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Document Categories</h3>
                  
                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    <!-- License Documents -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-primary hover:shadow-md transition-all">
                      <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                          <i class="ri-file-list-3-line text-blue-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-gray-900">License Documents</h4>
                      </div>
                      <p class="text-xs text-gray-500 mb-3">Official license certificates and related documentation</p>
                      <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-gray-500">12 documents</span>
                        <a href="#" class="text-xs font-medium text-primary hover:text-primary-dark">View all</a>
                      </div>
                    </div>
                    
                    <!-- Application Documents -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-primary hover:shadow-md transition-all">
                      <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                          <i class="ri-file-paper-2-line text-green-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-gray-900">Application Documents</h4>
                      </div>
                      <p class="text-xs text-gray-500 mb-3">Documents submitted with license applications</p>
                      <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-gray-500">8 documents</span>
                        <a href="#" class="text-xs font-medium text-primary hover:text-primary-dark">View all</a>
                      </div>
                    </div>
                    
                    <!-- Compliance Documents -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-primary hover:shadow-md transition-all">
                      <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                          <i class="ri-file-shield-2-line text-purple-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-gray-900">Compliance Documents</h4>
                      </div>
                      <p class="text-xs text-gray-500 mb-3">Regulatory compliance and reporting documents</p>
                      <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-gray-500">5 documents</span>
                        <a href="#" class="text-xs font-medium text-primary hover:text-primary-dark">View all</a>
                      </div>
                    </div>
                    
                    <!-- Payment Documents -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-primary hover:shadow-md transition-all">
                      <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                          <i class="ri-bill-line text-yellow-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-gray-900">Payment Documents</h4>
                      </div>
                      <p class="text-xs text-gray-500 mb-3">Invoices, receipts, and payment confirmations</p>
                      <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-gray-500">7 documents</span>
                        <a href="#" class="text-xs font-medium text-primary hover:text-primary-dark">View all</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Recent Documents -->
            <div class="mb-8">
              <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                  <h3 class="text-lg font-medium leading-6 text-gray-900">Documents</h3>
                  <div class="relative">
                    <input type="text" placeholder="Search documents..." class="enhanced-input py-2 px-3 text-sm" />
                  </div>
                </div>
                <div class="border-t border-gray-200">
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document Name</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Uploaded</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Document 1 -->
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-blue-100 rounded-md">
                                <i class="ri-file-pdf-line text-blue-600"></i>
                              </div>
                              <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">License_Certificate_2023.pdf</div>
                                <div class="text-xs text-gray-500">Related to: License #MAC-2023-0042</div>
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">License</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 12, 2023</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1.2 MB</td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                              <button class="text-primary hover:text-primary-dark">
                                <i class="ri-download-line"></i>
                              </button>
                              <button class="text-gray-500 hover:text-gray-700">
                                <i class="ri-eye-line"></i>
                              </button>
                              
                            </div>
                          </td>
                        </tr>
                        
                        <!-- Document 2 -->
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-green-100 rounded-md">
                                <i class="ri-file-pdf-line text-green-600"></i>
                              </div>
                              <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">Application_Form_Completed.pdf</div>
                                <div class="text-xs text-gray-500">Related to: Application #APP-2023-0078</div>
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Application</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">April 28, 2023</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3.5 MB</td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                              <button class="text-primary hover:text-primary-dark">
                                <i class="ri-download-line"></i>
                              </button>
                              <button class="text-gray-500 hover:text-gray-700">
                                <i class="ri-eye-line"></i>
                              </button>
                              
                            </div>
                          </td>
                        </tr>
                        
                        <!-- Document 3 -->
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-yellow-100 rounded-md">
                                <i class="ri-file-pdf-line text-yellow-600"></i>
                              </div>
                              <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">Invoice_MAY2023.pdf</div>
                                <div class="text-xs text-gray-500">Related to: Payment #PAY-2023-0056</div>
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Payment</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 1, 2023</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0.8 MB</td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                              <button class="text-primary hover:text-primary-dark">
                                <i class="ri-download-line"></i>
                              </button>
                              <button class="text-gray-500 hover:text-gray-700">
                                <i class="ri-eye-line"></i>
                              </button>
                              
                            </div>
                          </td>
                        </tr>
                        
                        <!-- Document 4 -->
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-purple-100 rounded-md">
                                <i class="ri-file-pdf-line text-purple-600"></i>
                              </div>
                              <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">Compliance_Report_Q1_2023.pdf</div>
                                <div class="text-xs text-gray-500">Related to: License #MAC-2023-0042</div>
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Compliance</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">April 15, 2023</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2.4 MB</td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending Review</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                              <button class="text-primary hover:text-primary-dark">
                                <i class="ri-download-line"></i>
                              </button>
                              <button class="text-gray-500 hover:text-gray-700">
                                <i class="ri-eye-line"></i>
                              </button>
                              
                            </div>
                          </td>
                        </tr>
                        
                        <!-- Document 5 -->
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-red-100 rounded-md">
                                <i class="ri-file-word-line text-red-600"></i>
                              </div>
                              <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">Technical_Specifications.docx</div>
                                <div class="text-xs text-gray-500">Related to: Application #APP-2023-0078</div>
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Application</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">April 22, 2023</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1.7 MB</td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                              <button class="text-primary hover:text-primary-dark">
                                <i class="ri-download-line"></i>
                              </button>
                              <button class="text-gray-500 hover:text-gray-700">
                                <i class="ri-eye-line"></i>
                              </button>
                              
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <div class="flex items-center justify-between">
                      <div class="flex-1 flex justify-between sm:hidden">
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                        <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                      </div>
                      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                          <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">32</span> documents
                          </p>
                        </div>
                        <div>
                          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                              <span class="sr-only">Previous</span>
                              <i class="ri-arrow-left-s-line"></i>
                            </a>
                            <a href="#" aria-current="page" class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">2</a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">3</a>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">7</a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                              <span class="sr-only">Next</span>
                              <i class="ri-arrow-right-s-line"></i>
                            </a>
                          </nav>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>
        </main>
      </div>
    </div>

    <!-- Upload Document Modal -->
    <div id="uploadDocumentModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  Upload New Document
                </h3>
                <div class="mt-4">
                  <form id="uploadDocumentForm" class="space-y-6">
                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                      <!-- Document Name -->
                      <div class="sm:col-span-3">
                        <label for="documentName" class="block text-sm font-medium text-gray-700">Document Name</label>
                        <div class="mt-1">
                          <input type="text" name="documentName" id="documentName"  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"  placeholder="Enter document name">
                        </div>
                      </div>

                      <!-- Document Category -->
                      <div class="sm:col-span-3">
                        <label for="documentCategory" class="block text-sm font-medium text-gray-700">Category</label>
                        <div class="mt-1">
                          <select id="documentCategory" name="documentCategory"  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" >
                            <option value="">Select a category</option>
                            <option value="license">License Document</option>
                            <option value="application">Application Document</option>
                            <option value="compliance">Compliance Document</option>
                            <option value="payment">Payment Document</option>
                            <option value="other">Other</option>
                          </select>
                        </div>
                      </div>

                      <!-- Related License -->
                      <div class="sm:col-span-3">
                        <label for="relatedLicense" class="block text-sm font-medium text-gray-700">Related License (Optional)</label>
                        <div class="mt-1">
                          <select id="relatedLicense" name="relatedLicense"  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" >
                            <option value="">Select a license</option>
                            <option value="MAC-2023-0042">MAC-2023-0042 (Standard License)</option>
                            <option value="MAC-2023-0078">MAC-2023-0078 (Premium License)</option>
                            <option value="MAC-2022-0156">MAC-2022-0156 (Enterprise License)</option>
                          </select>
                        </div>
                      </div>

                      <!-- Document Date -->
                      <div class="sm:col-span-3">
                        <label for="documentDate" class="block text-sm font-medium text-gray-700">Document Date</label>
                        <div class="mt-1">
                          <input type="date" name="documentDate" id="documentDate"  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" >
                        </div>
                      </div>

                      <!-- Document Description -->
                      <div class="sm:col-span-6">
                        <label for="documentDescription" class="block text-sm font-medium text-gray-700">Description (Optional)</label>
                        <div class="mt-1">
                          <textarea id="documentDescription" name="documentDescription" rows="3"  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"  placeholder="Enter a brief description of this document"></textarea>
                        </div>
                      </div>

                      <!-- File Upload -->
                      <div class="sm:col-span-6">
                        <label class="block text-sm font-medium text-gray-700">Upload Document</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                          <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                              <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-primary hover:text-primary-dark focus-within:outline-none">
                                <span>Upload a file</span>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only">
                              </label>
                              <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, DOC, DOCX, JPG, PNG up to 10MB</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" onclick="document.getElementById('uploadDocumentForm').submit();" class="enhanced-button sm:ml-3 sm:w-auto">
              Upload Document
            </button>
            <button type="button" onclick="closeUploadModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      function toggleDropdown() {
        document.getElementById("userDropdown").classList.toggle("show");
      }

      function toggleMobileSidebar() {
        document.getElementById("sidebar").classList.toggle("mobile-sidebar-open");
        document.getElementById("mobileSidebarOverlay").classList.toggle("show");
      }

      function openUploadModal() {
        document.getElementById("uploadDocumentModal").classList.remove("hidden");
        document.body.classList.add("overflow-hidden");
      }

      function closeUploadModal() {
        document.getElementById("uploadDocumentModal").classList.add("hidden");
        document.body.classList.remove("overflow-hidden");
      }

      // Close the dropdown if the user clicks outside of it
      window.onclick = function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button')) {
          var dropdowns = document.getElementsByClassName("dropdown-content");
          for (var i = 0; i < dropdowns.length; i++) {
            var openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }

        // Close mobile sidebar when clicking on overlay
        if (event.target.matches('#mobileSidebarOverlay')) {
          document.getElementById("sidebar").classList.remove("mobile-sidebar-open");
          document.getElementById("mobileSidebarOverlay").classList.remove("show");
        }

        // Close modal when clicking on the background overlay
        if (event.target.classList.contains('bg-opacity-75') || 
            (event.target.id === 'uploadDocumentModal' && !event.target.closest('.bg-white'))) {
          closeUploadModal();
        }
      }
    </script>
  </body>
</html>