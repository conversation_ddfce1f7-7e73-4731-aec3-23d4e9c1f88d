import { Response } from 'express';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { CreateProofOfPaymentDto, UpdateProofOfPaymentStatusDto } from './dto/create-proof-of-payment.dto';
import { PaymentStatus, PaymentType } from './entities/payment.entity';
import { ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class PaymentsController {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    create(createPaymentDto: CreatePaymentDto): Promise<import("./entities/payment.entity").Payment>;
    findAll(query: PaginateQuery, status?: PaymentStatus, paymentType?: PaymentType, dateRange?: 'last-30' | 'last-90' | 'last-year', search?: string, req?: any): Promise<PaginatedResult<any>>;
    getStatistics(req: any): Promise<{
        totalPayments: number;
        paidPayments: number;
        pendingPayments: number;
        overduePayments: number;
        totalAmount: number;
        paidAmount: number;
        pendingAmount: number;
    }>;
    findOne(id: string): Promise<import("./entities/payment.entity").Payment>;
    update(id: string, updatePaymentDto: UpdatePaymentDto): Promise<import("./entities/payment.entity").Payment>;
    remove(id: string): Promise<{
        message: string;
    }>;
    uploadProofOfPayment(paymentId: string, file: Express.Multer.File, createProofOfPaymentDto: CreateProofOfPaymentDto, req: any): Promise<any>;
    getProofOfPayments(query: PaginateQuery, status?: ProofOfPaymentStatus, paymentId?: string, req?: any): Promise<PaginatedResult<any>>;
    getProofOfPayment(id: string): Promise<import("./entities/proof-of-payment.entity").ProofOfPayment>;
    updateProofOfPaymentStatus(id: string, updateStatusDto: UpdateProofOfPaymentStatusDto, req: any): Promise<import("./entities/proof-of-payment.entity").ProofOfPayment>;
    downloadProofOfPayment(id: string, req: any, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    markOverduePayments(): Promise<void>;
}
