import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { PaymentStatus, PaymentType } from './entities/payment.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class PaymentsController {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    create(createPaymentDto: CreatePaymentDto): Promise<import("./entities/payment.entity").Payment>;
    findAll(query: PaginateQuery, status?: PaymentStatus, paymentType?: PaymentType, dateRange?: 'last-30' | 'last-90' | 'last-year', search?: string, req?: any): Promise<PaginatedResult<any>>;
    getStatistics(req: any): Promise<{
        totalPayments: number;
        paidPayments: number;
        pendingPayments: number;
        overduePayments: number;
        totalAmount: number;
        paidAmount: number;
        pendingAmount: number;
    }>;
    findOne(id: string): Promise<import("./entities/payment.entity").Payment>;
    update(id: string, updatePaymentDto: UpdatePaymentDto): Promise<import("./entities/payment.entity").Payment>;
    remove(id: string): Promise<{
        message: string;
    }>;
    markOverduePayments(): Promise<void>;
    getPaymentsByEntity(entityType: string, entityId: string, query: PaginateQuery): Promise<PaginatedResult<any>>;
    createPaymentForEntity(entityType: string, entityId: string, createPaymentDto: Omit<CreatePaymentDto, 'entity_type' | 'entity_id'>): Promise<import("./entities/payment.entity").Payment>;
}
