@startuml
participant "Client/ Customer" as Applicant
participant "Digital Portal" as System
participant "MACRA Staff" as Macra_Staff

title Standards Department Application Submission
Applicant -> System : Access Dashboard
Applicant -> System : Selects Type Approval or Short Code (REQ_ST01)
Applicant -> System : Uploads Documents (REQ_ST02)
Applicant -> System : Submits Request
Applicant -> System : Tracks Application Status

System -> System : Validate Submission
alt Submission Valid?
    System -> System : Save Submission to Cloud (REQ_ST11)
    System -> Macra_Staff : Route to Macra Staff (REQ_ST03)
    System -> Macra_Staff : Assign Officer & Notify (REQ_ST04)

    Macra_Staff -> System : Review Submission
    alt SLA Time Exceeded?
        System -> Macra_Staff : Send SLA Alert
    end

    alt Submission Approved?
        System -> System : Update Status to "Approved"
        System -> Applicant : Notify Approval (REQ_ST10)

        alt Type Approval?
            System -> Applicant : Generate & Send Invoice (REQ_ST05)
        end

        Applicant -> System : Upload Payment Proof / Bank API (REQ_ST06)
        Macra_Staff -> System : Validate Payment & Issue Receipt (REQ_ST07)

        alt Payment Validated?
            System -> System : Update Status "Payment Validated"
            System -> Applicant : Notify Payment Confirmation (REQ_ST10)
            Macra_Staff -> System : Generate Certificate / Approval Letter (REQ_ST08)
            Macra_Staff -> System : Route to DG for Signature (REQ_ST09)
            System -> Applicant : Dispatch Signed Document (REQ_ST10)
            System -> System : Log for Reports (REQ_ST11)
            Applicant -> System : View Status & Certificate
        else
            System -> Applicant : Notify Payment Failure (REQ_ST10)
            Applicant -> System : Retry Payment (REQ_ST06)
        end

    else
        alt Pending More Info?
            System -> Applicant : Status "Pending Info" (REQ_ST10)
            Applicant -> System : Upload Additional Docs (REQ_ST02)
        else
            System -> Applicant : Status "Rejected" (REQ_ST10)
            System -> System : Log Rejection (REQ_ST11)
        end
    end

else
    System -> Applicant : Notify Invalid Submission (REQ_ST10)
    Applicant -> System : Revise and Resubmit
end
@enduml