# Notification System

This notification system provides a complete solution for displaying user notifications in the header with a modal interface.

## Components

### 1. useNotifications Hook (`/hooks/useNotifications.ts`)

A custom React hook that manages notification state and provides methods for interacting with notifications.

**Features:**
- Fetches notifications for the authenticated user
- Tracks unread count
- Provides methods to mark notifications as read
- Auto-refreshes every 30 seconds
- <PERSON><PERSON> loading and error states

**Usage:**
```tsx
import { useNotifications } from '@/hooks/useNotifications';

const MyComponent = () => {
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refreshNotifications
  } = useNotifications();

  // Use the notification data...
};
```

### 2. NotificationItem Component (`/components/notifications/NotificationItem.tsx`)

A reusable component for displaying individual notifications.

**Features:**
- Shows notification icon based on type
- Displays priority indicators
- Formats timestamps (e.g., "2 hours ago")
- Handles read/unread states
- Provides mark as read functionality

**Props:**
- `notification`: The notification object
- `onMarkAsRead`: Callback when marking as read
- `onNotificationClick`: Optional callback when clicking notification

### 3. NotificationModal Component (`/components/notifications/NotificationModal.tsx`)

A modal component that displays all user notifications with filtering and actions.

**Features:**
- Filter by all/unread notifications
- Mark individual notifications as read
- Mark all notifications as read
- Refresh notifications
- Empty states and error handling
- Responsive design

**Props:**
- `isOpen`: Boolean to control modal visibility
- `onClose`: Callback when closing modal

### 4. Header Integration

The Header component has been updated to include:
- Notification button with unread count badge
- Click handler to open notification modal
- Real-time unread count updates

## Backend API Integration

The system integrates with the following backend endpoints:

### Get User Notifications
```
GET /notifications/my-notifications
Authorization: Bearer <token>
```

Returns paginated notifications for the authenticated user.

### Mark Notification as Read
```
PATCH /notifications/{id}/mark-read
Authorization: Bearer <token>
```

Marks a specific notification as read.

## Setup Instructions

1. **Install Dependencies**: All required dependencies are already included in the project.

2. **Environment Variables**: Ensure `NEXT_PUBLIC_API_URL` is set in `.env.local`:
   ```
   NEXT_PUBLIC_API_URL=http://localhost:3001
   ```

3. **Import Components**: The notification system is automatically integrated into the Header component.

4. **Authentication**: The system requires user authentication and will only work for logged-in users.

## Testing

A test page is available at `/test-notifications` that provides:
- Current user information
- Notification statistics
- Manual refresh and mark as read actions
- Error display
- API connection status

## Notification Types

The system supports various notification types with appropriate icons:
- `email`: Email notifications
- `sms`: SMS notifications  
- `in_app`: In-app notifications
- `application_status`: Application status updates
- `evaluation_assigned`: Task assignments
- `payment_due`: Payment reminders
- `license_expiry`: License expiration alerts
- `system_alert`: System alerts

## Priority Levels

Notifications support priority levels with color coding:
- `urgent`: Red
- `high`: Orange
- `medium`: Yellow (default)
- `low`: Green

## Styling

The components use Tailwind CSS classes and support both light and dark themes. The design follows the existing application's design system.

## Auto-refresh

Notifications are automatically refreshed every 30 seconds when the user is authenticated. This ensures users see new notifications without manual refresh.

## Error Handling

The system includes comprehensive error handling:
- Network errors are caught and displayed
- Failed API calls don't crash the interface
- Graceful fallbacks for missing data
- User-friendly error messages

## Performance

- Notifications are only fetched when needed
- Efficient state management prevents unnecessary re-renders
- Polling is automatically stopped when user logs out
- Modal content is lazy-loaded
