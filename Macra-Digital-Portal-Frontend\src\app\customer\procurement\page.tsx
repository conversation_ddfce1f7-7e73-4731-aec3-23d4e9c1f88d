'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import TextInput from '@/components/forms/TextInput';
import Select from '@/components/forms/Select';

interface Tender {
  id: string;
  title: string;
  description: string;
  category: string;
  publishDate: string;
  closingDate: string;
  status: 'open' | 'closed' | 'awarded' | 'cancelled';
  estimatedValue: number;
  currency: string;
  requirements: string[];
  documents: TenderDocument[];
  hasAccess: boolean;
  paymentRequired: boolean;
  accessFee: number;
}

interface TenderDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  downloadUrl: string;
}

interface Bid {
  id: string;
  tenderId: string;
  status: 'draft' | 'submitted' | 'under_review' | 'accepted' | 'rejected';
  submittedAt: string;
  amount: number;
  currency: string;
  documents: BidDocument[];
}

interface BidDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
}



interface Invoice {
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  tenderId: string;
  tenderTitle: string;
  amount: number;
  currency: string;
  description: string;
  paymentInstructions: {
    bankName: string;
    accountName: string;
    accountNumber: string;
    swiftCode: string;
    reference: string;
    generalInstructions?: string;
  };
}

// Sample data - replace with actual API calls
const sampleTenders: Tender[] = [
  {
    id: 'TEN-2024-001',
    title: 'IT Equipment and Software Procurement',
    description: 'Procurement of office computers, servers, networking equipment, and software licenses for MACRA operations.',
    category: 'Information Technology',
    publishDate: '2024-01-01',
    closingDate: '2024-02-15',
    status: 'open',
    estimatedValue: 2500000,
    currency: 'MWK',
    requirements: [
      'Valid business registration certificate',
      'Tax clearance certificate',
      'Technical specifications compliance',
      'Minimum 3 years experience in IT procurement',
      'Financial capacity demonstration'
    ],
    documents: [
      {
        id: 'doc-1',
        name: 'Technical Specifications.pdf',
        type: 'application/pdf',
        size: 1024000,
        downloadUrl: '/api/documents/download/doc-1'
      },
      {
        id: 'doc-2',
        name: 'Bid Submission Template.pdf',
        type: 'application/pdf',
        size: 512000,
        downloadUrl: '/api/documents/download/doc-2'
      }
    ],
    hasAccess: false,
    paymentRequired: true,
    accessFee: 50000
  },
  {
    id: 'TEN-2024-002',
    title: 'Vehicle Fleet Management Services',
    description: 'Comprehensive vehicle fleet management and maintenance services for MACRA vehicle fleet.',
    category: 'Transportation',
    publishDate: '2024-01-10',
    closingDate: '2024-02-20',
    status: 'open',
    estimatedValue: 1800000,
    currency: 'MWK',
    requirements: [
      'Valid business registration certificate',
      'Insurance coverage proof',
      'Fleet management experience',
      'Qualified technical staff',
      'Service level agreement compliance'
    ],
    documents: [
      {
        id: 'doc-3',
        name: 'Service Requirements.pdf',
        type: 'application/pdf',
        size: 800000,
        downloadUrl: '/api/documents/download/doc-3'
      }
    ],
    hasAccess: true,
    paymentRequired: false,
    accessFee: 0
  }
];

const sampleBids: Bid[] = [
  {
    id: 'bid-1',
    tenderId: 'TEN-2024-002',
    status: 'submitted',
    submittedAt: '2024-01-15T10:30:00Z',
    amount: 1750000,
    currency: 'MWK',
    documents: [
      {
        id: 'bid-doc-1',
        name: 'Technical Proposal.pdf',
        type: 'application/pdf',
        size: 2048000,
        uploadedAt: '2024-01-15T10:25:00Z'
      },
      {
        id: 'bid-doc-2',
        name: 'Financial Proposal.pdf',
        type: 'application/pdf',
        size: 1024000,
        uploadedAt: '2024-01-15T10:28:00Z'
      }
    ]
  }
];



const CustomerProcurementPage = () => {
  const { isAuthenticated, loading  : authLoading } = useAuth();
  const router = useRouter();

  const [tenders, setTenders] = useState<Tender[]>([]);
  const [myBids, setMyBids] = useState<Bid[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState<'available' | 'my-bids'>('available');
  const [selectedTender, setSelectedTender] = useState<Tender | null>(null);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [showBidModal, setShowBidModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // Redirect to customer login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        setError('');

        // In a real implementation, these would be API calls
        // const [tendersRes, bidsRes] = await Promise.allSettled([
        //   customerApi.getTenders(),
        //   customerApi.getMyBids()
        // ]);

        // For now, use sample data
        setTenders(sampleTenders);
        setMyBids(sampleBids);

      } catch (err) {
        console.error('Error fetching procurement data:', err);
        setError('Failed to load procurement data. Please try refreshing the page.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [isAuthenticated]);

  // Filter tenders
  const filteredTenders = tenders.filter(tender => {
    const matchesSearch = tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tender.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || tender.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || tender.status === filterStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Get unique categories
  const categories = Array.from(new Set(tenders.map(t => t.category)));

  const handlePayForAccess = async (tender: Tender) => {
    setSelectedTender(tender);
    setShowInvoiceModal(true);
  };

  const handleSubmitBid = (tender: Tender) => {
    setSelectedTender(tender);
    setShowBidModal(true);
  };

  const handleDownloadDocument = async (document: TenderDocument) => {
    try {
      // In a real implementation, this would download the file
      console.log('Downloading document:', document.name);
      // const blob = await customerApi.downloadTenderDocument(document.id);
      // const url = window.URL.createObjectURL(blob);
      // const a = document.createElement('a');
      // a.href = url;
      // a.download = document.name;
      // a.click();
    } catch (error) {
      console.error('Error downloading document:', error);
    }
  };

  const handleViewBidDocument = async (document: BidDocument) => {
    try {
      // In a real implementation, this would open the document in a new tab or modal
      console.log('Viewing bid document:', document.name);
      // const blob = await customerApi.viewBidDocument(document.id);
      // const url = window.URL.createObjectURL(blob);
      // window.open(url, '_blank');
      
      // For demo purposes, show an alert
      alert(`Opening document: ${document.name}\n\nIn a real implementation, this would open the document in a new tab or viewer.`);
    } catch (error) {
      console.error('Error viewing document:', error);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency === 'MWK' ? 'MWK' : 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      case 'awarded': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'submitted': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <Loader message="Loading procurement data..." />
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="mt-2 text-sm underline hover:no-underline"
          >
            Try again
          </button>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'available', label: 'Available Tenders', count: filteredTenders.length },
              { key: 'my-bids', label: 'My Bids', count: myBids.length }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as 'available' | 'my-bids')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.key
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Available Tenders Tab */}
        {activeTab === 'available' && (
          <div>
            {/* Filters */}
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <TextInput
                  label="Search"
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search tenders..."
                />
                <Select
                  label="Category"
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </Select>
                <Select
                  label="Status"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <option value="all">All Status</option>
                  <option value="open">Open</option>
                  <option value="closed">Closed</option>
                  <option value="awarded">Awarded</option>
                  <option value="cancelled">Cancelled</option>
                </Select>
              </div>
            </div>

            {/* Tenders List */}
            <div className="space-y-6">
              {filteredTenders.map((tender) => (
                <div key={tender.id} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                          {tender.title}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(tender.status)}`}>
                          {tender.status.charAt(0).toUpperCase() + tender.status.slice(1)}
                        </span>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 mb-2">{tender.description}</p>
                      <div className="flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <span>Category: {tender.category}</span>
                        <span>Estimated Value: {formatCurrency(tender.estimatedValue, tender.currency)}</span>
                        <span>Published: {formatDate(tender.publishDate)}</span>
                        <span>Closes: {formatDate(tender.closingDate)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Requirements */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Requirements:</h4>
                    <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {tender.requirements.map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Documents */}
                  {tender.hasAccess ? (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Documents:</h4>
                      <div className="space-y-2">
                        {tender.documents.map((doc) => (
                          <div key={doc.id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                            <div className="flex items-center">
                              <i className="ri-file-pdf-line text-red-500 mr-2"></i>
                              <span className="text-sm text-gray-700 dark:text-gray-300">{doc.name}</span>
                              <span className="text-xs text-gray-500 ml-2">
                                ({(doc.size / 1024).toFixed(0)} KB)
                              </span>
                            </div>
                            <button
                              onClick={() => handleDownloadDocument(doc)}
                              className="text-primary hover:text-red-700 text-sm font-medium"
                            >
                              Download
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : tender.paymentRequired && (
                    <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
                      <div className="flex items-center">
                        <i className="ri-lock-line text-yellow-600 mr-2"></i>
                        <span className="text-sm text-yellow-800 dark:text-yellow-200">
                          Payment of {formatCurrency(tender.accessFee, tender.currency)} required to access tender documents
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-3">
                    {!tender.hasAccess && tender.paymentRequired ? (
                      <button
                        onClick={() => handlePayForAccess(tender)}
                        className="px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 text-sm font-medium transition-colors duration-300"
                      >
                        Pay for Access ({formatCurrency(tender.accessFee, tender.currency)})
                      </button>
                    ) : tender.hasAccess && tender.status === 'open' ? (
                      <button
                        onClick={() => handleSubmitBid(tender)}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm font-medium"
                      >
                        Submit Bid
                      </button>
                    ) : null}
                  </div>
                </div>
              ))}

              {filteredTenders.length === 0 && (
                <div className="text-center py-12">
                  <i className="ri-file-list-3-line text-4xl text-gray-400 mb-4"></i>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No tenders found</h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    {searchTerm || filterCategory !== 'all' || filterStatus !== 'all'
                      ? 'Try adjusting your search criteria.'
                      : 'There are no tenders available at the moment.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* My Bids Tab */}
        {activeTab === 'my-bids' && (
          <div className="space-y-6">
            {myBids.map((bid) => {
              const tender = tenders.find(t => t.id === bid.tenderId);
              return (
                <div key={bid.id} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                        {tender?.title || 'Unknown Tender'}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <span>Bid Amount: {formatCurrency(bid.amount, bid.currency)}</span>
                        <span>Submitted: {formatDate(bid.submittedAt)}</span>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(bid.status)}`}>
                      {bid.status.replace('_', ' ').charAt(0).toUpperCase() + bid.status.replace('_', ' ').slice(1)}
                    </span>
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Submitted Documents:</h4>
                    <div className="space-y-2">
                      {bid.documents.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                          <div className="flex items-center flex-1">
                            <i className="ri-file-pdf-line text-red-500 mr-3 text-lg"></i>
                            <div className="flex-1">
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{doc.name}</span>
                                <span className="text-xs text-gray-500 ml-2 bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                                  {(doc.size / 1024).toFixed(0)} KB
                                </span>
                              </div>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                Uploaded: {formatDate(doc.uploadedAt)}
                              </span>
                            </div>
                          </div>
                          <button
                            onClick={() => handleViewBidDocument(doc)}
                            className="ml-3 px-3 py-1.5 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 text-xs font-medium transition-colors duration-300"
                          >
                            <i className="ri-eye-line mr-1"></i>
                            View
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}

            {myBids.length === 0 && (
              <div className="text-center py-12">
                <i className="ri-file-text-line text-4xl text-gray-400 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No bids submitted</h3>
                <p className="text-gray-500 dark:text-gray-400">
                  You haven&apos;t submitted any bids yet. Browse available tenders to get started.
                </p>
              </div>
            )}
          </div>
        )}



        {/* Invoice Modal */}
        {showInvoiceModal && selectedTender && (
          <InvoiceModal
            tender={selectedTender}
            onClose={() => {
              setShowInvoiceModal(false);
              setSelectedTender(null);
            }}
            onInvoiceGenerated={() => {
              // Close modal after invoice is generated
              setShowInvoiceModal(false);
              setSelectedTender(null);
            }}
          />
        )}

        {/* Bid Submission Modal */}
        {showBidModal && selectedTender && (
          <BidSubmissionModal
            tender={selectedTender}
            onClose={() => {
              setShowBidModal(false);
              setSelectedTender(null);
            }}
            onBidSubmitted={() => {
              // Refresh data after bid submission
              window.location.reload();
            }}
          />
        )}
      </div>
    </CustomerLayout>
  );
};

// Invoice Modal Component
const InvoiceModal: React.FC<{
  tender: Tender;
  onClose: () => void;
  onInvoiceGenerated: () => void;
}> = ({ tender, onClose, onInvoiceGenerated }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [invoiceGenerated, setInvoiceGenerated] = useState(false);
  const [invoiceData, setInvoiceData] = useState<Invoice | null>(null);

  const generateInvoice = async () => {
    setIsGenerating(true);
    try {
      // In a real implementation, this would call an API to generate the invoice
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
      
      const invoice = {
        invoiceNumber: `INV-${Date.now()}`,
        issueDate: new Date().toISOString().split('T')[0],
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
        tenderId: tender.id,
        tenderTitle: tender.title,
        amount: tender.accessFee,
        currency: tender.currency,
        description: `Tender document access fee for ${tender.title}`,
        paymentInstructions: {
          bankName: '',
          accountName: 'Malawi Communications Regulatory Authority (MACRA)',
          accountNumber: '',
          swiftCode: '',
          reference: `TENDER-${tender.id}`,
          generalInstructions: 'You can make this payment at any bank in Malawi. Please use the invoice number and reference when making payment. Mobile money payments are also accepted.'
        }
      };
      
      setInvoiceData(invoice);
      setInvoiceGenerated(true);
      // Don't call onInvoiceGenerated() here - let user see the invoice first
    } catch (error) {
      console.error('Invoice generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadInvoice = () => {
    // In a real implementation, this would download a PDF invoice
    console.log('Downloading invoice:', invoiceData);
    alert('Invoice downloaded successfully! Please check your downloads folder.');
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency === 'MWK' ? 'MWK' : 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {invoiceGenerated ? 'Invoice Generated' : 'Generate Invoice for Tender Access'}
          </h3>
          <button
            onClick={() => {
              if (invoiceGenerated) {
                onInvoiceGenerated(); // Call callback when user closes after seeing invoice
              }
              onClose();
            }}
            aria-label="Close invoice modal"
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        <div className="p-6">
          {!invoiceGenerated ? (
            // Invoice Generation Form
            <div>
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{tender.title}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  To access the tender documents, you need to pay an access fee of {formatCurrency(tender.accessFee, tender.currency)}.
                  Click the button below to generate an invoice that you can use to make the payment.
                </p>
                
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                  <div className="flex items-start">
                    <i className="ri-information-line text-blue-600 dark:text-blue-400 mt-0.5 mr-3"></i>
                    <div>
                      <h5 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-1">Payment Instructions</h5>
                      <p className="text-sm text-blue-700 dark:text-blue-400">
                        Once you generate the invoice, you can make payment at any bank, through mobile money, or visit any MACRA office. 
                        After payment confirmation, you will receive access to the tender documents.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  onClick={generateInvoice}
                  disabled={isGenerating}
                  className="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
                >
                  {isGenerating ? (
                    <>
                      <i className="ri-loader-4-line animate-spin mr-2"></i>
                      Generating...
                    </>
                  ) : (
                    <>
                      <i className="ri-file-text-line mr-2"></i>
                      Generate Invoice
                    </>
                  )}
                </button>
              </div>
            </div>
          ) : (
            // Invoice Display
            <div>
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <i className="ri-check-circle-line text-green-600 dark:text-green-400 mr-3"></i>
                  <div>
                    <h5 className="text-sm font-medium text-green-800 dark:text-green-300">Invoice Generated Successfully</h5>
                    <p className="text-sm text-green-700 dark:text-green-400">
                      Your invoice has been generated. Please download it and proceed with payment.
                    </p>
                  </div>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-6">
                {/* Invoice Header */}
                <div className="text-center border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                  <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100">INVOICE</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Malawi Communications Regulatory Authority</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Procurement Services</p>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Invoice Number</label>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{invoiceData?.invoiceNumber}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Issue Date</label>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{invoiceData?.issueDate}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Due Date</label>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{invoiceData?.dueDate}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Amount</label>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{invoiceData ? formatCurrency(invoiceData.amount, invoiceData.currency) : '-'}</p>
                  </div>
                </div>
                
                <div className="mb-4">
                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Description</label>
                  <p className="text-sm text-gray-700 dark:text-gray-300">{invoiceData?.description}</p>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <h6 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Payment Instructions</h6>
                  <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                      <p className="text-blue-800 dark:text-blue-300 font-medium mb-1">
                        {invoiceData?.paymentInstructions.generalInstructions}
                      </p>
                      <div className="space-y-1 text-blue-700 dark:text-blue-400">
                        <p><strong>Payee:</strong> {invoiceData?.paymentInstructions.accountName}</p>
                        <p><strong>Invoice Number:</strong> {invoiceData?.invoiceNumber}</p>
                        <p><strong>Reference:</strong> {invoiceData?.paymentInstructions.reference}</p>
                      </div>
                    </div>
                    <div className="mt-3">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        <i className="ri-information-line mr-1"></i>
                        Please keep your payment receipt as proof of payment. Access to tender documents will be granted after payment verification.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => {
                    onInvoiceGenerated(); // Call callback when user closes after seeing invoice
                    onClose();
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-300"
                >
                  Close
                </button>
                <button
                  onClick={downloadInvoice}
                  className="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300"
                >
                  <i className="ri-download-line mr-2"></i>
                  Download Invoice
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Bid Submission Modal Component
const BidSubmissionModal: React.FC<{
  tender: Tender;
  onClose: () => void;
  onBidSubmitted: () => void;
}> = ({ tender, onClose, onBidSubmitted }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bidAmount, setBidAmount] = useState('');
  const [technicalProposal, setTechnicalProposal] = useState<File | null>(null);
  const [financialProposal, setFinancialProposal] = useState<File | null>(null);
  const [companyProfile, setCompanyProfile] = useState<File | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!bidAmount || parseFloat(bidAmount) <= 0) {
      newErrors.bidAmount = 'Please enter a valid bid amount';
    }

    if (!technicalProposal) {
      newErrors.technicalProposal = 'Technical proposal is required';
    } else if (technicalProposal.type !== 'application/pdf') {
      newErrors.technicalProposal = 'Only PDF files are allowed';
    }

    if (!financialProposal) {
      newErrors.financialProposal = 'Financial proposal is required';
    } else if (financialProposal.type !== 'application/pdf') {
      newErrors.financialProposal = 'Only PDF files are allowed';
    }

    if (!companyProfile) {
      newErrors.companyProfile = 'Company profile is required';
    } else if (companyProfile.type !== 'application/pdf') {
      newErrors.companyProfile = 'Only PDF files are allowed';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileChange = (file: File | null, field: string) => {
    if (file && file.type !== 'application/pdf') {
      setErrors(prev => ({ ...prev, [field]: 'Only PDF files are allowed' }));
      return;
    }
    
    if (file && file.size > 10 * 1024 * 1024) { // 10MB limit
      setErrors(prev => ({ ...prev, [field]: 'File size must be less than 10MB' }));
      return;
    }

    setErrors(prev => ({ ...prev, [field]: '' }));
    
    switch (field) {
      case 'technicalProposal':
        setTechnicalProposal(file);
        break;
      case 'financialProposal':
        setFinancialProposal(file);
        break;
      case 'companyProfile':
        setCompanyProfile(file);
        break;
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // In a real implementation, this would submit the bid
      const formData = new FormData();
      formData.append('tenderId', tender.id);
      formData.append('bidAmount', bidAmount);
      if (technicalProposal) formData.append('technicalProposal', technicalProposal);
      if (financialProposal) formData.append('financialProposal', financialProposal);
      if (companyProfile) formData.append('companyProfile', companyProfile);

      // await customerApi.submitBid(formData);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      onBidSubmitted();
    } catch (error) {
      console.error('Bid submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency === 'MWK' ? 'MWK' : 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Submit Bid
          </h3>
          <button
            onClick={onClose}
            aria-label="Close modal"
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        <div className="mb-4">
          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{tender.title}</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Estimated Value: {formatCurrency(tender.estimatedValue, tender.currency)}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Closing Date: {new Date(tender.closingDate).toLocaleDateString()}
          </p>
        </div>

        <div className="space-y-4">
          {/* Bid Amount */}
          <TextInput
            label="Bid Amount (MWK) *"
            type="number"
            value={bidAmount}
            onChange={(e) => setBidAmount(e.target.value)}
            placeholder="Enter your bid amount"
            error={errors.bidAmount}
            required
          />

          {/* Technical Proposal */}
          <div>
            <label htmlFor="technical-proposal" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Technical Proposal (PDF only) *
            </label>
            <input
              id="technical-proposal"
              type="file"
              accept=".pdf"
              onChange={(e) => handleFileChange(e.target.files?.[0] || null, 'technicalProposal')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100 ${
                errors.technicalProposal ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
            />
            {errors.technicalProposal && (
              <p className="text-red-500 text-xs mt-1">{errors.technicalProposal}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Upload your technical proposal detailing how you will deliver the services/goods.
            </p>
          </div>

          {/* Financial Proposal */}
          <div>
            <label htmlFor="financial-proposal" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Financial Proposal (PDF only) *
            </label>
            <input
              id="financial-proposal"
              type="file"
              accept=".pdf"
              onChange={(e) => handleFileChange(e.target.files?.[0] || null, 'financialProposal')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100 ${
                errors.financialProposal ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
            />
            {errors.financialProposal && (
              <p className="text-red-500 text-xs mt-1">{errors.financialProposal}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Upload your detailed cost breakdown and pricing structure.
            </p>
          </div>

          {/* Company Profile */}
          <div>
            <label htmlFor="company-profile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Company Profile (PDF only) *
            </label>
            <input
              id="company-profile"
              type="file"
              accept=".pdf"
              onChange={(e) => handleFileChange(e.target.files?.[0] || null, 'companyProfile')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100 ${
                errors.companyProfile ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
            />
            {errors.companyProfile && (
              <p className="text-red-500 text-xs mt-1">{errors.companyProfile}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Upload your company profile including certifications, experience, and references.
            </p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
          <div className="flex items-start">
            <i className="ri-information-line text-yellow-600 mr-2 mt-0.5"></i>
            <div className="text-sm text-yellow-800 dark:text-yellow-200">
              <p className="font-medium mb-1">Important Notes:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>All documents must be in PDF format</li>
                <li>Maximum file size: 10MB per document</li>
                <li>Ensure all required documents are complete and accurate</li>
                <li>Bids cannot be modified after submission</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Bid'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomerProcurementPage;
