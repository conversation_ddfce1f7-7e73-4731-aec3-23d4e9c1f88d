'use client';

interface HelpCategoriesProps {
  activeCategory: string;
  onCategoryChange: (category: string) => void;
}

export default function HelpCategories({ activeCategory, onCategoryChange }: HelpCategoriesProps) {
  const categories = [
    {
      id: 'getting-started',
      label: 'Getting Started',
      icon: 'ri-rocket-line',
      description: 'Learn the basics of MACRA Portal'
    },
    {
      id: 'license-management',
      label: 'License Management',
      icon: 'ri-key-line',
      description: 'Apply, renew, and manage licenses'
    },
    {
      id: 'spectrum-management',
      label: 'Spectrum Management',
      icon: 'ri-radio-line',
      description: 'Frequency allocation and monitoring'
    },
    {
      id: 'financial-transactions',
      label: 'Financial Transactions',
      icon: 'ri-money-dollar-circle-line',
      description: 'Payments, invoices, and billing'
    },
    {
      id: 'reports-analytics',
      label: 'Reports & Analytics',
      icon: 'ri-file-chart-line',
      description: 'Generate and view reports'
    },
    {
      id: 'account-settings',
      label: 'Account Settings',
      icon: 'ri-settings-line',
      description: 'Profile and system preferences'
    },
    {
      id: 'troubleshooting',
      label: 'Troubleshooting',
      icon: 'ri-tools-line',
      description: 'Common issues and solutions'
    }
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
      <div className="px-4 py-5 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Help Categories</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Browse topics to find answers</p>
      </div>
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.id)}
            className={`w-full px-4 py-4 flex items-start text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
              activeCategory === category.id
                ? 'bg-red-50 dark:bg-red-900/20 border-r-4 border-red-600 dark:border-red-400'
                : ''
            }`}
          >
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 flex items-center justify-center rounded-lg ${
                activeCategory === category.id
                  ? 'bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              }`}>
                <i className={`${category.icon} text-sm`}></i>
              </div>
            </div>
            <div className="ml-3 flex-1">
              <h4 className={`text-sm font-medium ${
                activeCategory === category.id
                  ? 'text-red-900 dark:text-red-100'
                  : 'text-gray-900 dark:text-gray-100'
              }`}>
                {category.label}
              </h4>
              <p className={`text-xs mt-1 ${
                activeCategory === category.id
                  ? 'text-red-700 dark:text-red-300'
                  : 'text-gray-500 dark:text-gray-400'
              }`}>
                {category.description}
              </p>
            </div>
            {activeCategory === category.id && (
              <div className="flex-shrink-0 ml-2">
                <i className="ri-arrow-right-s-line text-red-600 dark:text-red-400"></i>
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}
