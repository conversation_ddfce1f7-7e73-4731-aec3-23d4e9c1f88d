import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EvaluationsService } from './evaluations.service';
import { EvaluationsController } from './evaluations.controller';
import { Evaluations } from '../entities/evaluations.entity';
import { EvaluationCriteria } from '../entities/evaluation-criteria.entity';
import { Applications } from '../entities/applications.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Evaluations,
      EvaluationCriteria,
      Applications,
    ]),
  ],
  controllers: [EvaluationsController],
  providers: [EvaluationsService],
  exports: [EvaluationsService],
})
export class EvaluationsModule {}
