import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';
import { Applicant } from '../types/license';

export interface CreateApplicantData {
  name: string;
  business_registration_number: string;
  tpin: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  level_of_insurance_cover?: string;
  physical_address_id?: string;
  postal_address_id?: string;
  contact_id?: string;
  date_incorporation: string; // Changed from Date to string to match backend DTO
  place_incorporation: string;
}

export const applicantService = {
  // Create new applicant
  async createApplicant(data: CreateApplicantData): Promise<Applicant> {
    try {
      console.log('Creating applicant with data:', data);

      const response = await apiClient.post('/applicants', data);
      return processApiResponse(response);
      throw new Error('Invalid response format from applicant creation');
    } catch (error) {
      console.error('Error creating applicant:', error);
      console.error('Error details:', (error as any)?.response?.data);
      throw error;
    }
  },

  // Get applicant by ID
  async getApplicant(id: string): Promise<Applicant> {
    try {
      const response = await apiClient.get(`/applicants/${id}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Error fetching applicant:', error);
      throw error;
    }
  },

  // Update applicant
  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {
    try {
      console.log('Updating applicant:', id, data);
      
      const response = await apiClient.put(`/applicants/${id}`, data);
      
      console.log('Applicant updated successfully:', response.data);
      return processApiResponse(response);
    } catch (error) {
      console.error('Error updating applicant:', error);
      throw error;
    }
  },

  // Get applicants by user (if user can have multiple applicants)
  async getApplicantsByUser(): Promise<Applicant[]> {
    try {
      const response = await apiClient.get('/applicants/by-user');
      return processApiResponse(response);
    } catch (error) {
      console.error('Error fetching user applicants:', error);
      throw error;
    }
  },

  // Delete applicant
  async deleteApplicant(id: string): Promise<{ message: string }> {
    try {
      const response = await apiClient.delete(`/applicants/${id}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Error deleting applicant:', error);
      throw error;
    }
  }
};
