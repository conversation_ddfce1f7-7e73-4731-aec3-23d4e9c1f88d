import { MigrationInterface, QueryRunner } from "typeorm";

export class FixConsumerAffairsConstraints1704720000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log('🔍 Checking consumer affairs complaints constraints...');
        
        // Check for orphaned created_by records
        const orphanedCreatedBy = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM consumer_affairs_complaints cac
            LEFT JOIN users u ON cac.created_by = u.user_id
            WHERE cac.created_by IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`Found ${orphanedCreatedBy[0].count} orphaned created_by records`);
        
        if (orphanedCreatedBy[0].count > 0) {
            console.log('🛠️ Fixing orphaned created_by records...');
            await queryRunner.query(`
                UPDATE consumer_affairs_complaints 
                SET created_by = NULL 
                WHERE created_by IS NOT NULL 
                AND created_by NOT IN (SELECT user_id FROM users)
            `);
        }
        
        // Check for orphaned updated_by records
        const orphanedUpdatedBy = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM consumer_affairs_complaints cac
            LEFT JOIN users u ON cac.updated_by = u.user_id
            WHERE cac.updated_by IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`Found ${orphanedUpdatedBy[0].count} orphaned updated_by records`);
        
        if (orphanedUpdatedBy[0].count > 0) {
            console.log('🛠️ Fixing orphaned updated_by records...');
            await queryRunner.query(`
                UPDATE consumer_affairs_complaints 
                SET updated_by = NULL 
                WHERE updated_by IS NOT NULL 
                AND updated_by NOT IN (SELECT user_id FROM users)
            `);
        }
        
        // Check for orphaned assigned_to records
        const orphanedAssignedTo = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM consumer_affairs_complaints cac
            LEFT JOIN users u ON cac.assigned_to = u.user_id
            WHERE cac.assigned_to IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`Found ${orphanedAssignedTo[0].count} orphaned assigned_to records`);
        
        if (orphanedAssignedTo[0].count > 0) {
            console.log('🛠️ Fixing orphaned assigned_to records...');
            await queryRunner.query(`
                UPDATE consumer_affairs_complaints 
                SET assigned_to = NULL 
                WHERE assigned_to IS NOT NULL 
                AND assigned_to NOT IN (SELECT user_id FROM users)
            `);
        }
        
        // Check for orphaned complainant_id records (these need to be deleted)
        const orphanedComplainants = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM consumer_affairs_complaints cac
            LEFT JOIN users u ON cac.complainant_id = u.user_id
            WHERE cac.complainant_id IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`Found ${orphanedComplainants[0].count} orphaned complainant_id records`);
        
        if (orphanedComplainants[0].count > 0) {
            console.log('⚠️ Deleting complaints with invalid complainant_id...');
            await queryRunner.query(`
                DELETE FROM consumer_affairs_complaints 
                WHERE complainant_id IS NOT NULL 
                AND complainant_id NOT IN (SELECT user_id FROM users)
            `);
        }
        
        // Fix column types to match users table
        console.log('🔧 Standardizing column types...');
        
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaints 
            MODIFY COLUMN complainant_id varchar(36) NOT NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaints 
            MODIFY COLUMN assigned_to varchar(36) NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaints 
            MODIFY COLUMN created_by varchar(36) NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaints 
            MODIFY COLUMN updated_by varchar(36) NULL
        `);
        
        // Also fix related tables
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaint_attachments 
            MODIFY COLUMN complaint_id varchar(36) NOT NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaint_attachments 
            MODIFY COLUMN uploaded_by varchar(36) NOT NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaint_status_history 
            MODIFY COLUMN complaint_id varchar(36) NOT NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE consumer_affairs_complaint_status_history 
            MODIFY COLUMN created_by varchar(36) NOT NULL
        `);
        
        // Verify the fix
        const remainingIssues = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM consumer_affairs_complaints cac
            LEFT JOIN users u ON cac.created_by = u.user_id
            WHERE cac.created_by IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`Remaining orphaned records: ${remainingIssues[0].count}`);
        
        if (remainingIssues[0].count === 0) {
            console.log('✅ Consumer affairs constraints fix completed successfully!');
        } else {
            throw new Error('❌ Still have orphaned records, fix failed');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        console.log('This migration cleans up data and cannot be rolled back');
    }
}