'use client';

import { useState, useEffect, useCallback } from 'react';
import { paymentService, PaymentsResponse, PaymentFilters } from '../../../services/paymentService';
import { PaginateQuery } from '../../../services/userService';
import DataTable from '../../common/DataTable';
import Select from '../../common/Select';

interface InvoicesTabProps {
  onViewInvoice?: (invoice: any) => void;
  onPayInvoice?: (invoice: any) => void;
}

const InvoicesTab = ({ onViewInvoice, onPayInvoice }: InvoicesTabProps) => {
  const [invoicesData, setInvoicesData] = useState<PaymentsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PaymentFilters>({});
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });

  const loadInvoices = useCallback(async (query: PaginateQuery & PaymentFilters) => {
    try {
      setLoading(true);
      setError(null);
      const response = await paymentService.getInvoices(query);
      setInvoicesData(response);
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError('Failed to load invoices. Please try again.');
      setInvoicesData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadInvoices({ ...currentQuery, ...filters });
  }, [loadInvoices, currentQuery, filters]);

  const handleQueryChange = (query: PaginateQuery) => {
    setCurrentQuery(query);
  };

  const handleFilterChange = (key: keyof PaymentFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'OVERDUE': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'CANCELLED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'LICENSE_FEE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'PROCUREMENT_FEE': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'APPLICATION_FEE': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'RENEWAL_FEE': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';
      case 'PENALTY_FEE': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return `${currency} ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const invoiceColumns = [
    {
      key: 'invoice_number',
      label: 'Invoice #',
      sortable: true,
      searchable: true,
      render: (value: unknown) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {String(value)}
        </div>
      ),
    },
    {
      key: 'payment_type',
      label: 'Type',
      render: (value: unknown) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor(String(value))}`}>
          {String(value).replace('_', ' ')}
        </span>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate" title={String(value)}>
          {String(value)}
        </div>
      ),
    },
    {
      key: 'issue_date',
      label: 'Issue Date',
      sortable: true,
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {formatDate(String(value))}
        </div>
      ),
    },
    {
      key: 'due_date',
      label: 'Due Date',
      sortable: true,
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {formatDate(String(value))}
        </div>
      ),
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value: unknown, item: any) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {formatAmount(Number(value), item.currency)}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, item: any) => (
        <div className="flex space-x-2">
          <button
            onClick={() => onViewInvoice?.(item)}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
          >
            View
          </button>
          {(item.status === 'PENDING' || item.status === 'OVERDUE') && (
            <button
              onClick={() => onPayInvoice?.(item)}
              className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium"
            >
              Pay Now
            </button>
          )}
          <button
            onClick={() => window.open(`/api/payments/${item.payment_id}/download`, '_blank')}
            className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 text-sm font-medium"
          >
            Download
          </button>
        </div>
      ),
    },
  ];

  const paymentTypes = [
    { value: '', label: 'All Types' },
    { value: 'LICENSE_FEE', label: 'License Fee' },
    { value: 'PROCUREMENT_FEE', label: 'Procurement Fee' },
    { value: 'APPLICATION_FEE', label: 'Application Fee' },
    { value: 'RENEWAL_FEE', label: 'Renewal Fee' },
    { value: 'PENALTY_FEE', label: 'Penalty Fee' },
  ];

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'OVERDUE', label: 'Overdue' },
  ];

  const dateRangeOptions = [
    { value: '', label: 'All Time' },
    { value: 'last-30', label: 'Last 30 Days' },
    { value: 'last-90', label: 'Last 90 Days' },
    { value: 'last-year', label: 'Last Year' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">Invoices</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            View and manage your outstanding invoices
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Total: {invoicesData?.meta.totalItems || 0} invoice{(invoicesData?.meta.totalItems || 0) !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <Select
            label="Status"
            value={filters.status || ''}
            onChange={(value) => handleFilterChange('status', value)}
            options={statusOptions}
          />
          <Select
            label="Payment Type"
            value={filters.payment_type || ''}
            onChange={(value) => handleFilterChange('payment_type', value)}
            options={paymentTypes}
          />
          <Select
            label="Date Range"
            value={filters.dateRange || ''}
            onChange={(value) => handleFilterChange('dateRange', value)}
            options={dateRangeOptions}
          />
          <div className="flex items-end">
            <button
              type="button"
              onClick={clearFilters}
              className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-red-500 rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <DataTable
          columns={invoiceColumns}
          data={invoicesData}
          loading={loading}
          onQueryChange={handleQueryChange}
          searchPlaceholder="Search invoices by number, description..."
          emptyStateIcon="ri-file-list-line"
          emptyStateMessage="No invoices found"
        />
      </div>
    </div>
  );
};

export default InvoicesTab;
