'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';
import { EvaluationForm } from '@/components/evaluation';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { ApplicationStatus } from '@/types/license';



interface EvaluateSubmitPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateSubmitPage: React.FC<EvaluateSubmitPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);
  const [evaluationComment, setEvaluationComment] = useState('');

  // Dynamic navigation hook - same as apply pages
  const {
    previousStep,
  } = useDynamicNavigation({
    currentStepRoute: 'submit',
    licenseCategoryId,
    applicationId
  });

  // Load application data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated]);

  // Navigation handlers - modified for evaluation
  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Application approval handlers
  const handleApproveApplication = async () => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      
      // Update application status to approved
      await applicationService.updateApplicationStatus(applicationId, 'approved');

      // Show success message and redirect to dashboard
      alert('Application approved successfully!');
      router.push('/dashboard');
    } catch (err: any) {
      console.error('Error approving application:', err);
      setError('Failed to approve application');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRejectApplication = async () => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      
      // Update application status to rejected
      await applicationService.updateApplicationStatus(applicationId, ApplicationStatus.REJECTED);

      // Show success message and redirect to dashboard
      alert('Application rejected successfully!');
      router.push('/dashboard');
    } catch (err: any) {
      console.error('Error rejecting application:', err);
      setError('Failed to reject application');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRequestRevision = async () => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      
      // Update application status to under review (for revision)
      await applicationService.updateApplicationStatus(applicationId, ApplicationStatus.UNDER_REVIEW);

      // Show success message and redirect to dashboard
      alert('Revision requested successfully!');
      router.push('/dashboard');
    } catch (err: any) {
      console.error('Error requesting revision:', err);
      setError('Failed to request revision');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Evaluation handlers for EvaluationLayout
  const handleStatusUpdate = async (status: string, comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      await applicationService.updateApplicationStatus(applicationId, status);
    } catch (err: any) {
      console.error('Error updating status:', err);
      setError('Failed to update application status');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSave = async (comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      // Save comment logic here
      console.log('Saving comment:', comment);
    } catch (err: any) {
      console.error('Error saving comment:', err);
      setError('Failed to save comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachmentUpload = async (file: File) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      // Upload attachment logic here
      console.log('Uploading attachment:', file.name);
    } catch (err: any) {
      console.error('Error uploading attachment:', err);
      setError('Failed to upload attachment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="submit"
        onNext={undefined}
        onPrevious={handlePrevious}
        showNextButton={false}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText="Continue"
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >

      {/* Application Review Summary */}
      <div className="space-y-6">
        {/* Application Overview */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Application Summary
            </h3>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              <i className="ri-file-list-line mr-2"></i>
              Final Review
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Application ID
              </label>
              <div className="p-3 bg-white dark:bg-gray-800 rounded-md border">
                <p className="text-gray-900 dark:text-gray-100 font-mono text-sm">
                  {applicationId}
                </p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                License Type
              </label>
              <div className="p-3 bg-white dark:bg-gray-800 rounded-md border">
                <p className="text-gray-900 dark:text-gray-100 capitalize">
                  {licenseType?.replace('_', ' ')}
                </p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Current Status
              </label>
              <div className="p-3 bg-white dark:bg-gray-800 rounded-md border">
                <p className="text-gray-900 dark:text-gray-100 capitalize">
                  {application?.status || 'Under Review'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Application Approval Section */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
          <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
            <i className="ri-shield-check-line mr-2"></i>
            Application Decision
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Evaluation Comments
              </label>
              <textarea
                value={evaluationComment}
                onChange={(e) => setEvaluationComment(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-800 dark:text-gray-100"
                placeholder="Add your final evaluation comments and decision rationale..."
              />
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={handleApproveApplication}
                disabled={isSubmitting}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="ri-check-line mr-2"></i>
                    Approve Application
                  </>
                )}
              </button>
              
              <button
                onClick={handleRequestRevision}
                disabled={isSubmitting}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="ri-edit-line mr-2"></i>
                    Request Revision
                  </>
                )}
              </button>
              
              <button
                onClick={handleRejectApplication}
                disabled={isSubmitting}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="ri-close-line mr-2"></i>
                    Reject Application
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Application Details Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <i className="ri-file-text-line mr-2 text-blue-600"></i>
            Application Details
          </h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Submitted Date
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100">
                    {application?.created_at ? new Date(application.created_at).toLocaleDateString() : 'Not available'}
                  </p>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Last Updated
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100">
                    {application?.updated_at ? new Date(application.updated_at).toLocaleDateString() : 'Not available'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Review Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center">
            <i className="ri-information-line mr-2"></i>
            Review Instructions
          </h3>
          <div className="space-y-3">
            <p className="text-blue-700 dark:text-blue-300">
              Before making your final decision, please ensure you have:
            </p>
            <ul className="list-disc list-inside text-blue-700 dark:text-blue-300 space-y-2 ml-4">
              <li>Reviewed all application sections for completeness and accuracy</li>
              <li>Verified all required documents have been submitted and are valid</li>
              <li>Checked that the applicant meets all licensing requirements</li>
              <li>Assessed the technical and financial capabilities of the applicant</li>
              <li>Considered any regulatory or compliance concerns</li>
              <li>Reviewed any previous evaluation comments from other steps</li>
            </ul>
          </div>
        </div>

        {/* Evaluation Section */}
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-800">
          <div className="flex items-start">
            <i className="ri-clipboard-line text-yellow-600 dark:text-yellow-400 text-xl mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                Final Evaluation Step
              </h4>
              <p className="text-yellow-700 dark:text-yellow-300 mb-4">
                This is the final step of the application evaluation process. Please review all previous steps and provide your final assessment.
              </p>
              <ul className="list-disc list-inside text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>Review all application sections for completeness and accuracy</li>
                <li>Verify all required documents have been submitted</li>
                <li>Provide comprehensive evaluation comments</li>
                <li>Make final decision on application status</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

        {/* Evaluation Form */}
        <EvaluationForm
          applicationId={applicationId!}
          currentStep="submit"
          onStatusUpdate={handleStatusUpdate}
          onCommentSave={handleCommentSave}
          onAttachmentUpload={handleAttachmentUpload}
          isSubmitting={isSubmitting}
        />
      </EvaluationLayout>
  );
};

export default EvaluateSubmitPage;
