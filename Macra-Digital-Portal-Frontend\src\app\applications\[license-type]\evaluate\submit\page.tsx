'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';

import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { evaluationService, type Evaluation, type EvaluationCriteria } from '@/services/evaluationService';
import { ApplicationStatus } from '@/types/license';



interface EvaluateSubmitPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateSubmitPage: React.FC<EvaluateSubmitPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Final evaluation state
  const [evaluation, setEvaluation] = useState<Evaluation | null>(null);
  const [evaluationComment, setEvaluationComment] = useState('');
  const [finalRecommendation, setFinalRecommendation] = useState<'approve' | 'conditional_approve' | 'reject' | ''>('');
  const [totalScore, setTotalScore] = useState<number>(0);
  const [evaluationCriteria, setEvaluationCriteria] = useState<EvaluationCriteria[]>([
    { category: 'Technical Capability', subcategory: 'Infrastructure', score: 0, weight: 0.3, max_marks: 100, awarded_marks: 0 },
    { category: 'Financial Capability', subcategory: 'Capital Requirements', score: 0, weight: 0.25, max_marks: 100, awarded_marks: 0 },
    { category: 'Legal Compliance', subcategory: 'Regulatory Requirements', score: 0, weight: 0.2, max_marks: 100, awarded_marks: 0 },
    { category: 'Experience', subcategory: 'Industry Experience', score: 0, weight: 0.15, max_marks: 100, awarded_marks: 0 },
    { category: 'Documentation', subcategory: 'Completeness', score: 0, weight: 0.1, max_marks: 100, awarded_marks: 0 }
  ]);
  const [shareholdingCompliance, setShareholdingCompliance] = useState<boolean>(true);

  // Dynamic navigation hook - same as apply pages
  const {
    previousStep,
  } = useDynamicNavigation({
    currentStepRoute: 'submit',
    licenseCategoryId,
    applicationId
  });

  // Calculate total score from criteria
  const calculateTotalScore = (criteria: EvaluationCriteria[]): number => {
    return evaluationService.calculateTotalScore(criteria);
  };

  // Update criteria score and recalculate total
  const updateCriteriaScore = (index: number, awardedMarks: number) => {
    const updatedCriteria = [...evaluationCriteria];
    updatedCriteria[index].awarded_marks = awardedMarks;
    updatedCriteria[index].score = (awardedMarks / (updatedCriteria[index].max_marks || 100)) * 100;
    setEvaluationCriteria(updatedCriteria);
    setTotalScore(calculateTotalScore(updatedCriteria));
  };

  // Load application data and existing evaluation
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || !user) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }

        // Try to load existing evaluation for this application
        try {
          const evaluations = await evaluationService.getEvaluationByApplication(applicationId);
          const currentUserEvaluation = Array.isArray(evaluations)
            ? evaluations.find((evaluation: Evaluation) => evaluation.evaluator_id === user.user_id && evaluation.status !== 'completed')
            : (evaluations && evaluations.evaluator_id === user.user_id && evaluations.status !== 'completed' ? evaluations : null);

          if (currentUserEvaluation) {
            setEvaluation(currentUserEvaluation);
            setEvaluationComment(currentUserEvaluation.evaluators_notes || '');
            setFinalRecommendation(currentUserEvaluation.recommendation || '');
            setTotalScore(currentUserEvaluation.total_score || 0);
            setShareholdingCompliance(currentUserEvaluation.shareholding_compliance ?? true);

            if (currentUserEvaluation.criteria && currentUserEvaluation.criteria.length > 0) {
              setEvaluationCriteria(currentUserEvaluation.criteria);
            }
          }
        } catch (evalError) {
          console.log('No existing evaluation found, will create new one');
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, user]);

  // Navigation handlers - modified for evaluation
  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Final evaluation submission handler
  const handleSubmitFinalEvaluation = async (recommendation: 'approve' | 'conditional_approve' | 'reject') => {
    if (!applicationId || !user) return;

    if (!evaluationComment.trim()) {
      setError('Please provide evaluation comments before submitting');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const evaluationData = {
        total_score: totalScore,
        recommendation,
        evaluators_notes: evaluationComment,
        criteria: evaluationCriteria.map(c => ({
          category: c.category,
          subcategory: c.subcategory,
          score: c.score,
          weight: c.weight,
          max_marks: c.max_marks,
          awarded_marks: c.awarded_marks
        }))
      };

      if (evaluation) {
        // Update existing evaluation
        await evaluationService.submitEvaluation(evaluation.evaluation_id, evaluationData);
      } else {
        // Create new evaluation
        const newEvaluation = await evaluationService.createEvaluation({
          application_id: applicationId,
          evaluator_id: user.user_id,
          evaluation_type: 'FINAL_REVIEW' as any,
          ...evaluationData,
          shareholding_compliance: shareholdingCompliance
        });
        setEvaluation(newEvaluation);
      }

      // Update application status based on recommendation
      let newStatus: string;
      switch (recommendation) {
        case 'approve':
          newStatus = ApplicationStatus.APPROVED;
          break;
        case 'conditional_approve':
          newStatus = ApplicationStatus.APPROVED; // Use APPROVED for conditional approval
          break;
        case 'reject':
          newStatus = ApplicationStatus.REJECTED;
          break;
        default:
          newStatus = ApplicationStatus.UNDER_REVIEW;
      }

      await applicationService.updateApplicationStatus(applicationId, newStatus);

      // Show success message and redirect
      alert(`Final evaluation submitted successfully! Application ${recommendation.replace('_', ' ')}.`);
      router.push('/dashboard');
    } catch (err: any) {
      console.error('Error submitting final evaluation:', err);
      setError('Failed to submit final evaluation');
    } finally {
      setIsSubmitting(false);
    }
  };



  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="submit"
        onNext={undefined}
        onPrevious={handlePrevious}
        showNextButton={false}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText="Continue"
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >

      {/* Application Review Summary */}
      <div className="space-y-6">
        {/* Application Overview */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Application Summary
            </h3>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              <i className="ri-file-list-line mr-2"></i>
              Final Review
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Application ID
              </label>
              <div className="p-3 bg-white dark:bg-gray-800 rounded-md border">
                <p className="text-gray-900 dark:text-gray-100 font-mono text-sm">
                  {applicationId}
                </p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                License Type
              </label>
              <div className="p-3 bg-white dark:bg-gray-800 rounded-md border">
                <p className="text-gray-900 dark:text-gray-100 capitalize">
                  {licenseType?.replace('_', ' ')}
                </p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Current Status
              </label>
              <div className="p-3 bg-white dark:bg-gray-800 rounded-md border">
                <p className="text-gray-900 dark:text-gray-100 capitalize">
                  {application?.status || 'Under Review'}
                </p>
              </div>
            </div>
          </div>
        </div>


        {/* Review Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center">
            <i className="ri-information-line mr-2"></i>
            Review Instructions
          </h3>
          <div className="space-y-3">
            <p className="text-blue-700 dark:text-blue-300">
              Before making your final decision, please ensure you have:
            </p>
            <ul className="list-disc list-inside text-blue-700 dark:text-blue-300 space-y-2 ml-4">
              <li>Reviewed all application sections for completeness and accuracy</li>
              <li>Verified all required documents have been submitted and are valid</li>
              <li>Checked that the applicant meets all licensing requirements</li>
              <li>Assessed the technical and financial capabilities of the applicant</li>
              <li>Considered any regulatory or compliance concerns</li>
              <li>Reviewed any previous evaluation comments from other steps</li>
            </ul>
          </div>
        </div>

        {/* Evaluation Criteria Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
            <i className="ri-clipboard-line mr-2"></i>
            Evaluation Criteria & Scoring
          </h3>

          <div className="space-y-4">
            {evaluationCriteria.map((criterion, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">
                      {criterion.category}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {criterion.subcategory} (Weight: {(criterion.weight * 100).toFixed(0)}%)
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {criterion.score.toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {criterion.awarded_marks || 0}/{criterion.max_marks || 100}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Awarded Marks:
                  </label>
                  <input
                    type="number"
                    min="0"
                    max={criterion.max_marks || 100}
                    value={criterion.awarded_marks || 0}
                    onChange={(e) => updateCriteriaScore(index, parseInt(e.target.value) || 0)}
                    className="w-24 px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    / {criterion.max_marks || 100}
                  </span>
                </div>
              </div>
            ))}

            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
                  Total Weighted Score
                </h4>
                <div className="text-2xl font-bold text-blue-800 dark:text-blue-200">
                  {totalScore.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Shareholding Compliance */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <i className="ri-shield-check-line mr-2"></i>
            Shareholding Compliance
          </h3>

          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={shareholdingCompliance}
                onChange={(e) => setShareholdingCompliance(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Applicant meets shareholding compliance requirements
              </span>
            </label>
          </div>
        </div>

        {/* Final Evaluation Decision */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
          <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
            <i className="ri-shield-check-line mr-2"></i>
            Final Evaluation Decision
          </h3>

          <div className="space-y-6">
            {/* Evaluation Comments */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Final Evaluation Comments <span className="text-red-500">*</span>
              </label>
              <textarea
                value={evaluationComment}
                onChange={(e) => setEvaluationComment(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-800 dark:text-gray-100"
                placeholder="Provide comprehensive evaluation comments, including rationale for your decision, key findings, and any conditions or recommendations..."
                required
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Please provide detailed comments explaining your evaluation and decision rationale.
              </p>
            </div>

            {/* Recommendation Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Final Recommendation <span className="text-red-500">*</span>
              </label>
              <div className="space-y-3">
                <label className="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                  <input
                    type="radio"
                    name="recommendation"
                    value="approve"
                    checked={finalRecommendation === 'approve'}
                    onChange={(e) => setFinalRecommendation(e.target.value as any)}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <div className="text-sm font-medium text-green-700 dark:text-green-300">
                      Approve Application
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Application meets all requirements and should be approved
                    </div>
                  </div>
                </label>

                <label className="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                  <input
                    type="radio"
                    name="recommendation"
                    value="conditional_approve"
                    checked={finalRecommendation === 'conditional_approve'}
                    onChange={(e) => setFinalRecommendation(e.target.value as any)}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <div className="text-sm font-medium text-yellow-700 dark:text-yellow-300">
                      Conditional Approval
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Approve with specific conditions that must be met
                    </div>
                  </div>
                </label>

                <label className="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                  <input
                    type="radio"
                    name="recommendation"
                    value="reject"
                    checked={finalRecommendation === 'reject'}
                    onChange={(e) => setFinalRecommendation(e.target.value as any)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <div className="text-sm font-medium text-red-700 dark:text-red-300">
                      Reject Application
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Application does not meet requirements and should be rejected
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => finalRecommendation && handleSubmitFinalEvaluation(finalRecommendation)}
                disabled={isSubmitting || !finalRecommendation || !evaluationComment.trim()}
                className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Submitting Final Evaluation...
                  </>
                ) : (
                  <>
                    <i className="ri-send-plane-line mr-2"></i>
                    Submit Final Evaluation
                  </>
                )}
              </button>

              {(!finalRecommendation || !evaluationComment.trim()) && (
                <p className="text-xs text-red-500 mt-2 text-center">
                  Please select a recommendation and provide evaluation comments before submitting.
                </p>
              )}
            </div>
          </div>
        </div>


      </div>

      </EvaluationLayout>
  );
};

export default EvaluateSubmitPage;
