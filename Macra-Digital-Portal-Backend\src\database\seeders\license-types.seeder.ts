import { DataSource } from 'typeorm';
import { LicenseTypes } from '../../entities/license-types.entity';

export interface Seeder {
  run(dataSource: DataSource): Promise<any>;
}

export default class LicenseTypesSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<any> {
    const repository = dataSource.getRepository(LicenseTypes);

    // Check if license types already exist
    const existingCount = await repository.count();
    if (existingCount > 0) {
      console.log('License types already exist, skipping seeder...');
      return;
    }

    const licenseTypes = [
      {
        name: 'Telecommunications',
        code: 'telecommunications',
        description: 'Licenses for telecommunications services including mobile networks, fixed networks, internet services, and broadcasting',
        validity: 5, // 5 years
      },
      {
        name: 'Postal Services',
        code: 'postal_services',
        description: 'Licenses for postal and courier services including domestic and international mail delivery',
        validity: 3, // 3 years
      },
      {
        name: 'Standards Compliance',
        code: 'standards_compliance',
        description: 'Certificates for standards compliance including type approval, equipment certification, and technical standards',
        validity: 2, // 2 years
      },
      {
        name: 'Broadcasting',
        code: 'broadcasting',
        description: 'Licenses for radio and television broadcasting services',
        validity: 5, // 5 years
      },
      {
        name: 'Spectrum Management',
        code: 'spectrum_management',
        description: 'Licenses for radio frequency spectrum allocation and management',
        validity: 10, // 10 years
      },
    ];

    console.log('Seeding license types...');
    
    for (const licenseTypeData of licenseTypes) {
      const licenseType = repository.create(licenseTypeData);
      await repository.save(licenseType);
      console.log(`✅ Created license type: ${licenseTypeData.name}`);
    }

    console.log('License types seeding completed!');
  }
}
