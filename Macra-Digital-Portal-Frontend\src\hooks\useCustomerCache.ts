'use client';

import { useState, useCallback, useRef, useEffect } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  staleWhileRevalidate?: boolean;
}

const DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

export const useCustomerCache = <T>() => {
  const cache = useRef<Map<string, CacheEntry<T>>>(new Map());
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  const get = useCallback((key: string): T | null => {
    const entry = cache.current.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now > entry.expiry) {
      cache.current.delete(key);
      return null;
    }

    return entry.data;
  }, []);

  const set = useCallback((key: string, data: T, options: CacheOptions = {}) => {
    const ttl = options.ttl || DEFAULT_TTL;
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    };
    cache.current.set(key, entry);
  }, []);

  const fetchWithCache = useCallback(async (
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> => {
    // Check cache first
    const cached = get(key);
    if (cached) {
      return cached;
    }

    // Set loading state
    setIsLoading(prev => ({ ...prev, [key]: true }));

    try {
      const data = await fetcher();
      set(key, data, options);
      return data;
    } finally {
      setIsLoading(prev => ({ ...prev, [key]: false }));
    }
  }, [get, set]);

  const invalidate = useCallback((key?: string) => {
    if (key) {
      cache.current.delete(key);
    } else {
      cache.current.clear();
    }
  }, []);

  const isStale = useCallback((key: string, maxAge: number = DEFAULT_TTL): boolean => {
    const entry = cache.current.get(key);
    if (!entry) return true;

    return Date.now() - entry.timestamp > maxAge;
  }, []);

  // Cleanup expired entries periodically
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of cache.current.entries()) {
        if (now > entry.expiry) {
          cache.current.delete(key);
        }
      }
    }, 60000); // Cleanup every minute

    return () => clearInterval(cleanup);
  }, []);

  return {
    get,
    set,
    fetchWithCache,
    invalidate,
    isStale,
    isLoading
  };
};

export default useCustomerCache;