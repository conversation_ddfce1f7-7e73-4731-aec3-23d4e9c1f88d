'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import { 
  getLicenseTypeStepConfig,
  getOptimizedStepConfig,
  getStepsByLicenseTypeCode,
  isLicenseTypeCodeSupported,
  getStepByRoute,
  getStepIndex,
  getTotalSteps,
  getNextStep,
  getPreviousStep,
  calculateProgress,
  StepConfig,
  LicenseTypeStepConfig
} from '@/config/licenseTypeStepConfig';
import { useLicenseData } from '@/hooks/useLicenseData';
import { CustomerApiService } from '@/lib/customer-api';

interface UseOptimizedStepConfigReturn {
  // Configuration data
  licenseConfig: LicenseTypeStepConfig | null;
  currentStep: StepConfig | null;
  currentStepIndex: number;
  totalSteps: number;
  nextStep: StepConfig | null;
  previousStep: StepConfig | null;
  
  // License data
  licenseTypeId: string | null;
  licenseCategoryId: string | null;
  applicationId: string | null;
  
  // State
  loading: boolean;
  error: string | null;
  
  // Progress tracking
  completedSteps: string[];
  progress: number;
  
  // Utility functions
  isStepAccessible: (stepIndex: number) => boolean;
  markStepCompleted: (stepId: string) => void;
  getStepRoute: (stepId: string) => string | null;
}

/**
 * Optimized step configuration hook that consolidates all step-related functionality
 * Replaces multiple separate hooks with a single, comprehensive solution
 */
export const useOptimizedStepConfig = (currentStepRoute: string): UseOptimizedStepConfigReturn => {
  const searchParams = useSearchParams();
  const { categories, loading: licenseDataLoading } = useLicenseData();
  
  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');
  
  // State
  const [licenseConfig, setLicenseConfig] = useState<LicenseTypeStepConfig | null>(null);
  const [licenseTypeId, setLicenseTypeId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  
  // Create customer API service instance
  const customerApi = useMemo(() => new CustomerApiService(), []);
  
  // Load license configuration
  const loadLicenseConfig = useCallback(async () => {
    try {
      if (!licenseCategoryId || licenseDataLoading) {
        return;
      }
      
      setLoading(true);
      setError(null);
      
      console.log('🔍 Loading optimized step config for category:', licenseCategoryId);
      
      // Find license category
      const category = categories.find(cat => cat.license_category_id === licenseCategoryId);
      if (!category?.license_type_id) {
        // Fallback: fetch from API
        const fetchedCategory = await customerApi.getLicenseCategory(licenseCategoryId);
        if (!fetchedCategory?.license_type_id) {
          throw new Error('License category does not have a license type ID');
        }
        
        const licenseType = await customerApi.getLicenseType(fetchedCategory.license_type_id);
        if (!licenseType) {
          throw new Error('License type not found');
        }
        
        setLicenseTypeId(licenseType.code || licenseType.license_type_id);
        
        // Use optimized configuration
        const config = getOptimizedStepConfig(licenseType.code || licenseType.license_type_id);
        setLicenseConfig(config);
        
        console.log('✅ Loaded config via API:', config.name);
      } else {
        // Use cached data
        const licenseType = await customerApi.getLicenseType(category.license_type_id);
        if (!licenseType) {
          throw new Error('License type not found');
        }
        
        setLicenseTypeId(licenseType.code || licenseType.license_type_id);
        
        // Use optimized configuration based on license type code
        let config: LicenseTypeStepConfig;
        
        if (licenseType.code && isLicenseTypeCodeSupported(licenseType.code)) {
          console.log('✅ Using optimized config for supported license type:', licenseType.code);
          config = getOptimizedStepConfig(licenseType.code);
        } else {
          console.log('⚠️ Using fallback config for license type:', licenseType.code || 'unknown');
          config = getLicenseTypeStepConfig(licenseType.code || licenseType.license_type_id);
        }
        
        setLicenseConfig(config);
        console.log('✅ Loaded config from cache:', config.name);
      }
      
    } catch (err: any) {
      console.error('Error loading license configuration:', err);
      setError(err.message || 'Failed to load license configuration');
      
      // Use default fallback configuration
      const fallbackConfig = getLicenseTypeStepConfig('default');
      setLicenseConfig(fallbackConfig);
      setLicenseTypeId('default');
    } finally {
      setLoading(false);
    }
  }, [licenseCategoryId, licenseDataLoading, categories, customerApi]);
  
  // Load configuration when dependencies change
  useEffect(() => {
    loadLicenseConfig();
  }, [loadLicenseConfig]);
  
  // Computed values based on current configuration
  const currentStep = useMemo(() => {
    if (!licenseTypeId || !currentStepRoute) return null;
    return getStepByRoute(licenseTypeId, currentStepRoute);
  }, [licenseTypeId, currentStepRoute]);
  
  const currentStepIndex = useMemo(() => {
    if (!licenseTypeId || !currentStepRoute) return -1;
    return getStepIndex(licenseTypeId, currentStepRoute);
  }, [licenseTypeId, currentStepRoute]);
  
  const totalSteps = useMemo(() => {
    if (!licenseTypeId) return 0;
    return getTotalSteps(licenseTypeId);
  }, [licenseTypeId]);
  
  const nextStep = useMemo(() => {
    if (!licenseTypeId || !currentStepRoute) return null;
    return getNextStep(licenseTypeId, currentStepRoute);
  }, [licenseTypeId, currentStepRoute]);
  
  const previousStep = useMemo(() => {
    if (!licenseTypeId || !currentStepRoute) return null;
    return getPreviousStep(licenseTypeId, currentStepRoute);
  }, [licenseTypeId, currentStepRoute]);
  
  const progress = useMemo(() => {
    if (!licenseTypeId) return 0;
    return calculateProgress(licenseTypeId, completedSteps);
  }, [licenseTypeId, completedSteps]);
  
  // Utility functions
  const isStepAccessible = useCallback((stepIndex: number) => {
    // Allow access to current and previous steps, or if editing existing application
    return applicationId !== null || stepIndex <= currentStepIndex;
  }, [applicationId, currentStepIndex]);
  
  const markStepCompleted = useCallback((stepId: string) => {
    setCompletedSteps(prev => {
      if (!prev.includes(stepId)) {
        return [...prev, stepId];
      }
      return prev;
    });
  }, []);
  
  const getStepRoute = useCallback((stepId: string) => {
    if (!licenseConfig) return null;
    const step = licenseConfig.steps.find(s => s.id === stepId);
    return step?.route || null;
  }, [licenseConfig]);
  
  return {
    // Configuration data
    licenseConfig,
    currentStep,
    currentStepIndex,
    totalSteps,
    nextStep,
    previousStep,
    
    // License data
    licenseTypeId,
    licenseCategoryId,
    applicationId,
    
    // State
    loading,
    error,
    
    // Progress tracking
    completedSteps,
    progress,
    
    // Utility functions
    isStepAccessible,
    markStepCompleted,
    getStepRoute,
  };
};
