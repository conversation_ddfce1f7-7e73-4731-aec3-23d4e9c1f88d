@startuml
actor Applicant
participant "System" as System
participant "Macra Staff" as MacraStaff

== Application Submission ==
Applicant -> System: Access online portal
Applicant -> System: Select license type
note right of Applicant: REQ_P01
Applicant -> System: Select sub-type
note right of Applicant: REQ_P02
Applicant -> System: Enter address details
Applicant -> System: Upload documents
note right of Applicant: REQ_P06
Applicant -> System: Submit application

== Address Validation ==
System -> System: Validate address via MNAS
note right of System: REQ_P03

alt Address valid
    System -> System: Save application
    System -> MacraStaff: Route to Macra Staff
    note right of System: REQ_P05

    MacraStaff -> System : Review application and record evaluations
    note right of MacraStaff: REQ_P07

    alt Approved by committee
        MacraStaff -> System : Board decision
        note right of MacraStaff: REQ_P07

        alt Approved by Board
            System -> System: Generate invoice
            note right of System: REQ_P08
            System -> Applicant: Notify (approval + invoice)
            note right of System: REQ_P10

            Applicant -> System: Make payment
            note right of Applicant: REQ_P09

            System -> System: Validate payment
            note right of System: REQ_P09

            alt Payment valid
                System -> Applicant: Notify (payment confirmed)
                note right of System: REQ_P10

                MacraStaff -> MacraStaff: Generate license with QR
                note right of MacraStaff: REQ_P11

                System -> Applicant: Send license
                System -> System: Track license validity
                note right of System: REQ_P11, REQ_P12
                System -> Applicant: Notify (license issued)
                note right of System: REQ_P10

                Applicant -> System: View license
            else Payment failed
                System -> Applicant: Notify payment failed
                note right of System: REQ_P10
                Applicant -> System: Retry payment
                note right of Applicant: REQ_P09
            end
        else Board rejected
            System -> Applicant: Notify rejected
            note right of System: REQ_P10
        end
    else Committee decision pending info
        System -> Applicant: Notify pending info
        note right of System: REQ_P10
        Applicant -> System: Upload additional documents
        note right of Applicant: REQ_P06
    else Committee rejected
        System -> Applicant: Notify rejected
        note right of System: REQ_P10
    end
else Invalid address
    System -> Applicant: Notify invalid address
    note right of System: REQ_P10
    Applicant -> System: Revise address
end
@enduml