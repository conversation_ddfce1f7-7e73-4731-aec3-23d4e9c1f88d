import { IsString, IsEmail, IsOptional, IsDateString, Length, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateApplicantDto {
  @ApiProperty({
    description: 'Applicant name',
    example: '<PERSON>',
    maxLength: 255
  })
  @IsString()
  @Length(1, 255)
  name: string;

  @ApiProperty({
    description: 'Business registration number',
    example: 'BRN123456789'
  })
  @IsString()
  business_registration_number: string;

  @ApiProperty({
    description: 'Tax Payer Identification Number (TPIN)',
    example: 'TPIN123456789'
  })
  @IsString()
  tpin: string;

  @ApiProperty({
    description: 'Company website',
    example: 'https://example.com'
  })
  @IsString()
  website: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Phone number',
    example: '+265123456789',
    minLength: 10,
    maxLength: 20
  })
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  phone: string;

  @ApiPropertyOptional({
    description: 'Fax number',
    example: '+265123456789',
    minLength: 10,
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid fax number format' })
  fax?: string;

  @ApiPropertyOptional({
    description: 'Level of insurance cover',
    example: 'Comprehensive'
  })
  @IsOptional()
  @IsString()
  level_of_insurance_cover?: string;

  @ApiProperty({
    description: 'Date of incorporation',
    example: '2020-01-01'
  })
  @IsDateString()
  date_incorporation: Date;

  @ApiProperty({
    description: 'Place of incorporation',
    example: 'Lilongwe, Malawi'
  })
  @IsString()
  place_incorporation: string;
}
