import { PartialType } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  MaxLength,
  IsUrl,
} from 'class-validator';
import { CreateTypeApprovedManufacturerDto } from './create.dto';

export class UpdateTypeApprovedManufacturerDto extends PartialType(
  CreateTypeApprovedManufacturerDto,
) {
  @IsOptional()
  @IsUUID('4', { message: 'Updated by must be a valid UUID.' })
  updated_by?: string;
}
