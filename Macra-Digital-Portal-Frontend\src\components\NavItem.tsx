'use client';

import React from 'react';
import Link from 'next/link';
import { useLoading } from '@/contexts/LoadingContext';

interface NavItemProps {
  href: string;
  icon: string;
  label: string;
  isActive?: boolean;
  onClick?: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ 
  href, 
  icon, 
  label, 
  isActive = false,
  onClick 
}) => {
  const { showLoader } = useLoading();

  const handleClick = () => {
    // Show loader with specific message based on the page
    const pageMessages: { [key: string]: string } = {
      '/dashboard': 'Loading Dashboard...',
      '/applications/telecommunications': 'Loading Telecommunications...',
      '/applications/postal': 'Loading Postal Services...',
      '/applications/standards': 'Loading Standards...',
      '/applications/clf': 'Loading CLF...',
      '/resources': 'Loading Resources...',
      '/procurement': 'Loading Procurement...',
      '/spectrum': 'Loading Spectrum Management...',
      '/financial': 'Loading Financial Data...',
      '/reports': 'Loading Reports...',
      '/users': 'Loading User Management...',
      '/audit-trail': 'Loading Audit Trail...',
      '/help': 'Loading Help & Support...'
    };

    const message = pageMessages[href] || 'Loading page...';
    showLoader(message);

    if (onClick) {
      onClick();
    }
  };

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={`
        flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200
        ${isActive
          ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'
          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'
        }
      `}
    >
      <div className={`w-5 h-5 flex items-center justify-center mr-3 ${isActive ? 'text-red-600 dark:text-red-400' : ''}`}>
        <i className={icon}></i>
      </div>
      {label}
    </Link>
  );
};

export default NavItem;