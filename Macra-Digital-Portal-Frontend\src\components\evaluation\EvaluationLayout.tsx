'use client';

import React, { Suspense } from 'react';
import EvaluationProgress from './EvaluationProgress';

interface EvaluationLayoutProps {
  children: React.ReactNode;
  applicationId?: string;
  licenseTypeCode?: string;
  currentStepRoute?: string;
  onSubmit?: () => void;
  onSave?: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  isSubmitting?: boolean;
  isSaving?: boolean;
  showNextButton?: boolean;
  showPreviousButton?: boolean;
  showSaveButton?: boolean;
  showSubmitButton?: boolean;
  nextButtonText?: string;
  previousButtonText?: string;
  saveButtonText?: string;
  submitButtonText?: string;
  nextButtonDisabled?: boolean;
  previousButtonDisabled?: boolean;
  saveButtonDisabled?: boolean;
  submitButtonDisabled?: boolean;
  className?: string;
  showProgress?: boolean;
  progressFallback?: React.ReactNode;
  stepValidationErrors?: string[];
  showStepInfo?: boolean;
}

// Progress loading fallback
const ProgressLoadingFallback: React.FC = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3">
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const EvaluationLayout: React.FC<EvaluationLayoutProps> = ({
  children,
  applicationId,
  licenseTypeCode,
  currentStepRoute,
  onSubmit,
  onSave,
  onNext,
  onPrevious,
  isSubmitting = false,
  isSaving = false,
  showNextButton = true,
  showPreviousButton = true,
  showSaveButton = false,
  showSubmitButton = false,
  nextButtonText = 'Continue',
  previousButtonText = 'Back',
  saveButtonText = 'Save',
  submitButtonText = 'Submit',
  nextButtonDisabled = false,
  previousButtonDisabled = false,
  saveButtonDisabled = false,
  submitButtonDisabled = false,
  className = '',
  showProgress = true,
  progressFallback,
  stepValidationErrors = [],
  showStepInfo = true
}) => {
  return (
    <div className={`min-h-screen bg-gray-50 overflow-y-auto  dark:bg-gray-900 ${className}`}>
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 p-6  mb-20">
          {/* Progress Steps - Left Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <Suspense fallback={<ProgressLoadingFallback />}>
                <EvaluationProgress />
              </Suspense>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              {/* Step Information Banner */}
              {showStepInfo && licenseTypeCode && currentStepRoute && (
                <div className="border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        License Type: {licenseTypeCode.replace(/_/g, ' ').toUpperCase()}
                      </h3>
                      <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                        Current Step: {currentStepRoute.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                    </div>
                    {stepValidationErrors.length > 0 && (
                      <div className="flex items-center text-red-600 dark:text-red-400">
                        <i className="ri-error-warning-line mr-1"></i>
                        <span className="text-xs">{stepValidationErrors.length} validation error{stepValidationErrors.length !== 1 ? 's' : ''}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Main Content */}
              <div className="p-6">
                {children}
              </div>

              {/* Action Buttons */}
              <div className="border-t border-gray-200 dark:border-gray-700 px-6 py-4 bg-gray-50 dark:bg-gray-900/50 rounded-b-lg">
                <div className="flex items-center justify-between">
                  <div className="flex space-x-3">
                    {showPreviousButton && (
                      <button
                        type="button"
                        onClick={onPrevious}
                        disabled={previousButtonDisabled}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <i className="ri-arrow-left-line mr-2"></i>
                        {previousButtonText}
                      </button>
                    )}
                  </div>

                  <div className="flex space-x-3">
                    {showSaveButton && (
                      <button
                        type="button"
                        onClick={onSave}
                        disabled={saveButtonDisabled || isSaving}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSaving ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                          </>
                        ) : (
                          <>
                            <i className="ri-save-line mr-2"></i>
                            {saveButtonText}
                          </>
                        )}
                      </button>
                    )}

                    {showSubmitButton && (
                      <button
                        type="button"
                        onClick={onSubmit}
                        disabled={submitButtonDisabled || isSubmitting}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Submitting...
                          </>
                        ) : (
                          <>
                            <i className="ri-send-plane-line mr-2"></i>
                            {submitButtonText}
                          </>
                        )}
                      </button>
                    )}

                    {showNextButton && (
                      <button
                        type="button"
                        onClick={onNext}
                        disabled={nextButtonDisabled}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {nextButtonText}
                        <i className="ri-arrow-right-line ml-2"></i>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
};

export default EvaluationLayout;
