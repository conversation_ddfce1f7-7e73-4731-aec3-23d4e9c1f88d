interface LicenseCardProps {
  licensee: string;
  licenseNumber: string;
  registeredAddress: string;
  category: string;
  dateOfIssue: string;
  dateOfExpiry: string;
  status: string;
  statusColor: string;
  utilization: number;
}

const LicenseCard: React.FC<LicenseCardProps> = ({
  title,
  company,
  status,
  statusColor,
  utilization,
  licenseId,
  expiration,
  users,
  type,
}) => {
  return (
    <div className="license-card bg-white rounded-lg shadow overflow-hidden">
      <div className="p-5">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${statusColor}`}>
              <i className={`ri-verified-badge-line text-${statusColor.split('-')[0]}-600`}></i>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">{title}</h3>
              <p className="text-xs text-gray-500">{company}</p>
            </div>
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}`}>
            {status}
          </span>
        </div>
        <div className="mt-4">
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-500">Utilization</span>
            <span className="font-medium text-gray-900">{utilization}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`bg-${statusColor.split('-')[0]}-500 h-2 rounded-full`}
              style={{ width: `${utilization}%` }}
            ></div>
          </div>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-500">License ID</span>
            <p className="font-medium text-gray-900 mt-1">{licenseId}</p>
          </div>
          <div>
            <span className="text-gray-500">Expiration</span>
            <p className="font-medium text-gray-900 mt-1">{expiration}</p>
          </div>
          <div>
            <span className="text-gray-500">Users</span>
            <p className="font-medium text-gray-900 mt-1">{users}</p>
          </div>
          <div>
            <span className="text-gray-500">Type</span>
            <p className="font-medium text-gray-900 mt-1">{type}</p>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-5 py-3 flex justify-between">
        <a href="#" className="text-sm font-medium text-primary hover:text-primary">
          View details
        </a>
        <button className="text-sm font-medium text-gray-500 hover:text-gray-700">
          {status === 'Expired' ? 'Renew' : 'Manage'}
        </button>
      </div>
    </div>
  );
};

export default LicenseCard;