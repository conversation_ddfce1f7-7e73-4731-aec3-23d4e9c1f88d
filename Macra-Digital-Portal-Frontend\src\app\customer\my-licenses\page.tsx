'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';
import Loader from '@/components/Loader';
import { useApplicationNotifications } from '@/hooks/useApplicationNotifications';
import { Application } from '@/types/license';


// Status Modal Component
interface StatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  application: Application | null;
  onContinueApplication?: (application: Application) => void;
  canContinueApplication?: (application: Application) => boolean;
  onStatusUpdate?: () => void; // Callback to refresh the applications list
}

const StatusModal: React.FC<StatusModalProps> = React.memo(({
  isOpen,
  onClose,
  application,
  onContinueApplication,
  canContinueApplication,
  onStatusUpdate
}) => {
  const [currentApplication, setCurrentApplication] = useState<Application | null>(application);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLatestApplicationStatus = useCallback(async () => {
    if (!application?.application_id) return;

    setLoading(true);
    setError(null);

    try {
      const latestApplication = await applicationService.getApplication(application.application_id);
      setCurrentApplication(latestApplication);

      // Don't call onStatusUpdate here to prevent render loop
      // Only call it when user explicitly refreshes
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load latest status: ${errorMessage}`);
      // Fallback to the passed application data
      setCurrentApplication(application);
    } finally {
      setLoading(false);
    }
  }, [application]);

  // Reset modal state when it's closed
  useEffect(() => {
    if (!isOpen) {
      setCurrentApplication(null);
      setError(null);
      setLoading(false);
    }
  }, [isOpen]);

  // Fetch latest application status from database when modal opens
  useEffect(() => {
    if (isOpen && application?.application_id) {
      fetchLatestApplicationStatus();
    }
  }, [isOpen, application?.application_id, fetchLatestApplicationStatus]);

  if (!isOpen || !application) {
    console.log('🚫 StatusModal not rendering - isOpen:', isOpen, 'application:', !!application);
    return null;
  }

  // Use currentApplication (from database) if available, otherwise fallback to passed application
  const displayApplication = currentApplication || application;

  const steps = [
    { id: 1, name: 'Draft', description: 'Application incomplete', icon: 'ri-file-text-line' },
    { id: 2, name: 'Submitted', description: 'Application received and logged', icon: 'ri-file-text-line' },
    { id: 3, name: 'Under Review', description: 'Being reviewed by MACRA team', icon: 'ri-search-line' },
    { id: 4, name: 'Evaluation', description: 'Technical evaluation in progress', icon: 'ri-clipboard-line' },
    { id: 5, name: 'Approved', description: 'License approved and issued', icon: 'ri-check-line' },
    { id: 6, name: 'Rejected', description: 'License rejected', icon: 'ri-check-line' }
  ];

  const getStepStatus = (stepId: number) => {
    const status = displayApplication.status;

    // Map database status to step progression
    switch (status) {
      case 'draft':
        return stepId === 1 ? 'current' : 'upcoming';
      case 'submitted':
        return stepId <= 2 ? (stepId === 2 ? 'current' : 'complete') : 'upcoming';
      case 'under_review':
        return stepId <= 3 ? (stepId === 3 ? 'current' : 'complete') : 'upcoming';
      case 'evaluation':
        return stepId <= 4 ? (stepId === 4 ? 'current' : 'complete') : 'upcoming';
      case 'approved':
        return stepId <= 5 ? 'complete' : 'upcoming';
      case 'rejected':
        return stepId === 6 ? 'error' : stepId < 6 ? 'complete' : 'upcoming';
      default:
        return stepId === 1 ? 'current' : 'upcoming';
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-out sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Loading indicator */}
            {loading && (
              <div className="flex items-center justify-center py-4">
                <Loader message="Loading latest status..." />
              </div>
            )}

            {/* Error message */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                <p className="text-red-700 dark:text-red-200 text-sm">{error}</p>
              </div>
            )}

            {/* Modal header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Application Status
                {currentApplication && (
                  <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                    (Live from database)
                  </span>
                )}
              </h3>
              <button
                type="button"
                title="Close modal"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>

            {/* Application info */}
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                {displayApplication.application_number}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {displayApplication.license_category?.name || 'License Category'}
              </p>
              <div className="mt-2 flex items-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Current Status:
                </span>
                <span className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full ${
                  displayApplication.status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  displayApplication.status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                  displayApplication.status === 'under_review' || displayApplication.status === 'evaluation' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                  displayApplication.status === 'submitted' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                  {displayApplication.status?.replace('_', ' ').toUpperCase() || 'DRAFT'}
                </span>
              </div>
            </div>

            {/* Status tracker */}
            <div className="relative mb-6">
              {/* Progress line */}
              <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600">
                <div
                  className={`h-full bg-primary transition-all duration-500 ${
                    displayApplication.status === 'approved' ? 'w-full' :
                    displayApplication.status === 'evaluation' ? 'w-4/5' :
                    displayApplication.status === 'under_review' ? 'w-3/5' :
                    displayApplication.status === 'submitted' ? 'w-2/5' :
                    displayApplication.status === 'draft' ? 'w-1/5' :
                    'w-0'
                  }`}
                />
              </div>

              {/* Steps */}
              <div className="relative flex justify-between">
                {steps.map((step) => {
                  const stepStatus = getStepStatus(step.id);
                  return (
                    <div key={step.id} className="flex flex-col items-center">
                      <div className={`
                        w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300
                        ${stepStatus === 'complete' ? 'bg-primary border-primary text-white' :
                          stepStatus === 'current' ? 'bg-primary border-primary text-white animate-pulse' :
                          stepStatus === 'error' ? 'bg-red-500 border-red-500 text-white' :
                          'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'}
                      `}>
                        <i className={step.icon}></i>
                      </div>
                      <div className="mt-3 text-center">
                        <div className={`text-sm font-medium ${
                          stepStatus === 'complete' || stepStatus === 'current' ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          {step.name}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20">
                          {step.description}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Status details */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Current Status</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {displayApplication.status?.replace('_', ' ').toUpperCase() || 'DRAFT'}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Submitted</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {displayApplication.submitted_at
                      ? new Date(displayApplication.submitted_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                      : displayApplication.created_at
                      ? new Date(displayApplication.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                      : 'Not available'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Estimated Time</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    30-45 business days
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Modal footer */}
          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>

            {/* Continue Application Button */}
            {displayApplication && canContinueApplication && onContinueApplication && canContinueApplication(displayApplication) && (
              <button
                type="button"
                onClick={() => {
                  onContinueApplication(displayApplication);
                  onClose();
                }}
                className="w-full inline-flex justify-center rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mr-3 sm:w-auto sm:text-sm"
              >
                <i className="ri-edit-line mr-2"></i>
                Continue Application
              </button>
            )}

            {/* Refresh Status Button */}
            <button
              type="button"
              onClick={async () => {
                await fetchLatestApplicationStatus();
                // Only call onStatusUpdate when user explicitly refreshes
                if (onStatusUpdate) {
                  onStatusUpdate();
                }
              }}
              disabled={loading}
              className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mr-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              <i className={`ri-refresh-line mr-2 ${loading ? 'animate-spin' : ''}`}></i>
              Refresh Status
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});

StatusModal.displayName = 'StatusModal';

const MyLicensesPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Enable application notifications
  useApplicationNotifications();
  
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);
  const modalOpeningRef = React.useRef<boolean>(false);

  // Check for success message from URL params
  useEffect(() => {
    if (searchParams.get('submitted') === 'true') {
      setShowSuccessMessage(true);
      // Remove the parameter from URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('submitted');
      window.history.replaceState({}, '', newUrl.toString());

      // Hide success message after 5 seconds
      setTimeout(() => setShowSuccessMessage(false), 5000);
    }
  }, [searchParams]);

  const breadcrumbs = [
    { label: 'Dashboard', href: '/customer' },
    { label: 'My Licenses', href: '/customer/my-licenses' }
  ];

  // Modal functions
  const openStatusModal = (application: Application) => {
    if (modalOpeningRef.current) {
      console.log('🚫 Modal already opening, ignoring duplicate click');
      return;
    }
    
    modalOpeningRef.current = true;
    console.log('🔄 Opening status modal for application:', application.application_id);
    setSelectedApplication(application);
    // Small delay to ensure state is stable
    setTimeout(() => {
      setIsModalOpen(true);
      modalOpeningRef.current = false;
    }, 50);
  };

  const closeStatusModal = () => {
    console.log('🔄 Closing status modal');
    modalOpeningRef.current = false;
    setIsModalOpen(false);
    // Delay clearing the selected application to prevent flashing
    setTimeout(() => {
      setSelectedApplication(null);
    }, 300);
  };

  // Function to continue working on an application
  const handleContinueApplication = async (application: Application) => {
    // Clear any existing errors
    setError(null)
    if (!application || !application.license_category_id) {
      console.error('License category ID not found in application data:', application);
      setError('Unable to continue application: License category information is missing. Please contact support.');
      return;
    }
    // Build the continue URL using query parameters
    const continueUrl = `/customer/applications/apply/applicant-info?license_category_id=${application.license_category_id}&application_id=${application.application_id}`;
    router.push(continueUrl);
  };

  // Check if application can be continued (not submitted and incomplete)
  const canContinueApplication = (application: Application) => {
    const excludedStatuses = ['draft'];
    return excludedStatuses.includes(application.status);
  };

  // Function to fetch user applications
  const fetchUserApplications = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
       const applicationsData = await applicationService.getUserApplications();
      // Ensure we have an array
      const applications = Array.isArray(applicationsData) ? applicationsData : [];


      // Fix empty status values and add default values
      const processedApplications = applications.map((app: Application) => ({
        ...app,
        status: app.status || 'draft', // Default empty status to 'draft'
        progress_percentage: app.progress_percentage || 0,
        current_step: app.current_step || 1,
        application_number: app.application_number || `APP-${app.application_id?.slice(0, 8)}`,
        license_category: app.license_category ? {
          ...app.license_category, // Preserve all license_category properties including license_type_id
          name: app.license_category.name || 'License Category'
        } : undefined // Set to undefined if no license_category data
      }));

      // Log status distribution for debugging
      if (processedApplications.length > 0) {
        const statusCounts = processedApplications.reduce((acc: Record<string, number>, app) => {
          acc[app.status] = (acc[app.status] || 0) + 1;
          return acc;
        }, {});
      }

      setApplications(processedApplications);

    } catch (err: unknown) {
      console.error('Applications fetch error:', err);
      const error = err as { response?: { status?: number; data?: { message?: string } }; message?: string };

      if (error.response?.status === 404) {
        // No applications found - this is okay
        setApplications([]);
        setError(null);
      } else if (error.response?.status === 401) {
        setError('Authentication required. Please log in again.');
        router.push('/customer/auth/login');
        return;
      } else {
        setError(error.response?.data?.message || error.message || 'Failed to fetch applications');
      }
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, router, user]);



  useEffect(() => {
    if (isAuthenticated) {
      fetchUserApplications(); // Load user applications    
    }
  }, [isAuthenticated, fetchUserApplications]);

  // Show loading state
  if (loading) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your licenses...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Failed to load applications
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              type="button"
              onClick={() => {
                // Add delay to avoid rate limiting
                setTimeout(() => fetchUserApplications(), 500);
              }}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Client-side filtering with proper debugging
  const filteredApplications = applications.filter((app: Application) => {
    if (activeFilter === 'all') return true;

    // Special handling for "in_progress" filter
    if (activeFilter === 'in_progress') {
      return canContinueApplication(app);
    }

    const matches = app.status === activeFilter;

    // Debug logging for submitted filter
    if (activeFilter === 'submitted') {
      console.log(`App ${app.application_number}: status="${app.status}", matches=${matches}`);
    }

    return matches;
  });

  // Debug: Log filter results
  console.log(`Active filter: ${activeFilter}, Total apps: ${applications.length}, Filtered: ${filteredApplications.length}`);

  // Debug: Show all unique statuses in the data
  const uniqueStatuses = [...new Set(applications.map(app => app.status))];
  console.log('Unique statuses in data:', uniqueStatuses);

  const getStatusBadge = (status: string): React.ReactElement => {
    const statusConfig = {
      'draft': { color: 'bg-blue-100 text-blue-800', label: 'Draft' },
      'submitted': { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },
      'under_review': { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },
      'evaluation': { color: 'bg-purple-100 text-purple-800', label: 'Evaluation' },
      'approved': { color: 'bg-green-100 text-green-800', label: 'Approved' },
      'rejected': { color: 'bg-red-100 text-red-800', label: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.submitted;
    return (
      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${config.color}`}>
        {config.label}
      </span>
    );
  };

  return (
    <CustomerLayout breadcrumbs={breadcrumbs}>
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">My Licenses</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Track your license applications and manage your approved licenses.
            </p>
          </div>
        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <i className="ri-check-circle-line text-green-600 dark:text-green-400 text-xl mr-3"></i>
              <div>
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Application Submitted Successfully!
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  Your license application has been submitted and is now under review. You will receive email notifications about status updates.
                </p>
              </div>
              <button
                type="button"
                onClick={() => setShowSuccessMessage(false)}
                className="ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
                title="Close success message"
                aria-label="Close success message"
              >
                <i className="ri-close-line text-lg"></i>
              </button>
            </div>
          </div>
        )}

        {/* Filter tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Filter Applications</h2>
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'All Applications' },
              { key: 'in_progress', label: 'In Progress' },
              { key: 'submitted', label: 'Submitted' },
              { key: 'under_review', label: 'Under Review' },
              { key: 'evaluation', label: 'Evaluation' },
              { key: 'approved', label: 'Approved' },
              { key: 'rejected', label: 'Rejected' },
              { key: 'draft', label: 'Draft' }
            ].map(filter => (
              <button
                key={filter.key}
                type="button"
                onClick={() => setActiveFilter(filter.key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeFilter === filter.key
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>
        </div>

        {/* Applications table */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Applications Overview</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              View and track all your license applications
            </p>
          </div>

          {filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 dark:text-gray-500 mb-4">
                <i className="ri-file-list-line text-4xl"></i>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No applications found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {activeFilter === 'all'
                  ? "You haven't submitted any license applications yet."
                  : `No applications with status "${activeFilter.replace('_', ' ')}" found.`
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Application Details
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      License Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Progress
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredApplications.map((application) => (
                    <tr key={application.application_id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                            <i className="ri-file-text-line text-blue-600 dark:text-blue-400"></i>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {application?.applicant?.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Application Number: {application.application_number.slice(0, 8)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {application.license_category?.name || 'License Category'}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {application.license_category?.license_type?.name || 'License Type'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(application.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 relative overflow-hidden">
                            <div
                              className={`${
                                canContinueApplication(application) ? 'bg-blue-500' : 'bg-primary'
                              } h-2 rounded-full transition-all duration-300 absolute top-0 left-0 ${
                                (application.progress_percentage || 0) >= 100 ? 'w-full' :
                                (application.progress_percentage || 0) >= 75 ? 'w-3/4' :
                                (application.progress_percentage || 0) >= 50 ? 'w-1/2' :
                                (application.progress_percentage || 0) >= 25 ? 'w-1/4' :
                                (application.progress_percentage || 0) > 0 ? 'w-1/12' : 'w-0'
                              }`}
                            />
                          </div>
                          <div className="flex flex-col">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {application.progress_percentage || 0}%
                            </span>
                            {canContinueApplication(application) && (
                              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                                In Progress
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {application.submitted_at
                            ? new Date(application.submitted_at).toLocaleDateString()
                            : new Date(application.created_at).toLocaleDateString()
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {/* Continue Application Button - for in-progress applications */}
                          <button
                            type="button"
                            title="Continue working on this application"
                            onClick={() => openStatusModal(application)}
                            className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-200 transition-colors"
                          >
                            <i className="ri-eye-line"></i>
                            View
                          </button>

                          {/* Download License Button - for approved applications */}
                          {application.status === 'approved' && (
                            <button
                              type="button"
                              title="Download license"
                              className="text-primary hover:text-red-700 transition-colors"
                            >
                              <i className="ri-download-line"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Status Modal */}
      {selectedApplication && (
        <StatusModal
          key={selectedApplication.application_id}
          isOpen={isModalOpen}
          onClose={closeStatusModal}
          application={selectedApplication}
          onContinueApplication={handleContinueApplication}
          canContinueApplication={canContinueApplication}
          onStatusUpdate={fetchUserApplications}
        />
      )}
    </CustomerLayout>
  );
};

export default MyLicensesPage;
