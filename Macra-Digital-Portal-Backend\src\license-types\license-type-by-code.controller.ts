import {
  Controller,
  Get,
  Param,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { LicenseTypesService } from './license-types.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { LicenseTypes } from '../entities/license-types.entity';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('License Types')
@Controller('license-type-by-code')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LicenseTypeByCodeController {
  constructor(private readonly licenseTypesService: LicenseTypesService) {}

  @Get(':code')
  @ApiOperation({ summary: 'Get license type by code' })
  @ApiParam({ name: 'code', description: 'License type code' })
  @ApiResponse({
    status: 200,
    description: 'License type retrieved successfully',
    type: LicenseTypes,
  })
  @ApiResponse({
    status: 404,
    description: 'License type not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseType',
    description: 'Viewed license type details by code',
  })
  async findByCode(@Param('code') code: string): Promise<LicenseTypes> {
    return this.licenseTypesService.findByCode(code);
  }
}