import { Response } from 'express';
import { PaymentsService } from './payments.service';
import { CreateProofOfPaymentDto } from './dto/create-proof-of-payment.dto';
import { PaymentStatus, PaymentType } from './entities/payment.entity';
import { ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
export declare class CustomerPaymentsController {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    getCustomerPayments(status?: PaymentStatus, paymentType?: PaymentType, dateRange?: 'last-30' | 'last-90' | 'last-year', search?: string, page?: number, limit?: number, req: any): Promise<import("./payments.service").PaymentQueryResult>;
    getCustomerPaymentStatistics(req: any): Promise<{
        totalPayments: number;
        paidPayments: number;
        pendingPayments: number;
        overduePayments: number;
        totalAmount: number;
        paidAmount: number;
        pendingAmount: number;
    }>;
    getCustomerPayment(id: string, req: any): Promise<import("./entities/payment.entity").Payment>;
    uploadCustomerProofOfPayment(paymentId: string, file: Express.Multer.File, createProofOfPaymentDto: CreateProofOfPaymentDto, req: any): Promise<any>;
    getCustomerProofOfPayments(status?: ProofOfPaymentStatus, paymentId?: string, page?: number, limit?: number, req: any): Promise<import("./payments.service").ProofOfPaymentQueryResult>;
    getCustomerProofOfPayment(id: string, req: any): Promise<import("./entities/proof-of-payment.entity").ProofOfPayment>;
    downloadCustomerProofOfPayment(id: string, req: any, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getCustomerPaymentByInvoiceNumber(invoiceNumber: string, req: any): Promise<import("./entities/payment.entity").Payment>;
}
