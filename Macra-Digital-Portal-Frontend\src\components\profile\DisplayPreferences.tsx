'use client';

import { useTheme } from '../../lib/ThemeContext';

export default function DisplayPreferences() {
  const { theme, setTheme } = useTheme();

  const themeOptions = [
    {
      value: 'light',
      label: 'Light',
      description: 'Use light theme',
      icon: 'ri-sun-line'
    },
    {
      value: 'dark',
      label: 'Dark',
      description: 'Use dark theme',
      icon: 'ri-moon-line'
    },
    {
      value: 'system',
      label: 'System',
      description: 'Follow system preference',
      icon: 'ri-computer-line'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Display Preferences Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Display Preferences</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Customize how the interface appears to you.
        </p>
      </div>

      {/* Theme Selection */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-6">
            {/* Theme Selector */}
            <div>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">Theme</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Choose your preferred color scheme
                  </p>
                </div>
                <div className="ml-4">
                  <select
                    value={theme}
                    onChange={(e) => setTheme(e.target.value)}
                    className="block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm rounded-md"
                  >
                    {themeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Theme Options Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {themeOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setTheme(option.value)}
                  className={`relative p-4 border-2 rounded-lg transition-all duration-200 ${
                    theme === option.value
                      ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center mb-2 ${
                      theme === option.value
                        ? 'bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}>
                      <i className={`${option.icon} text-lg`}></i>
                    </div>
                    <h5 className={`text-sm font-medium ${
                      theme === option.value
                        ? 'text-red-900 dark:text-red-100'
                        : 'text-gray-900 dark:text-gray-100'
                    }`}>
                      {option.label}
                    </h5>
                    <p className={`text-xs mt-1 ${
                      theme === option.value
                        ? 'text-red-700 dark:text-red-300'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {option.description}
                    </p>
                  </div>
                  {theme === option.value && (
                    <div className="absolute top-2 right-2">
                      <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                        <i className="ri-check-line text-white text-xs"></i>
                      </div>
                    </div>
                  )}
                </button>
              ))}
            </div>

            {/* Theme Preview */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Preview</h5>
              <div className="space-y-3">
                {/* Sample UI Elements */}
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                      <i className="ri-user-line text-white text-sm"></i>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Sample User</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400"><EMAIL></p>
                    </div>
                  </div>
                  <button className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors">
                    Action
                  </button>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Card Title</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">123</p>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Another Card</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">456</p>
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>
  );
}
