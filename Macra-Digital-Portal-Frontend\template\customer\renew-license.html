<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>License Renewal - Customer Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style type="text/tailwindcss">
      @layer components {
        .btn-active-primary {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗'];
        }
        .btn-active-secondary {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-secondary-subtle rounded-full hover:bg-secondary-subtle transition transform hover:translate-x-1 after:content-['_↗'];
        }
        .btn-section {
          @apply flex place-content-end border-t pt-4;
        }
        .tab-heading {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6 sm:items-start md:items-start lg:items-center;
        }
        .custom-form-label {
          @apply block text-sm font-medium text-gray-700 pb-2;
        }
        .enhanced-input {
          @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }

        .enhanced-select {
          @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }

        .enhanced-checkbox {
          @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
        }

        .main-button {
          @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
        }

        .secondary-main-button {
          @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
        }

        .license-card {
          @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1;
        }

        .license-card-icon {
          @apply w-12 h-12 rounded-lg flex items-center justify-center text-2xl;
        }

        .license-card-button {
          @apply w-full bg-primary text-white px-4 py-3 rounded-lg font-medium hover:bg-primary-subtle focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 transform hover:scale-105;
        }

        .category-section {
          @apply mb-12;
        }

        .category-header {
          @apply flex items-center mb-6 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border-l-4 border-primary;
        }

        .category-icon {
          @apply w-8 h-8 bg-primary text-white rounded-lg flex items-center justify-center mr-4;
        }

        .fade-in {
          animation: fadeIn 0.6s ease-in-out;
        }

        .slide-up {
          animation: slideUp 0.8s ease-out;
        }

        .inner-section {
          @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2 border-b border-gray-200 pb-4;
        }

        .btn-active-primary {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗'];
        }

        .sub-heading {
          @apply text-gray-700;
        }

        .card-bg {
          @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200;
        }

        .card-bg-expired {
          @apply bg-red-50 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200;
        }

        .expiry-badge {
          @apply relative inline-flex items-center justify-center w-8 h-8 rounded-full text-lg;
        }

        .expiry-critical {
          @apply bg-red-100 text-red-600;
        }

        .expiry-warning {
          @apply bg-yellow-100 text-yellow-600;
        }

        .expiry-good {
          @apply bg-green-100 text-green-600;
        }

        .expiry-badge .days-subscript {
          @apply absolute -bottom-1 -right-3 bg-white text-xs font-bold rounded-full w-7 h-5 flex items-center justify-center border-2;
        }

        .expiry-critical .days-subscript {
          @apply border-red-600 text-red-600;
        }

        .expiry-warning .days-subscript {
          @apply border-yellow-600 text-yellow-600;
        }

        .expiry-good .days-subscript {
          @apply border-green-600 text-green-600;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }

      @layer utilities {
        :root {
          --color-primary: #e02b20;
          --color-secondary: #20d5e0;
          --color-primary-subtle: #e4463c;
          --color-secondary-subtle: #abeff3;
        }
      }
    </style>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      .stagger-animation {
        animation-delay: calc(var(--stagger) * 0.1s);
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>

            <!-- My Licenses Dropdown -->
            <div class="relative">
              <button
                onclick="toggleSidebarDropdown('licensesDropdown')"
                class="w-full flex items-center justify-between px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none"
              >
                <div class="flex items-center">
                  <div class="w-5 h-5 flex items-center justify-center mr-3">
                    <i class="ri-key-line"></i>
                  </div>
                  My Licenses
                </div>
                <i id="licensesDropdownIcon" class="ri-arrow-down-s-line text-gray-400 transition-transform duration-200"></i>
              </button>
              <div id="licensesDropdown" class="hidden mt-1 ml-8 space-y-1">
                <a
                  href="my-licenses.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-list-check-line"></i>
                  </div>
                  View All Licenses
                </a>
                <a
                  href="new-application.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-file-add-line"></i>
                  </div>
                  New Application
                </a>
                <a
                  href="renew-license.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-refresh-line"></i>
                  </div>
                  Renew License
                </a>
              </div>
            </div>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
            <a
              href="request-resource.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-hand-heart-line"></i>
              </div>
              Request Resource
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div class="flex-1 flex items-center justify-between">
              <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
                <h1 class="text-xl font-medium text-gray-900"></h1>
              </div>
              <div class="flex items-center">
                <button
                  type="button"
                  class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
                >
                  <span class="sr-only">View notifications</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-notification-3-line ri-lg"></i>
                  </div>
                  <span
                    class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                  ></span>
                </button>
                <div class="dropdown relative">
                  <button
                    type="button"
                    onclick="toggleDropdown()"
                    class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Open user menu</span>
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdown"
                    class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                  >
                    <div class="py-1">
                      <a
                        href="profile.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Your Profile</a
                      >
                      <a
                        href="account-settings.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Settings</a
                      >
                      <a
                        href="../auth/login.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Sign out</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="tab-heading">
              <div>
                <h1 class="text-3xl font-bold text-gray-900">License Renewal</h1>
                <p class="mt-2 text-gray-600">Manage your license renewals and view upcoming expirations.</p>
              </div>
            </div>

            <!-- Renewal Process Steps -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">Renewal Process</h2>
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Select License</p>
                    <p class="text-xs text-gray-500">Choose license to renew</p>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Update Information</p>
                    <p class="text-xs text-gray-500">Review and update details</p>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Submit Documents</p>
                    <p class="text-xs text-gray-500">Upload required files</p>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Pay & Submit</p>
                    <p class="text-xs text-gray-500">Complete renewal</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Expiration Alert -->
            <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-8">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="ri-alarm-warning-line text-red-400 text-xl"></i>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Upcoming Expirations</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p>You have <strong>4 licenses</strong> expiring within the next 90 days. Renew them now to avoid service interruption.</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- License Renewal Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <!-- TechConnect ISP License - Expiring Soon -->
              <div class="card-bg-expired border-red-200">
                <div class="p-6">
                  <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                      <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="ri-global-line text-2xl text-blue-600"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-lg font-semibold text-gray-900">TechConnect ISP</h3>
                        <p class="text-sm text-gray-500">License #ASL-2022-001</p>
                      </div>
                    </div>
                    <span class="expiry-badge expiry-critical">
                      <i class="ri-time-line"></i>
                      <span class="days-subscript">!</span>
                    </span>
                  </div>

                  <div class="space-y-3 mb-4">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Issue Date:</span>
                      <span class="font-medium">Jan 15, 2022</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Expiry Date:</span>
                      <span class="font-medium text-red-600">Jan 15, 2025</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">License Category:</span>
                      <span class="font-medium">Application Service </span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Renewal Fee:</span>
                      <span class="font-medium text-primary">MWK 30,000,000</span>
                    </div>
                  </div>

                  <div class="btn-section">
                    <button onclick="startRenewal('isp', 'ASL-2022-001')" class="btn-active-primary">
                      <i class="ri-refresh-line mr-2"></i>
                      Renew Now
                    </button>
                  </div>
                </div>
              </div>

              <!-- Blantyre Broadcasting Radio License - Warning -->
              <div class="card-bg border-yellow-200">
                <div class="p-6">
                  <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                      <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="ri-radio-line text-2xl text-red-600"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-lg font-semibold text-gray-900">Blantyre Broadcasting</h3>
                        <p class="text-sm text-gray-500">License #CSL-2022-002</p>
                      </div>
                    </div>
                    <span class="expiry-badge expiry-warning">
                      <i class="ri-time-line"></i>
                      <span class="days-subscript">45</span>
                    </span>
                  </div>

                  <div class="space-y-3 mb-4">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Issue Date:</span>
                      <span class="font-medium">May 30, 2022</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Expiry Date:</span>
                      <span class="font-medium text-yellow-600">June 15, 2025</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">License Category:</span>
                      <span class="font-medium">Content Service </span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Renewal Fee:</span>
                      <span class="font-medium text-primary">MWK 6,000,000</span>
                    </div>
                  </div>

                  <div class="btn-section">
                    <button onclick="startRenewal('radio', 'CSL-2022-002')" class="btn-active-primary">
                      <i class="ri-refresh-line mr-2"></i>
                      Renew Now
                    </button>
                  </div>
                </div>
              </div>

              <!-- SatCom Malawi Network Facility License - Good -->
              <div class="card-bg">
                <div class="p-6">
                  <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                      <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="ri-satellite-dish-line text-2xl text-indigo-600"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-lg font-semibold text-gray-900">SatCom Malawi</h3>
                        <p class="text-sm text-gray-500">License #NFL-2022-001</p>
                      </div>
                    </div>
                    <span class="expiry-badge expiry-good">
                      <i class="ri-time-line"></i>
                      <span class="days-subscript">180</span>
                    </span>
                  </div>

                  <div class="space-y-3 mb-4">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Issue Date:</span>
                      <span class="font-medium">Nov 30, 2023</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Expiry Date:</span>
                      <span class="font-medium text-green-600">Nov 30, 2025</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">License Category:</span>
                      <span class="font-medium">Network Facility</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Renewal Fee:</span>
                      <span class="font-medium text-primary">MWK 2,500,000</span>
                    </div>
                  </div>

                  <div class="btn-section">
                    <button onclick="startRenewal('satellite', 'NFL-2022-001')" class="btn-active-secondary">
                      <i class="ri-refresh-line mr-2"></i>
                      Renew Early
                    </button>
                  </div>
                </div>
              </div>

              <!-- MobileNet Solutions Network Service License - Warning -->
              <div class="card-bg border-yellow-200">
                <div class="p-6">
                  <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                      <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="ri-smartphone-line text-2xl text-green-600"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-lg font-semibold text-gray-900">MobileNet Solutions</h3>
                        <p class="text-sm text-gray-500">License #NSL-2022-001</p>
                      </div>
                    </div>
                    <span class="expiry-badge expiry-warning">
                      <i class="ri-time-line"></i>
                      <span class="days-subscript">62</span>
                    </span>
                  </div>

                  <div class="space-y-3 mb-4">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Issue Date:</span>
                      <span class="font-medium">Apr 8, 2022</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Expiry Date:</span>
                      <span class="font-medium text-yellow-600">Apr 8, 2025</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">License Category:</span>
                      <span class="font-medium">Network Service </span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Renewal Fee:</span>
                      <span class="font-medium text-primary">MWK 1,800,000</span>
                    </div>
                  </div>

                  <div class="btn-section">
                    <button onclick="startRenewal('mobile', 'NSL-2022-001')" class="btn-active-primary">
                      <i class="ri-refresh-line mr-2"></i>
                      Renew Now
                    </button>
                  </div>
                </div>
              </div>

              <!-- Express Courier MW Postal License - Good -->
              <div class="card-bg">
                <div class="p-6">
                  <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                      <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="ri-mail-line text-2xl text-orange-600"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-lg font-semibold text-gray-900">Express Courier MW</h3>
                        <p class="text-sm text-gray-500">License #PSL-2022-001</p>
                      </div>
                    </div>
                    <span class="expiry-badge expiry-good">
                      <i class="ri-time-line"></i>
                      <span class="days-subscript">245</span>
                    </span>
                  </div>

                  <div class="space-y-3 mb-4">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Issue Date:</span>
                      <span class="font-medium">Nov 5, 2022</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Expiry Date:</span>
                      <span class="font-medium text-green-600">Nov 5, 2025</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">License Category:</span>
                      <span class="font-medium">Postal Service </span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Renewal Fee:</span>
                      <span class="font-medium text-primary">MWK 3,000,000</span>
                    </div>
                  </div>

                  <div class="btn-section">
                    <button onclick="startRenewal('postal', 'PSL-2022-001')" class="btn-active-secondary">
                      <i class="ri-refresh-line mr-2"></i>
                      Renew Early
                    </button>
                  </div>
                </div>
              </div>

              <!-- Malawi University Radio License - Critical -->
              <div class="card-bg border-red-200">
                <div class="p-6">
                  <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                      <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="ri-radio-line text-2xl text-red-600"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-lg font-semibold text-gray-900">Malawi University</h3>
                        <p class="text-sm text-gray-500">License #CSL-2022-001</p>
                      </div>
                    </div>
                    <span class="expiry-badge expiry-critical">
                      <i class="ri-time-line"></i>
                      <span class="days-subscript">8</span>
                    </span>
                  </div>

                  <div class="space-y-3 mb-4">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Issue Date:</span>
                      <span class="font-medium">Jan 23, 2022</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Expiry Date:</span>
                      <span class="font-medium text-red-600">Jan 23, 2025</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">License Category:</span>
                      <span class="font-medium">Content Service</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Renewal Fee:</span>
                      <span class="font-medium text-primary">MWK 6,000,000</span>
                    </div>
                  </div>

                  <div class="btn-section">
                    <button onclick="startRenewal('university-radio', 'CSL-2022-001')" class="btn-active-primary">
                      <i class="ri-refresh-line mr-2"></i>
                      Renew Urgently
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Renewal Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Renewal Requirements -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-file-list-line mr-2 text-primary"></i>
                  Renewal Requirements
                </h3>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Updated Financial Statements</p>
                      <p class="text-xs text-gray-500">Latest audited financial statements</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Compliance Certificate</p>
                      <p class="text-xs text-gray-500">Certificate of compliance with regulations</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Technical Assessment</p>
                      <p class="text-xs text-gray-500">Updated technical capacity documentation</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Renewal Application Form</p>
                      <p class="text-xs text-gray-500">Completed renewal application</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Renewal Timeline -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-calendar-line mr-2 text-primary"></i>
                  Renewal Timeline
                </h3>
                <div class="space-y-4">
                  <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">Submit Application</p>
                      <p class="text-xs text-gray-500">Complete and submit renewal form</p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                    <div class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">Review Process</p>
                      <p class="text-xs text-gray-500">15-30 days for evaluation</p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">License Issued</p>
                      <p class="text-xs text-gray-500">New license certificate issued</p>
                    </div>
                  </div>
                  <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-700">
                      <strong>Important:</strong> Submit renewal applications at least 60 days before expiration to ensure continuity of service.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Toggle sidebar dropdown
      function toggleSidebarDropdown(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        const icon = document.getElementById(dropdownId + 'Icon');

        if (dropdown.classList.contains('hidden')) {
          dropdown.classList.remove('hidden');
          icon.classList.add('rotate-180');
        } else {
          dropdown.classList.add('hidden');
          icon.classList.remove('rotate-180');
        }
      }

      // License renewal function
      function startRenewal(licenseType, licenseNumber) {
        // Store the selected license information
        localStorage.setItem('renewalLicenseType', licenseType);
        localStorage.setItem('renewalLicenseNumber', licenseNumber);

        // License type mappings
        const licenseNames = {
          'isp': 'TechConnect ISP License',
          'radio': 'Blantyre Broadcasting Radio License',
          'satellite': 'SatCom Malawi Network Facility License',
          'mobile': 'MobileNet Solutions Network Service License',
          'postal': 'Express Courier MW Postal License',
          'university-radio': 'Malawi University Radio License'
        };

        // Renewal form URLs (these would be actual renewal forms)
        const renewalUrls = {
          'isp': 'renewals/isp-renewal-form.html',
          'radio': 'renewals/radio-renewal-form.html',
          'satellite': 'renewals/satellite-renewal-form.html',
          'mobile': 'renewals/mobile-renewal-form.html',
          'postal': 'renewals/postal-renewal-form.html',
          'university-radio': 'renewals/university-radio-renewal-form.html'
        };

        const licenseName = licenseNames[licenseType];

        if (confirm(`You are about to renew ${licenseName} (${licenseNumber}). Do you want to proceed?`)) {
          // Redirect to specific renewal form
          const renewalUrl = renewalUrls[licenseType];
          if (renewalUrl) {
            window.location.href = renewalUrl;
          } else {
            alert(`Renewal form for ${licenseName} is coming soon.`);
          }
        }
      }

      // Calculate days until expiration (for dynamic updates)
      function calculateDaysUntilExpiry(expiryDate) {
        const today = new Date();
        const expiry = new Date(expiryDate);
        const timeDiff = expiry.getTime() - today.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
        return daysDiff;
      }

      // Update expiry badges dynamically (example function)
      function updateExpiryBadges() {
        // This would typically fetch real data from an API
        // For demo purposes, we're using static data
        console.log('Expiry badges updated');
      }

      // Initialize page
      document.addEventListener('DOMContentLoaded', function() {
        updateExpiryBadges();

        // Open the licenses dropdown by default since we're on the renew license page
        const licensesDropdown = document.getElementById('licensesDropdown');
        const licensesIcon = document.getElementById('licensesDropdownIcon');
        if (licensesDropdown && licensesIcon) {
          licensesDropdown.classList.remove('hidden');
          licensesIcon.classList.add('rotate-180');
        }
      });
    </script>
  </body>
</html>