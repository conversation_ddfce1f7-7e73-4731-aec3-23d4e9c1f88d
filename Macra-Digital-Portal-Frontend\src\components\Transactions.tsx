interface Transaction {
  client: string;
  iconColor: string;
  amount: string;
  status: string;
  statusColor: string;
  date: string;
}

const Transactions = () => {
  const transactions: Transaction[] = [
    {
      client: 'Acme Corp',
      iconColor: 'bg-blue-100 text-blue-600',
      amount: '$2,450.00',
      status: 'Completed',
      statusColor: 'bg-green-100 text-green-800',
      date: 'May 7, 2025',
    },
    {
      client: 'Quantum Solutions',
      iconColor: 'bg-purple-100 text-purple-600',
      amount: '$1,850.00',
      status: 'Completed',
      statusColor: 'bg-green-100 text-green-800',
      date: 'May 5, 2025',
    },
    {
      client: 'Horizon Dynamics',
      iconColor: 'bg-green-100 text-green-600',
      amount: '$950.00',
      status: 'Pending',
      statusColor: 'bg-yellow-100 text-yellow-800',
      date: 'May 3, 2025',
    },
    {
      client: 'Stellar Innovations',
      iconColor: 'bg-red-100 text-red-600',
      amount: '$3,200.00',
      status: 'Failed',
      statusColor: 'bg-red-100 text-red-800',
      date: 'May 1, 2025',
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Recent Transactions</h3>
          <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50">
            <i className="ri-download-line mr-1"></i>
            Export
          </button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions.map((transaction, index) => (
                <tr key={index}>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`flex-shrink-0 h-8 w-8 rounded-full ${transaction.iconColor}`}>
                        <i className="ri-building-line"></i>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{transaction.client}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transaction.amount}</div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${transaction.statusColor}`}>
                      {transaction.status}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                    {transaction.date}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4 text-center">
          <a href="#" className="text-sm font-medium text-primary hover:text-primary">
            View all transactions <span aria-hidden="true">→</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default Transactions;