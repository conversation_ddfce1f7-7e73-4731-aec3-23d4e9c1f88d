import React from 'react';
import Link from 'next/link';

interface LicenseCardProps {
  id: string;
  title: string;
  licenseNumber: string;
  status: 'Active' | 'Expiring Soon' | 'Expired' | 'Pending';
  issueDate: string;
  expirationDate: string;
}

const LicenseCard: React.FC<LicenseCardProps> = ({
  id,
  title,
  licenseNumber,
  status,
  issueDate,
  expirationDate
}) => {
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'Active':
        return {
          badge: 'bg-green-100 text-green-800',
          iconBg: 'bg-green-100',
          iconColor: 'text-green-600'
        };
      case 'Expiring Soon':
        return {
          badge: 'bg-orange-100 text-orange-800',
          iconBg: 'bg-orange-100',
          iconColor: 'text-orange-600'
        };
      case 'Expired':
        return {
          badge: 'bg-red-100 text-red-800',
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600'
        };
      case 'Pending':
        return {
          badge: 'bg-yellow-100 text-yellow-800',
          iconBg: 'bg-yellow-100',
          iconColor: 'text-yellow-600'
        };
      default:
        return {
          badge: 'bg-gray-100 text-gray-800',
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-600'
        };
    }
  };

  const statusStyles = getStatusStyles(status);

  return (
    <div className="license-card bg-white border border-gray-200 rounded-lg overflow-hidden hover:transform hover:-translate-y-1 transition-transform duration-300">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${statusStyles.iconBg}`}>
              <div className={`w-5 h-5 flex items-center justify-center ${statusStyles.iconColor}`}>
                <i className="ri-verified-badge-line"></i>
              </div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">{title}</h3>
              <p className="text-xs text-gray-500">{licenseNumber}</p>
            </div>
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles.badge}`}>
            {status}
          </span>
        </div>
        <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-gray-500">Issue Date</span>
            <p className="font-medium text-gray-900 mt-1">{issueDate}</p>
          </div>
          <div>
            <span className="text-gray-500">Expiration</span>
            <p className="font-medium text-gray-900 mt-1">{expirationDate}</p>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-4 py-2 flex justify-between">
        <Link 
          href={`/customer/licenses/${id}`} 
          className="text-xs font-medium text-primary hover:text-primary"
        >
          View details
        </Link>
        <Link 
          href={`/customer/licenses/${id}/renew`} 
          className="text-xs font-medium text-gray-500 hover:text-gray-700"
        >
          Renew
        </Link>
      </div>
    </div>
  );
};

export default LicenseCard;
