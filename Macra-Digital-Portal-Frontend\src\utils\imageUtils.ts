/**
 * Utility functions for handling profile images
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

/**
 * Get the full URL for a profile image
 * @param profileImage - The profile image path from the API
 * @returns Full URL for the profile image
 */
export const getProfileImageUrl = (profileImage?: string): string | null => {
  if (!profileImage) return null;
  
  // If it's already a full URL (starts with http), return as is
  if (profileImage.startsWith('http://') || profileImage.startsWith('https://')) {
    return profileImage;
  }
  
  // If it's a relative path, prepend the API base URL
  if (profileImage.startsWith('/')) {
    return `${API_BASE_URL}${profileImage}`;
  }
  
  // If it's just a filename, assume it's in the uploads directory
  return `${API_BASE_URL}/uploads/avatars/${profileImage}`;
};

/**
 * Get user initials for fallback display
 * @param firstName - User's first name
 * @param lastName - User's last name
 * @returns Initials string (e.g., "JD")
 */
export const getUserInitials = (firstName?: string, lastName?: string): string => {
  const first = firstName?.charAt(0)?.toUpperCase() || '';
  const last = lastName?.charAt(0)?.toUpperCase() || '';
  return `${first}${last}` || 'U';
};

/**
 * Validate image file for upload
 * @param file - File to validate
 * @returns Validation result with error message if invalid
 */
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
    };
  }

  // Check file size (5MB max)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size too large. Maximum size is 5MB.'
    };
  }

  return { valid: true };
};
