'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { authService } from '@/services/auth.service';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/solid';
import Cookies from "js-cookie";
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';

export default function VerifyTwoFactorPage() {
  const router = useRouter();
  const { completeTwoFactorLogin } = useAuth();
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Checking verification parameters...');
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);
  const searchParams = useSearchParams();
  const userId = searchParams.get('i') || '';
  const u = searchParams.get('unique') || '';
  const c = searchParams.get('c') || '';


  useEffect(() => {

    // Check URL parameters for comprehensive validation
    if (!userId || !u || !c) {
      setLoading(false);

      // Determine the type of access issue
      const missingParams = [];
      if (!userId) missingParams.push('user ID');
      if (!u) missingParams.push('unique identifier');
      if (!c) missingParams.push('verification code');

      if (missingParams.length <= 3) {
        // Direct access to the route - show comprehensive error
        setUnauthorizedAccess(true);
        setError('Unauthorized access. This page can only be accessed through a valid 2FA verification link sent to your email.');
        setLoadingMessage('Please check your email for the verification link or login again.');
        setLoading(true);
      } else {
        // Partial parameters - likely corrupted or tampered link
        setError(`Invalid verification link. Missing required parameters: ${missingParams.join(', ')}. Please use the complete link from your email.`);
        setLoadingMessage('Invalid link. Redirecting to login...');
        setLoading(true);
      }

      setTimeout(() => {
        router.replace('/auth/login');
      }, 7000);

      return;
    }

    const verify2FA = async () => {
      try {
        setLoadingMessage('Verifying your 2FA code...');

        const { access_token, user, message } = await authService.verify2FA({
          unique: u,
          user_id: userId,
          code: c
        });
        if (access_token && user) {

          setSuccess(message || 'Your account has been verified successfully!');
          setLoadingMessage('Account verified! Redirecting to dashboard...');
          setLoading(true);
          // Check if user has 2FA enabled and use completeTwoFactorLogin
          if (user.two_factor_enabled) {
            // Retrieve rememberMe preference from sessionStorage
            const rememberMe = sessionStorage.getItem('remember_me') === 'true';
            await completeTwoFactorLogin(access_token, user, rememberMe);
            // Clear the remember me preference from session storage
            sessionStorage.removeItem('remember_me');
            // Redirect to dashboard after successful 2FA login
            setTimeout(() => {
              router.push('/dashboard');
            }, 2000);
          } else {
            // Fallback: Save to cookies directly for users without 2FA
            Cookies.set('auth_token', access_token, { expires: 1 });
            Cookies.set('auth_user', JSON.stringify(user), { expires: 1 });
            setLoadingMessage('Account authentication not detected! Redirecting to login..');
            // Redirect to login for non-2FA users
            setTimeout(() => {
              router.push('/auth/login');
            }, 7000);
          }
        } else {
          console.error('❌ No access token or user data in 2FA verification response');
          setLoading(false);
          setError('Verification failed. The server response was incomplete. Please try again or contact support.');
          setLoadingMessage('Verification failed. Redirecting to login...');
          setTimeout(() => {
            router.replace('/auth/login');
          }, 7000);
        }

      } catch (err: any) {
        setLoading(false);
        let message: string = 'Failed to verify 2FA. Please try again.';

        // Provide more specific error messages based on the error type
        if (err?.response?.status === 400) {
          message = 'Invalid verification code or expired link. Redirecting to login';
        } else if (err?.response?.status === 401) {
          message = 'Unauthorized access. The verification link may have expired or been used already.';
        } else if (err?.response?.status === 404) {
          message = 'Verification request not found. Please login again to receive a new verification link.';
        } else if (err?.response?.data?.message) {
          message = err.response.data.message;
        } else if (err?.message) {
          message = err.message;
        }

        console.error('❌ 2FA verification error:', err);
        setError(message);
        setLoadingMessage('Verification failed. Redirecting to login...');
        setLoading(true);
        setTimeout(() => {
          router.replace('/auth/login');
        }, 4000);
      }
    };

    // Use URL parameters for the condition check
    if (userId && u && c) {
      verify2FA();
    } else {
      setError('Missing verification information. Redirecting to login..');
      setLoadingMessage('Missing verification information. Redirecting to login...');
      setLoading(true);
      setTimeout(() => {
        router.replace('/auth/login');
      }, 3000);
    }
    
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={loadingMessage} />
        </div>
      </div>
    );
  }


  const alreadyEnabled = success.toLowerCase().includes('enabled');
  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image src="/images/macra-logo.png" alt="MACRA Logo" width={50} height={50} className="mx-auto h-16 w-auto" />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {success ? (
            <span className="text-green-600 dark:text-green-300">Account Verification Success!</span>
          ) : error ? (
            <span className="text-red-800 dark:text-red-300">Error</span>
          ) : (
            <span className="text-gray-600 dark:text-gray-300">Account Verification</span>
          )}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          {error && !alreadyEnabled && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md">
                <XCircleIcon className="w-10 h-10 animate-pulse text-red-600 dark:text-red-300" />
              </div>
              <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center">
                {error}
              </div>
              {unauthorizedAccess && (
                <div className="mt-4 w-full">
                  <button
                    onClick={() => router.replace('/auth/login')}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          )}

          {(success || alreadyEnabled ) && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                <svg
                  className="w-8 h-8 text-green-600 dark:text-green-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="text-center text-gray-600 dark:text-gray-400">
                {success} <br />
              </div>
            </div>
          )}

        </div>
      </div>
    </div>
  );
}
