'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import PaymentTabs from '@/components/customer/payments/PaymentTabs';
import InvoicesTab from '@/components/customer/payments/InvoicesTab';
import PaymentsTab from '@/components/customer/payments/PaymentsTab';

const CustomerPaymentsPage = () => {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('invoices');
  const [isLoading, setIsLoading] = useState(true);

  // Redirect to customer login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/customer/auth/login');
    } else {
      setIsLoading(false);
    }
  }, [isAuthenticated, router]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleViewInvoice = (invoice: any) => {
    console.log('View invoice:', invoice);
    // TODO: Implement invoice view modal or navigation
  };

  const handlePayInvoice = (invoice: any) => {
    console.log('Pay invoice:', invoice);
    // TODO: Implement payment flow
  };

  const handleViewPayment = (payment: any) => {
    console.log('View payment:', payment);
    // TODO: Implement payment view modal or navigation
  };

  const handleUploadProof = (payment: any) => {
    console.log('Upload proof for payment:', payment);
    // TODO: Implement proof of payment upload modal
  };

  // Define tabs for the tab system
  const tabs = [
    {
      id: 'invoices',
      label: 'Invoices',
      icon: 'ri-file-list-3-line',
      content: (
        <InvoicesTab
          onViewInvoice={handleViewInvoice}
          onPayInvoice={handlePayInvoice}
        />
      )
    },
    {
      id: 'payments',
      label: 'Payment History',
      icon: 'ri-money-dollar-circle-line',
      content: (
        <PaymentsTab
          onViewPayment={handleViewPayment}
          onUploadProof={handleUploadProof}
        />
      )
    }
  ];

  if (isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <Loader message="Loading payments..." />
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Payments & Invoices</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Manage your invoices and payment history
              </p>
            </div>
          </div>
        </div>

        {/* Payment Tabs */}
        <PaymentTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      </div>
    </CustomerLayout>
  );
};

export default CustomerPaymentsPage;
