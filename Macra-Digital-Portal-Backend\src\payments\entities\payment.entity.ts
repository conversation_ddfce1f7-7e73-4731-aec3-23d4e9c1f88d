import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Application } from 'express';
import { User } from 'src/entities';

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentType {
  LICENSE_FEE = 'License Fee',
  PROCUREMENT_FEE = 'Procurement Fee',
  APPLICATION_FEE = 'Application Fee',
  RENEWAL_FEE = 'Renewal Fee',
  PENALTY_FEE = 'Penalty Fee',
  INSPECTION_FEE = 'Inspection Fee',
}

export enum Currency {
  MWK = 'MWK',
  USD = 'USD',
  EUR = 'EUR',
}

@Entity('payments')
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  payment_id: string;

  @Column({ unique: true })
  invoice_number: string;

  @Column('decimal', { precision: 15, scale: 2 })
  amount: number;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.MWK,
  })
  currency: Currency;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentType,
  })
  payment_type: PaymentType;

  @Column('text')
  description: string;

  @Column({ type: 'date' })
  due_date: Date;

  @Column({ type: 'date' })
  issue_date: Date;

  @Column({ type: 'date', nullable: true })
  paid_date?: Date;

  @Column({ nullable: true })
  payment_method?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ nullable: true })
  transaction_reference?: string;

  // Relations
  @Column()
  user_id: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ nullable: true })
  application_id?: string;


  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
