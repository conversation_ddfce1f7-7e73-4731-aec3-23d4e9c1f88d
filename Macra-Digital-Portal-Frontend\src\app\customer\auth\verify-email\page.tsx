'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { authService } from '@/services/auth.service';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  EnvelopeIcon,
} from '@heroicons/react/24/solid';
import Cookies from "js-cookie";
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';

export default function CustomerVerifyEmailPage() {
  const router = useRouter();
  const { completeTwoFactorLogin } = useAuth();
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Verifying your email address...');
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);
  const searchParams = useSearchParams();
  const userId = searchParams.get('i') || '';
  const u = searchParams.get('unique') || '';
  const c = searchParams.get('c') || '';

  useEffect(() => {
    if (!userId || !u || !c) {
      setLoading(false);
      const missingParams = [];
      if (!userId) missingParams.push('user ID');
      if (!u) missingParams.push('unique identifier');
      if (!c) missingParams.push('verification code');

      if (missingParams.length <= 3) {
        // Direct access to the route - show comprehensive error
        setUnauthorizedAccess(true);
        setError('Unauthorized access. This page can only be accessed through a valid email verification link sent to your email after registration.');
        setLoadingMessage('Please check your email for the verification link or register again.');
        setLoading(true);
      } else {
        // Partial parameters - likely corrupted or tampered link
        setError(`Invalid verification link. Missing required parameters: ${missingParams.join(', ')}. Please use the complete link from your email.`);
        setLoadingMessage('Invalid link. Redirecting to login...');
        setLoading(true);
      }

      setTimeout(() => {
        router.replace('/customer/auth/login');
      }, 7000);

      return;
    }

    const verifyEmail = async () => {
      try {
        setLoadingMessage('Verifying your email address...');

        const { access_token, user, message } = await authService.verifyEmail({
          unique: u,
          user_id: userId,
          code: c
        });

        if (access_token && user) {
          setSuccess(message || 'Your email has been verified successfully!');
          setLoadingMessage('Email verified! Setting up your account...');
          setLoading(true);
          
          // Check if user has 2FA enabled and use completeTwoFactorLogin
          if (user.two_factor_enabled) {
            // Retrieve rememberMe preference from sessionStorage
            const rememberMe = sessionStorage.getItem('remember_me') === 'true';
            await completeTwoFactorLogin(access_token, user, rememberMe);
            // Clear the remember me preference from session storage
            sessionStorage.removeItem('remember_me');
            // Redirect to customer portal after successful email verification and login
            setTimeout(() => {
              router.push('/customer');
            }, 2000);
          } else {
            // Fallback: Save to cookies directly for users without 2FA
            Cookies.set('auth_token', access_token, { expires: 1 });
            Cookies.set('auth_user', JSON.stringify(user), { expires: 1 });
            setLoadingMessage('Email verification complete. Redirecting to customer portal...');
            // Redirect to customer portal for verified users
            setTimeout(() => {
              router.push('/customer');
            }, 2000);
          }
        } else {
          console.error('❌ No access token or user data in email verification response');
          setLoading(false);
          setError('Email verification failed. The server response was incomplete. Please try again or contact support.');
          setLoadingMessage('Verification failed. Redirecting to login...');
          setTimeout(() => {
            router.replace('/customer/auth/login');
          }, 7000);
        }
      } catch (err: any) {
        setLoading(false);
        let message = 'Failed to verify email. Please try again.';
        if (err?.response?.status === 400) {
          message = 'Invalid or expired verification link.';
        } else if (err?.response?.status === 401) {
          message = 'Unauthorized. The verification link may have expired or been used.';
        } else if (err?.response?.status === 404) {
          message = 'Verification request not found.';
        } else if (err?.response?.data?.message) {
          message = err.response.data.message;
        } else if (err?.message) {
          message = err.message;
        }

        console.error('❌ Email verification error:', err);
        setError(message);
        setLoadingMessage('Verification failed. Redirecting to login...');
        setLoading(true);
        setTimeout(() => {
          router.replace('/customer/auth/login');
        }, 4000);
      }
    };

    // Use URL parameters for the condition check
    if (userId && u && c) {
      verifyEmail();
    } else {
      setError('Missing verification information. Redirecting to login..');
      setLoadingMessage('Missing verification information. Redirecting to login...');
      setLoading(true);
      setTimeout(() => {
        router.replace('/customer/auth/login');
      }, 3000);
    }
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={loadingMessage} />
        </div>
      </div>
    );
  }

  const alreadyEnabled = success.toLowerCase().includes('enabled');
  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image src="/images/macra-logo.png" alt="MACRA Logo" width={50} height={50} className="mx-auto h-16 w-auto" />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {success ? (
            <span className="text-green-600 dark:text-green-300">Email Verification Success!</span>
          ) : error ? (
            <span className="text-red-800 dark:text-red-300">Verification Error</span>
          ) : (
            <span className="text-gray-600 dark:text-gray-300">Email Verification</span>
          )}
        </h2>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Customer Portal Email Verification
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          <div className="flex flex-col items-center text-center">
            {success ? (
              <>
                <div className="w-16 h-16 mb-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center shadow-md">
                  <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
                </div>
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Email Verified Successfully!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {success}
                  </p>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6 w-full text-left">
                  <div className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-400 mt-1" />
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-green-800 dark:text-green-200">
                        Welcome to MACRA Customer Portal!
                      </h4>
                      <p className="mt-2 text-sm text-green-700 dark:text-green-300">
                        Your email has been verified and your account is now active. You will be redirected to the customer portal shortly.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                  <ArrowPathIcon className="w-4 h-4 inline animate-spin mr-1" />
                  Redirecting to customer portal...
                </div>
              </>
            ) : error ? (
              <>
                <div className="w-16 h-16 mb-6 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md">
                  <XCircleIcon className="w-8 h-8 text-red-600 dark:text-red-400" />
                </div>
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {unauthorizedAccess ? 'Unauthorized Access' : 'Verification Failed'}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {error}
                  </p>
                </div>
                {unauthorizedAccess && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6 w-full text-left">
                    <div className="flex items-start">
                      <EnvelopeIcon className="h-5 w-5 text-blue-400 mt-1" />
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                          How to verify your email:
                        </h4>
                        <ul className="mt-2 list-disc list-inside text-sm text-blue-700 dark:text-blue-300 space-y-1">
                          <li>Complete the customer registration process</li>
                          <li>Check your inbox for the verification email</li>
                          <li>Click the verification link in the email</li>
                          <li>Your account will be activated automatically</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                  <ArrowPathIcon className="w-4 h-4 inline animate-spin mr-1" />
                  Redirecting to login...
                </div>
              </>
            ) : (
              <>
                <div className="w-16 h-16 mb-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center shadow-md">
                  <EnvelopeIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Verifying Email...
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Please wait while we verify your email address.
                  </p>
                </div>
              </>
            )}

            <button
              onClick={() => router.push('/customer/auth/login')}
              className="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center"
            >
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                strokeWidth={2}
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Customer Login
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
