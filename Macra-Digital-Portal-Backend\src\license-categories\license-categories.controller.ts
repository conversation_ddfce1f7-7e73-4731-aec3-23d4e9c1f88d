import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  ParseUUIDPipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { LicenseCategoriesService } from './license-categories.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateLicenseCategoryDto } from '../dto/license-categories/create-license-category.dto';
import { UpdateLicenseCategoryDto } from '../dto/license-categories/update-license-category.dto';
import { LicenseCategories } from '../entities/license-categories.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('License Categories')
@Controller('license-categories')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LicenseCategoriesController {
  constructor(private readonly licenseCategoriesService: LicenseCategoriesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all license categories' })
  @ApiResponse({
    status: 200,
    description: 'List of license categories retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Viewed license categories list',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<LicenseCategories>> {
    return this.licenseCategoriesService.findAll(query);
  }

  @Get('by-license-type/:licenseTypeId')
  @ApiOperation({ summary: 'Get license categories by license type' })
  @ApiParam({ name: 'licenseTypeId', description: 'License type UUID' })
  @ApiResponse({
    status: 200,
    description: 'License categories for the specified license type retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Viewed license categories by license type',
  })
  async findByLicenseType(@Param('licenseTypeId', ParseUUIDPipe) licenseTypeId: string): Promise<LicenseCategories[]> {
    return this.licenseCategoriesService.findByLicenseType(licenseTypeId);
  }

  @Get('license-type/:licenseTypeId/tree')
  @ApiOperation({ summary: 'Get hierarchical tree of categories for a license type' })
  @ApiParam({ name: 'licenseTypeId', description: 'License type UUID' })
  @ApiResponse({
    status: 200,
    description: 'Category tree retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Viewed license category tree',
  })
  async getCategoryTree(@Param('licenseTypeId', ParseUUIDPipe) licenseTypeId: string): Promise<LicenseCategories[]> {
    return this.licenseCategoriesService.findCategoryTree(licenseTypeId);
  }

  @Get('license-type/:licenseTypeId/root')
  @ApiOperation({ summary: 'Get root categories (no parent) for a license type' })
  @ApiParam({ name: 'licenseTypeId', description: 'License type UUID' })
  @ApiResponse({
    status: 200,
    description: 'Root categories retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Viewed root license categories',
  })
  async getRootCategories(@Param('licenseTypeId', ParseUUIDPipe) licenseTypeId: string): Promise<LicenseCategories[]> {
    return this.licenseCategoriesService.findRootCategories(licenseTypeId);
  }

  @Get('license-type/:licenseTypeId/for-parent-selection')
  @ApiOperation({ summary: 'Get license categories for parent selection dropdown' })
  @ApiParam({ name: 'licenseTypeId', description: 'License type UUID' })
  @ApiQuery({ name: 'excludeId', description: 'Category ID to exclude from selection (optional)', required: false })
  @ApiResponse({
    status: 200,
    description: 'License categories for parent selection retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          license_category_id: { type: 'string' },
          name: { type: 'string' },
          parent_id: { type: 'string', nullable: true },
          parent: {
            type: 'object',
            properties: {
              license_category_id: { type: 'string' },
              name: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Viewed license categories for parent selection',
  })
  async getCategoriesForParentSelection(
    @Param('licenseTypeId', ParseUUIDPipe) licenseTypeId: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<LicenseCategories[]> {
    try {
      const result = await this.licenseCategoriesService.findCategoriesForParentSelection(licenseTypeId, excludeId);
      console.log('Controller returning categories for parent selection:', result.length);
      return result;
    } catch (error) {
      console.error('Controller error in getCategoriesForParentSelection:', error);
      throw error;
    }
  }

  @Get('license-type/:licenseTypeId/potential-parents')
  @ApiOperation({ summary: 'Get potential parent categories for a license type' })
  @ApiParam({ name: 'licenseTypeId', description: 'License type UUID' })
  @ApiQuery({ name: 'excludeId', description: 'Category ID to exclude (optional)', required: false })
  @ApiResponse({
    status: 200,
    description: 'Potential parent categories retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Viewed potential parent categories',
  })
  async getPotentialParents(
    @Param('licenseTypeId', ParseUUIDPipe) licenseTypeId: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<LicenseCategories[]> {
    return this.licenseCategoriesService.findPotentialParents(licenseTypeId, excludeId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get license category by ID' })
  @ApiParam({ name: 'id', description: 'License category UUID' })
  @ApiResponse({
    status: 200,
    description: 'License category retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'License category not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Viewed license category details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<LicenseCategories> {
    return this.licenseCategoriesService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new license category' })
  @ApiResponse({
    status: 201,
    description: 'License category created successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'License type not found',
  })
  @ApiResponse({
    status: 409,
    description: 'License category with this name already exists for this license type',
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Created new license category',
  })
  async create(
    @Body() createLicenseCategoryDto: CreateLicenseCategoryDto,
    @Request() req: any,
  ): Promise<LicenseCategories> {
    return this.licenseCategoriesService.create(createLicenseCategoryDto, req.user.userId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update license category' })
  @ApiParam({ name: 'id', description: 'License category UUID' })
  @ApiResponse({
    status: 200,
    description: 'License category updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'License category or license type not found',
  })
  @ApiResponse({
    status: 409,
    description: 'License category with this name already exists for this license type',
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Updated license category',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLicenseCategoryDto: UpdateLicenseCategoryDto,
    @Request() req: any,
  ): Promise<LicenseCategories> {
    return this.licenseCategoriesService.update(id, updateLicenseCategoryDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete license category' })
  @ApiParam({ name: 'id', description: 'License category UUID' })
  @ApiResponse({
    status: 200,
    description: 'License category deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'License category not found',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategory',
    description: 'Deleted license category',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.licenseCategoriesService.remove(id);
    return { message: 'License category deleted successfully' };
  }
}
