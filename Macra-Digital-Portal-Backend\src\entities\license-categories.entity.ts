import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  BeforeInsert,
  JoinColumn,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { LicenseTypes } from './license-types.entity';
import { LicenseCategoryDocument } from './license-category-document.entity';

@Entity('license_categories')
export class LicenseCategories {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  license_category_id: string;

  @Column({ type: 'uuid' })
  license_type_id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 20 })
  fee: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text' })
  authorizes: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  parent_id?: string;

  // Relations
  @ManyToOne(() => LicenseTypes)
  @JoinColumn({ name: 'license_type_id' })
  license_type: LicenseTypes;

  // Parent-child relationship for hierarchical categories
  @ManyToOne(() => LicenseCategories, (category) => category.children, { nullable: true })
  @JoinColumn({ name: 'parent_id' })
  parent?: LicenseCategories;

  @OneToMany(() => LicenseCategories, (category) => category.parent)
  children: LicenseCategories[];

  @ManyToOne(() => User, {nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToMany(() => LicenseCategoryDocument, (document) => document.license_category)
  license_category_documents: LicenseCategoryDocument[];

  @BeforeInsert()
  generateId() {
    if (!this.license_category_id) {
      this.license_category_id = uuidv4();
    }
  }
}
