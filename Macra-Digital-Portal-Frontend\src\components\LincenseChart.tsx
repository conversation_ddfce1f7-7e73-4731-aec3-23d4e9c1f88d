'use client';

import { useECharts } from '@/lib/echats';
import * as echarts from 'echarts';

// Define ECharts options type for better type safety
interface EChartsOption {
  animation?: boolean;
  tooltip?: any;
  legend?: any;
  grid?: any;
  xAxis?: any;
  yAxis?: any;
  series?: any[];
}

const LicenseChart = () => {
  const chartRef = useECharts({
    animation: false,
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      borderColor: '#e5e7eb',
      textStyle: { color: '#1f2937' },
    },
    legend: {
      data: ['Active', 'Pending', 'Expired'],
      bottom: 0,
      textStyle: { color: '#1f2937' },
    },
    grid: {
      left: 0,
      right: 0,
      top: 10,
      bottom: 30,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#1f2937' },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#1f2937' },
    },
    series: [
      {
        name: 'Active',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: { width: 3 },
        showSymbol: false,
        areaStyle: {
          opacity: 0.1,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(87, 181, 231, 0.5)' },
            { offset: 1, color: 'rgba(87, 181, 231, 0.1)' },
          ]),
        },
        emphasis: { focus: 'series' },
        color: 'rgba(87, 181, 231, 1)',
        data: [1250, 1320, 1290, 1400, 1450, 1380, 1482],
      },
      {
        name: 'Pending',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: { width: 3 },
        showSymbol: false,
        areaStyle: {
          opacity: 0.1,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(251, 191, 114, 0.5)' },
            { offset: 1, color: 'rgba(251, 191, 114, 0.1)' },
          ]),
        },
        emphasis: { focus: 'series' },
        color: 'rgba(251, 191, 114, 1)',
        data: [120, 132, 101, 134, 90, 70, 57],
      },
      {
        name: 'Expired',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: { width: 3 },
        showSymbol: false,
        areaStyle: {
          opacity: 0.1,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(252, 141, 98, 0.5)' },
            { offset: 1, color: 'rgba(252, 141, 98, 0.1)' },
          ]),
        },
        emphasis: { focus: 'series' },
        color: 'rgba(252, 141, 98, 1)',
        data: [45, 42, 50, 34, 30, 35, 40],
      },
    ],
  } as EChartsOption);

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden lg:col-span-2">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">License Status Overview</h3>
          <div className="flex space-x-3">
            <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50">
              Daily
            </button>
            <button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-button text-white bg-primary hover:bg-primary">
              Weekly
            </button>
            <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50">
              Monthly
            </button>
          </div>
        </div>
        <div ref={chartRef} className="mt-4" style={{ height: '300px' }} />
      </div>
    </div>
  );
};

export default LicenseChart;