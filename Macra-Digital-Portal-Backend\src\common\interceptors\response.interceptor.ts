import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface StandardResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  meta?: any;
  timestamp: string;
  path: string;
  statusCode: number;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, StandardResponse<T>> {
  private readonly logger = new Logger(ResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<StandardResponse<T>> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    return next.handle().pipe(
      map((data) => {
        // Already formatted
        if (this.isStandardResponse(data)) return data;

        // Skip file streams
        const isFileDownload =
          (response.getHeader('content-type') as string)?.includes('application/octet-stream') ||
          (response.getHeader('content-disposition') as string)?.includes('attachment');
        if (isFileDownload) return data;

        // Paginated: { data, meta }
        if (this.isPaginated(data)) {
          return this.wrapResponse(request, response, {
            data: data.data,
            meta: data.meta,
            message: 'Data retrieved successfully',
          });
        }

        // Array response
        if (Array.isArray(data)) {
          return this.wrapResponse(request, response, {
            data,
            meta: { total: data.length },
            message: 'Data retrieved successfully',
          });
        }

        // Object response
        if (typeof data === 'object' && data !== null) {
          const message =
            typeof data.message === 'string' && !('success' in data)
              ? data.message
              : this.getSuccessMessage(request.method, request.url);

          return this.wrapResponse(request, response, { data, message });
        }

        // Primitive/null
        return this.wrapResponse(request, response, {
          data,
          message: this.getSuccessMessage(request.method, request.url),
        });
      }),
    );
  }

  private wrapResponse(
    request: any,
    response: any,
    {
      data,
      message,
      meta = undefined,
    }: {
      data: any;
      message: string;
      meta?: any;
    }
  ): StandardResponse {
    return {
      success: true,
      message,
      data,
      meta,
      timestamp: new Date().toISOString(),
      path: request.url,
      statusCode: response.statusCode,
    };
  }

  private isStandardResponse(data: any): data is StandardResponse {
    return typeof data === 'object' && data?.success !== undefined && data?.timestamp !== undefined;
  }

  private isPaginated(data: any): data is { data: any; meta: any } {
    return typeof data === 'object' && 'data' in data && 'meta' in data;
  }

  private getSuccessMessage(method: string, url: string): string {
    const resource = this.extractResource(url);
    switch (method.toUpperCase()) {
      case 'GET': return `${resource} retrieved successfully`;
      case 'POST': return `${resource} created successfully`;
      case 'PUT':
      case 'PATCH': return `${resource} updated successfully`;
      case 'DELETE': return `${resource} deleted successfully`;
      default: return 'Operation completed successfully';
    }
  }

  private extractResource(url: string): string {
    const segments = url.split('/').filter(Boolean);
    const lastSegment = segments[segments.length - 1] || 'resource';
    return this.capitalizeWords(lastSegment.replace(/[-_]/g, ' '));
  }

  private capitalizeWords(input: string): string {
    return input
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
}
