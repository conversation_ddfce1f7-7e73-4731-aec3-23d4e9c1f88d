import React from 'react';
import Link from 'next/link';

interface StatusCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
  iconBgColor: string;
  iconTextColor: string;
  linkText: string;
  linkHref: string;
}

const StatusCard: React.FC<StatusCardProps> = ({
  title,
  value,
  icon,
  bgColor,
  iconBgColor,
  iconTextColor,
  linkText,
  linkHref
}) => {
  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className={`flex-shrink-0 ${iconBgColor} rounded-md p-3`}>
            <div className={`h-6 w-6 ${iconTextColor}`}>
              {icon}
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd>
                <div className="text-lg font-medium text-gray-900">{value}</div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-5 py-3">
        <div className="text-sm">
          <Link href={linkHref} className="font-medium text-primary hover:text-primary">
            {linkText}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default StatusCard;
