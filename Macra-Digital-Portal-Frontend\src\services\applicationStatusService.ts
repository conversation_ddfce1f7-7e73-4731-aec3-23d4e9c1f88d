'use client';

// Types for application status tracking
export interface ApplicationStatusTrackingData {
  id: string;
  applicationId: string;
  currentStatus: string;
  statusHistory: StatusHistoryEntry[];
  submittedDate: string;
  lastUpdated: string;
  estimatedCompletion?: string;
  assignedOfficer?: string;
  notes?: string;
}

export interface StatusHistoryEntry {
  status: string;
  timestamp: string;
  updatedBy: string;
  notes?: string;
}

export interface UpdateApplicationStatusData {
  status: string;
  notes?: string;
  assignedOfficer?: string;
  estimatedCompletion?: string;
}

// Application status constants
export const APPLICATION_STATUSES = {
  SUBMITTED: 'SUBMITTED',
  UNDER_REVIEW: 'UNDER_REVIEW', 
  ADDITIONAL_INFO_REQUIRED: 'ADDITIONAL_INFO_REQUIRED',
  TECHNICAL_REVIEW: 'TECHNICAL_REVIEW',
  PENDING_PAYMENT: 'PENDING_PAYMENT',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  WITHDRAWN: 'WITHDRAWN'
} as const;

export type ApplicationStatus = typeof APPLICATION_STATUSES[keyof typeof APPLICATION_STATUSES];

// Status display configuration
const STATUS_CONFIG = {
  [APPLICATION_STATUSES.SUBMITTED]: {
    displayName: 'Submitted',
    colorClass: 'text-blue-600 bg-blue-100',
    progressPercentage: 10
  },
  [APPLICATION_STATUSES.UNDER_REVIEW]: {
    displayName: 'Under Review',
    colorClass: 'text-yellow-600 bg-yellow-100',
    progressPercentage: 30
  },
  [APPLICATION_STATUSES.ADDITIONAL_INFO_REQUIRED]: {
    displayName: 'Additional Information Required',
    colorClass: 'text-orange-600 bg-orange-100',
    progressPercentage: 25
  },
  [APPLICATION_STATUSES.TECHNICAL_REVIEW]: {
    displayName: 'Technical Review',
    colorClass: 'text-purple-600 bg-purple-100',
    progressPercentage: 60
  },
  [APPLICATION_STATUSES.PENDING_PAYMENT]: {
    displayName: 'Pending Payment',
    colorClass: 'text-indigo-600 bg-indigo-100',
    progressPercentage: 80
  },
  [APPLICATION_STATUSES.APPROVED]: {
    displayName: 'Approved',
    colorClass: 'text-green-600 bg-green-100',
    progressPercentage: 100
  },
  [APPLICATION_STATUSES.REJECTED]: {
    displayName: 'Rejected',
    colorClass: 'text-red-600 bg-red-100',
    progressPercentage: 100
  },
  [APPLICATION_STATUSES.WITHDRAWN]: {
    displayName: 'Withdrawn',
    colorClass: 'text-gray-600 bg-gray-100',
    progressPercentage: 0
  }
};

// Roles that can update application status
const UPDATE_ROLES = [
  'ADMIN',
  'LICENSING_OFFICER',
  'TECHNICAL_REVIEWER',
  'SUPERVISOR'
];

class ApplicationStatusService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';
  }

  // Get application status tracking data
  async getApplicationStatusTracking(applicationId: string): Promise<ApplicationStatusTrackingData> {
    try {
      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch application status: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching application status:', error);
      throw error;
    }
  }

  // Update application status
  async updateApplicationStatus(
    applicationId: string, 
    updateData: UpdateApplicationStatusData
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Failed to update application status: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error updating application status:', error);
      throw error;
    }
  }

  // Check if user can update status based on roles
  canUpdateStatus(userRoles: string[]): boolean {
    return userRoles.some(role => UPDATE_ROLES.includes(role.toUpperCase()));
  }

  // Get display name for status
  getStatusDisplayName(status: string): string {
    return STATUS_CONFIG[status as ApplicationStatus]?.displayName || status;
  }

  // Get color class for status
  getStatusColorClass(status: string): string {
    return STATUS_CONFIG[status as ApplicationStatus]?.colorClass || 'text-gray-600 bg-gray-100';
  }

  // Calculate progress percentage
  calculateProgressPercentage(status: string): number {
    return STATUS_CONFIG[status as ApplicationStatus]?.progressPercentage || 0;
  }

  // Get estimated completion date
  getEstimatedCompletion(status: string, submittedDate?: string): string | null {
    if (!submittedDate || status === APPLICATION_STATUSES.APPROVED || status === APPLICATION_STATUSES.REJECTED) {
      return null;
    }

    // Estimated processing times in days
    const processingTimes: Record<
      | typeof APPLICATION_STATUSES.SUBMITTED
      | typeof APPLICATION_STATUSES.UNDER_REVIEW
      | typeof APPLICATION_STATUSES.ADDITIONAL_INFO_REQUIRED
      | typeof APPLICATION_STATUSES.TECHNICAL_REVIEW
      | typeof APPLICATION_STATUSES.PENDING_PAYMENT,
      number
    > = {
      [APPLICATION_STATUSES.SUBMITTED]: 14,
      [APPLICATION_STATUSES.UNDER_REVIEW]: 10,
      [APPLICATION_STATUSES.ADDITIONAL_INFO_REQUIRED]: 21,
      [APPLICATION_STATUSES.TECHNICAL_REVIEW]: 7,
      [APPLICATION_STATUSES.PENDING_PAYMENT]: 3
    };

    const daysToAdd =
      status in processingTimes
        ? processingTimes[status as keyof typeof processingTimes]
        : 14;
    const submitted = new Date(submittedDate);
    const estimated = new Date(submitted);
    estimated.setDate(estimated.getDate() + daysToAdd);

    return estimated.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }

  // Get all available statuses
  getAllStatuses(): { value: string; label: string }[] {
    return Object.values(APPLICATION_STATUSES).map(status => ({
      value: status,
      label: this.getStatusDisplayName(status)
    }));
  }

  // Get status history for an application
  async getStatusHistory(applicationId: string): Promise<StatusHistoryEntry[]> {
    try {
      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status/history`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch status history: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching status history:', error);
      throw error;
    }
  }

  // Helper method to get auth token
  private getAuthToken(): string {
    try {
      const authData = localStorage.getItem('auth_token');
      return authData || '';
    } catch {
      return '';
    }
  }
}

// Export singleton instance
export const applicationStatusService = new ApplicationStatusService();

// Export default
export default applicationStatusService;