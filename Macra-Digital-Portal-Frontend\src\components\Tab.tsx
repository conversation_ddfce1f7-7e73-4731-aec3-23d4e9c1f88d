"use client"
import React, { useState } from "react";

interface TabProps {
  tabs: string[];
}

const Tab = ({ tabs }: TabProps) => {
  const [activeTab, setActiveTab] = useState('Overview');

  return (
    <div className="border-t border-gray-200 px-4 sm:px-6">
        <div className="py-3 flex space-x-8">
          {tabs.map((tab: any) => (
            <button
              key={tab}
              className={`tab-button text-sm px-1 py-2 ${activeTab === tab ? 'active' : 'text-gray-500'}`}
              onClick={() => setActiveTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>
    </div>
  );
};

export default Tab;