/**
 * Example usage of the polymorphic payment system
 * This file demonstrates how to use the updated payment system
 * with different entity types.
 */

import { PaymentsService } from '../payments.service';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { PaymentType, Currency } from '../entities/payment.entity';

export class PaymentExamples {
  constructor(private readonly paymentsService: PaymentsService) {}

  /**
   * Example: Create payment for an application
   */
  async createApplicationPayment(
    applicationId: string,
    userId: string,
    createdBy: string
  ) {
    const paymentDto: CreatePaymentDto = {
      invoice_number: `APP-${Date.now()}`,
      amount: 1000.00,
      currency: Currency.MWK,
      payment_type: PaymentType.APPLICATION_FEE,
      description: 'Application processing fee for telecommunications license',
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      issue_date: new Date().toISOString(),
      user_id: userId,
      created_by: createdBy,
      entity_type: 'application',
      entity_id: applicationId,
    };

    return await this.paymentsService.createPayment(paymentDto);
  }

  /**
   * Example: Create payment for a license renewal
   */
  async createLicenseRenewalPayment(
    licenseId: string,
    userId: string,
    createdBy: string
  ) {
    const paymentDto: CreatePaymentDto = {
      invoice_number: `LIC-${Date.now()}`,
      amount: 5000.00,
      currency: Currency.MWK,
      payment_type: PaymentType.RENEWAL_FEE,
      description: 'Annual license renewal fee',
      due_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days from now
      issue_date: new Date().toISOString(),
      user_id: userId,
      created_by: createdBy,
      entity_type: 'license',
      entity_id: licenseId,
    };

    return await this.paymentsService.createPayment(paymentDto);
  }

  /**
   * Example: Create payment for procurement
   */
  async createProcurementPayment(
    procurementId: string,
    userId: string,
    createdBy: string,
    amount: number
  ) {
    const paymentDto: CreatePaymentDto = {
      invoice_number: `PROC-${Date.now()}`,
      amount,
      currency: Currency.MWK,
      payment_type: PaymentType.PROCUREMENT_FEE,
      description: 'Procurement participation fee',
      due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
      issue_date: new Date().toISOString(),
      user_id: userId,
      created_by: createdBy,
      entity_type: 'procurement',
      entity_id: procurementId,
    };

    return await this.paymentsService.createPayment(paymentDto);
  }

  /**
   * Example: Create inspection fee payment
   */
  async createInspectionPayment(
    inspectionId: string,
    userId: string,
    createdBy: string
  ) {
    const paymentDto: CreatePaymentDto = {
      invoice_number: `INSP-${Date.now()}`,
      amount: 500.00,
      currency: Currency.MWK,
      payment_type: PaymentType.INSPECTION_FEE,
      description: 'Site inspection fee',
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      issue_date: new Date().toISOString(),
      user_id: userId,
      created_by: createdBy,
      entity_type: 'inspection',
      entity_id: inspectionId,
    };

    return await this.paymentsService.createPayment(paymentDto);
  }

  /**
   * Example: Get all payments for an application
   */
  async getApplicationPayments(applicationId: string) {
    return await this.paymentsService.getPaymentsByEntity(
      'application',
      applicationId,
      { page: 1, limit: 10 }
    );
  }

  /**
   * Example: Get all payments for a license
   */
  async getLicensePayments(licenseId: string) {
    return await this.paymentsService.getPaymentsByEntity(
      'license',
      licenseId,
      { page: 1, limit: 10 }
    );
  }

  /**
   * Example: Using the convenience method to create payment for entity
   */
  async createPaymentForAnyEntity(
    entityType: string,
    entityId: string,
    userId: string,
    createdBy: string,
    paymentType: PaymentType,
    amount: number,
    description: string
  ) {
    return await this.paymentsService.createPaymentForEntity(
      entityType,
      entityId,
      {
        invoice_number: `${entityType.toUpperCase()}-${Date.now()}`,
        amount,
        currency: Currency.MWK,
        payment_type: paymentType,
        description,
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        issue_date: new Date().toISOString(),
        user_id: userId,
        created_by: createdBy,
      }
    );
  }

  /**
   * Example: Integration with application service
   */
  async handleApplicationSubmission(
    applicationId: string,
    userId: string,
    adminId: string
  ) {
    // Create application fee payment
    const payment = await this.createApplicationPayment(
      applicationId,
      userId,
      adminId
    );

    console.log(`Payment created for application ${applicationId}:`, {
      paymentId: payment.payment_id,
      invoiceNumber: payment.invoice_number,
      amount: payment.amount,
      dueDate: payment.due_date,
    });

    return payment;
  }

  /**
   * Example: Integration with license renewal process
   */
  async handleLicenseRenewal(
    licenseId: string,
    userId: string,
    adminId: string
  ) {
    // Create renewal fee payment
    const payment = await this.createLicenseRenewalPayment(
      licenseId,
      userId,
      adminId
    );

    // Get all existing payments for this license
    const existingPayments = await this.getLicensePayments(licenseId);

    console.log(`Renewal payment created for license ${licenseId}:`, {
      newPayment: payment.payment_id,
      totalPayments: existingPayments.total,
    });

    return {
      newPayment: payment,
      paymentHistory: existingPayments,
    };
  }
}

/**
 * Example service integration
 */
export class ApplicationService {
  constructor(private readonly paymentsService: PaymentsService) {}

  async submitApplication(applicationData: any, userId: string) {
    // ... application creation logic ...

    // Create payment for the application
    const payment = await this.paymentsService.createPaymentForEntity(
      'application',
      applicationData.id,
      {
        invoice_number: `APP-${applicationData.id.slice(-8)}`,
        amount: 1000.00,
        currency: Currency.MWK,
        payment_type: PaymentType.APPLICATION_FEE,
        description: `Application fee for ${applicationData.licenseType}`,
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        issue_date: new Date().toISOString(),
        user_id: userId,
        created_by: userId, // In this case, user creates their own payment
      }
    );

    return {
      application: applicationData,
      payment: payment,
    };
  }
}
