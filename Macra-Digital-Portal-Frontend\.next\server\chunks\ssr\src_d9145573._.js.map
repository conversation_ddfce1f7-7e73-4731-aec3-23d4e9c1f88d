{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: {\r\n    applicant_id: string;\r\n    name: string;\r\n    business_registration_number: string;\r\n    tpin: string;\r\n    website: string;\r\n    email: string;\r\n    phone: string;\r\n    fax?: string;\r\n    level_of_insurance_cover?: string;\r\n    date_incorporation: string;\r\n    place_incorporation: string;\r\n    created_at: string;\r\n    updated_at: string;\r\n  };\r\n  license_category?: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type?: {\r\n      license_type_id: string;\r\n      name: string;\r\n      code: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAsFO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\n\r\nexport interface AppNotification {\r\n  notification_id: string;\r\n  user_id: string;\r\n  application_id: string;\r\n  application_number: string;\r\n  license_category_name: string;\r\n  title: string;\r\n  message: string;\r\n  type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection';\r\n  status: 'unread' | 'read';\r\n  priority: 'low' | 'medium' | 'high';\r\n  created_at: string;\r\n  read_at?: string;\r\n  metadata?: {\r\n    old_status?: ApplicationStatus;\r\n    new_status?: ApplicationStatus;\r\n    step?: number;\r\n    progress_percentage?: number;\r\n  };\r\n}\r\n\r\nexport interface NotificationSummary {\r\n  total_count: number;\r\n  unread_count: number;\r\n  notifications: AppNotification[];\r\n}\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications?${queryParams.toString()}`;\r\n      console.log(`[NotificationService] Fetching notifications from: ${endpoint}`);\r\n      \r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response);\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.total_count || 0,\r\n        unread_count: data.unread_count || 0,\r\n        notifications: Array.isArray(data.notifications) ? data.notifications : []\r\n      };\r\n    } catch (error: any) {\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications?${new URLSearchParams(params || {}).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read\r\n  async markAllAsRead(): Promise<void> {\r\n    const response = await apiClient.patch('/notifications/mark-all-read');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: ApplicationStatus,\r\n  newStatus: ApplicationStatus,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AA6BO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,CAAC,eAAe,EAAE,YAAY,QAAQ,IAAI;YAC3D,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,UAAU;YAE5E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAEhC,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,WAAW,IAAI;gBACjC,cAAc,KAAK,YAAY,IAAI;gBACnC,eAAe,MAAM,OAAO,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,GAAG,EAAE;YAC5E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,MAAM,QAAQ,EAAE,QAAQ;gBAClC,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,CAAC,eAAe,EAAE,IAAI,gBAAgB,UAAU,CAAC,GAAG,QAAQ,IAAI;YAC5E;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,KAAK,CAAC;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iCAAiC;IACjC,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;QACvC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,gBAAgB;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,CAAC,EAAE,EAAE,mBAAmB,WAAW,CAAC,GAAG;IACjF,MAAM,WAAW,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG;IAE5C,MAAM,WAAW;QACf,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6DAA6D,CAAC;YACrI,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,iCAAiC,EAAE,eAAe,SAAS,kCAAkC,CAAC;YACrK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8BAA8B,EAAE,eAAe,SAAS,sCAAsC,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,uDAAuD,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,wEAAwE,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,kCAAkC,CAAC;YAC1G,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6BAA6B,EAAE,UAAU,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;QACpI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAU,IAAI;AAChC;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAE3C,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,CAAC,IAAI,EAAE,mBAAmB;QAC1C,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,uHAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { notificationService, AppNotification } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({ isOpen, onClose }) => {\r\n  const { user } = useAuth();\r\n  const { showError, showSuccess } = useToast();\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('all');\r\n\r\n  // Fetch notifications\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!user) return;\r\n    \r\n    setLoading(true);\r\n    try {\r\n      const data = await notificationService.getUserNotifications({ \r\n        limit: 50,\r\n        status: filter === 'unread' ? 'unread' : undefined\r\n      });\r\n      \r\n      // Validate response data\r\n      if (data && Array.isArray(data.notifications)) {\r\n        setNotifications(data.notifications);\r\n      } else {\r\n        console.warn('Invalid notification data received:', data);\r\n        setNotifications([]);\r\n      }\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      const errorResponse = error && typeof error === 'object' && 'response' in error ? error.response : null;\r\n      \r\n      console.error('Error fetching notifications:', {\r\n        error: errorMessage,\r\n        response: errorResponse && typeof errorResponse === 'object' && 'data' in errorResponse ? errorResponse.data : null,\r\n        status: errorResponse && typeof errorResponse === 'object' && 'status' in errorResponse ? errorResponse.status : null,\r\n        filter\r\n      });\r\n      \r\n      setNotifications([]);\r\n      showError('Failed to load notifications. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [user, filter, showError]);\r\n\r\n  // Initial fetch and refetch when filter changes\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isOpen, fetchNotifications]);\r\n\r\n  // Mark notification as read\r\n  const markAsRead = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.markAsRead(notificationId);\r\n      setNotifications(prev => \r\n        prev.map(notification => \r\n          notification.notification_id === notificationId \r\n            ? { ...notification, status: 'read' as const }\r\n            : notification\r\n        )\r\n      );\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  // Mark all as read\r\n  const markAllAsRead = async () => {\r\n    try {\r\n      await notificationService.markAllAsRead();\r\n      setNotifications(prev => \r\n        prev.map(notification => ({ ...notification, status: 'read' as const }))\r\n      );\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  // Delete notification\r\n  const deleteNotification = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.deleteNotification(notificationId);\r\n      setNotifications(prev => \r\n        prev.filter(notification => notification.notification_id !== notificationId)\r\n      );\r\n      showSuccess('Notification deleted');\r\n    } catch (error) {\r\n      console.error('Error deleting notification:', error);\r\n      showError('Failed to delete notification');\r\n    }\r\n  };\r\n\r\n  // Get notification icon based on type\r\n  const getNotificationIcon = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'status_change':\r\n        return 'ri-information-line';\r\n      case 'approval':\r\n        return 'ri-check-double-line';\r\n      case 'rejection':\r\n        return 'ri-close-circle-line';\r\n      case 'document_required':\r\n        return 'ri-file-text-line';\r\n      case 'reminder':\r\n        return 'ri-alarm-line';\r\n      default:\r\n        return 'ri-notification-line';\r\n    }\r\n  };\r\n\r\n  // Get notification color based on type\r\n  const getNotificationColor = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'approval':\r\n        return 'text-green-600';\r\n      case 'rejection':\r\n        return 'text-red-600';\r\n      case 'document_required':\r\n        return 'text-orange-600';\r\n      case 'reminder':\r\n        return 'text-yellow-600';\r\n      default:\r\n        return 'text-blue-600';\r\n    }\r\n  };\r\n\r\n  // Format time ago\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) return 'Just now';\r\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\r\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\r\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\r\n    \r\n    return date.toLocaleDateString();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              <i className=\"ri-notification-line mr-2\"></i>\r\n              Notifications\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Filters and Actions */}\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'all'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                All\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'unread'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                Unread\r\n              </button>\r\n            </div>\r\n\r\n            {notifications.some(n => n.status === 'unread') && (\r\n              <button\r\n                onClick={markAllAsRead}\r\n                className=\"text-sm text-primary hover:text-primary-dark focus:outline-none\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n\r\n          {/* Notifications List */}\r\n          <div className=\"max-h-96 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-8 text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n                <p className=\"mt-4 text-sm text-gray-500 dark:text-gray-400\">Loading notifications...</p>\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-8 text-center\">\r\n                <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-4\"></i>\r\n                <p className=\"text-gray-500 dark:text-gray-400\">\r\n                  {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-2\">\r\n                {notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.notification_id}\r\n                    className={`p-4 rounded-lg border transition-colors ${\r\n                      notification.status === 'unread' \r\n                        ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' \r\n                        : 'bg-white border-gray-200 dark:bg-gray-700 dark:border-gray-600'\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className={`flex-shrink-0 ${getNotificationColor(notification.type)}`}>\r\n                          <i className={`${getNotificationIcon(notification.type)} text-xl`}></i>\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                              {notification.title}\r\n                            </h4>\r\n                            {notification.status === 'unread' && (\r\n                              <div className=\"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2\"></div>\r\n                            )}\r\n                          </div>\r\n                          <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\r\n                            {notification.message}\r\n                          </p>\r\n                          <div className=\"mt-2 flex items-center justify-between\">\r\n                            <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                              {formatTimeAgo(notification.created_at)}\r\n                            </p>\r\n                            <div className=\"flex space-x-2\">\r\n                              {notification.status === 'unread' && (\r\n                                <button\r\n                                  onClick={() => markAsRead(notification.notification_id)}\r\n                                  className=\"text-xs text-primary hover:text-primary-dark focus:outline-none\"\r\n                                >\r\n                                  Mark as read\r\n                                </button>\r\n                              )}\r\n                              <button\r\n                                onClick={() => deleteNotification(notification.notification_id)}\r\n                                className=\"text-xs text-red-600 hover:text-red-700 focus:outline-none\"\r\n                              >\r\n                                Delete\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          <div className=\"flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Close\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,oBAAsD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAC9E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvD,sBAAsB;IACtB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;gBAC1D,OAAO;gBACP,QAAQ,WAAW,WAAW,WAAW;YAC3C;YAEA,yBAAyB;YACzB,IAAI,QAAQ,MAAM,OAAO,CAAC,KAAK,aAAa,GAAG;gBAC7C,iBAAiB,KAAK,aAAa;YACrC,OAAO;gBACL,QAAQ,IAAI,CAAC,uCAAuC;gBACpD,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,gBAAgB,SAAS,OAAO,UAAU,YAAY,cAAc,QAAQ,MAAM,QAAQ,GAAG;YAEnG,QAAQ,KAAK,CAAC,iCAAiC;gBAC7C,OAAO;gBACP,UAAU,iBAAiB,OAAO,kBAAkB,YAAY,UAAU,gBAAgB,cAAc,IAAI,GAAG;gBAC/G,QAAQ,iBAAiB,OAAO,kBAAkB,YAAY,YAAY,gBAAgB,cAAc,MAAM,GAAG;gBACjH;YACF;YAEA,iBAAiB,EAAE;YACnB,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAM;QAAQ;KAAU;IAE5B,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAmB;IAE/B,4BAA4B;IAC5B,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;YACrC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,eAAe,KAAK,iBAC7B;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,IAC3C;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,mBAAmB;IACnB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,aAAa;YACvC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,CAAC;YAExE,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ;IACF;IAEA,sBAAsB;IACtB,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;YAC7C,iBAAiB,CAAA,OACf,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,eAAe,KAAK;YAE/D,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,UAAU;QACZ;IACF;IAEA,sCAAsC;IACtC,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;QAC5E,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;QAE/E,OAAO,KAAK,kBAAkB;IAChC;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAAgC;;;;;;;0CAG/C,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,QACP,0BACA,0GACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,WACP,0BACA,0GACJ;kDACH;;;;;;;;;;;;4BAKF,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,2BACpC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;mCAE7D,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAE,WAAU;8CACV,WAAW,WAAW,4BAA4B;;;;;;;;;;;iDAIvD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,WAAW,CAAC,wCAAwC,EAClD,aAAa,MAAM,KAAK,WACpB,wEACA,kEACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,cAAc,EAAE,qBAAqB,aAAa,IAAI,GAAG;8DACxE,cAAA,8OAAC;wDAAE,WAAW,GAAG,oBAAoB,aAAa,IAAI,EAAE,QAAQ,CAAC;;;;;;;;;;;8DAEnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,aAAa,KAAK;;;;;;gEAEpB,aAAa,MAAM,KAAK,0BACvB,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAGnB,8OAAC;4DAAE,WAAU;sEACV,aAAa,OAAO;;;;;;sEAEvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,cAAc,aAAa,UAAU;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;wEACZ,aAAa,MAAM,KAAK,0BACvB,8OAAC;4EACC,SAAS,IAAM,WAAW,aAAa,eAAe;4EACtD,WAAU;sFACX;;;;;;sFAIH,8OAAC;4EACC,SAAS,IAAM,mBAAmB,aAAa,eAAe;4EAC9D,WAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAxCN,aAAa,eAAe;;;;;;;;;;;;;;;kCAuD3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from './NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,wBAAwB;QACrD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,WAAW;0BAErC,cAAA,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,8OAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,8OAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,iJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 1599, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/LicenseCard.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\n\r\ninterface LicenseCardProps {\r\n  id: string;\r\n  title: string;\r\n  licenseNumber: string;\r\n  status: 'Active' | 'Expiring Soon' | 'Expired' | 'Pending';\r\n  issueDate: string;\r\n  expirationDate: string;\r\n}\r\n\r\nconst LicenseCard: React.FC<LicenseCardProps> = ({\r\n  id,\r\n  title,\r\n  licenseNumber,\r\n  status,\r\n  issueDate,\r\n  expirationDate\r\n}) => {\r\n  const getStatusStyles = (status: string) => {\r\n    switch (status) {\r\n      case 'Active':\r\n        return {\r\n          badge: 'bg-green-100 text-green-800',\r\n          iconBg: 'bg-green-100',\r\n          iconColor: 'text-green-600'\r\n        };\r\n      case 'Expiring Soon':\r\n        return {\r\n          badge: 'bg-orange-100 text-orange-800',\r\n          iconBg: 'bg-orange-100',\r\n          iconColor: 'text-orange-600'\r\n        };\r\n      case 'Expired':\r\n        return {\r\n          badge: 'bg-red-100 text-red-800',\r\n          iconBg: 'bg-red-100',\r\n          iconColor: 'text-red-600'\r\n        };\r\n      case 'Pending':\r\n        return {\r\n          badge: 'bg-yellow-100 text-yellow-800',\r\n          iconBg: 'bg-yellow-100',\r\n          iconColor: 'text-yellow-600'\r\n        };\r\n      default:\r\n        return {\r\n          badge: 'bg-gray-100 text-gray-800',\r\n          iconBg: 'bg-gray-100',\r\n          iconColor: 'text-gray-600'\r\n        };\r\n    }\r\n  };\r\n\r\n  const statusStyles = getStatusStyles(status);\r\n\r\n  return (\r\n    <div className=\"license-card bg-white border border-gray-200 rounded-lg overflow-hidden hover:transform hover:-translate-y-1 transition-transform duration-300\">\r\n      <div className=\"p-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center\">\r\n            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${statusStyles.iconBg}`}>\r\n              <div className={`w-5 h-5 flex items-center justify-center ${statusStyles.iconColor}`}>\r\n                <i className=\"ri-verified-badge-line\"></i>\r\n              </div>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-sm font-medium text-gray-900\">{title}</h3>\r\n              <p className=\"text-xs text-gray-500\">{licenseNumber}</p>\r\n            </div>\r\n          </div>\r\n          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles.badge}`}>\r\n            {status}\r\n          </span>\r\n        </div>\r\n        <div className=\"mt-3 grid grid-cols-2 gap-2 text-xs\">\r\n          <div>\r\n            <span className=\"text-gray-500\">Issue Date</span>\r\n            <p className=\"font-medium text-gray-900 mt-1\">{issueDate}</p>\r\n          </div>\r\n          <div>\r\n            <span className=\"text-gray-500\">Expiration</span>\r\n            <p className=\"font-medium text-gray-900 mt-1\">{expirationDate}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"bg-gray-50 px-4 py-2 flex justify-between\">\r\n        <Link \r\n          href={`/customer/licenses/${id}`} \r\n          className=\"text-xs font-medium text-primary hover:text-primary\"\r\n        >\r\n          View details\r\n        </Link>\r\n        <Link \r\n          href={`/customer/licenses/${id}/renew`} \r\n          className=\"text-xs font-medium text-gray-500 hover:text-gray-700\"\r\n        >\r\n          Renew\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LicenseCard;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,cAA0C,CAAC,EAC/C,EAAE,EACF,KAAK,EACL,aAAa,EACb,MAAM,EACN,SAAS,EACT,cAAc,EACf;IACC,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;YACF;gBACE,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;QACJ;IACF;IAEA,MAAM,eAAe,gBAAgB;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,wDAAwD,EAAE,aAAa,MAAM,EAAE;kDAC9F,cAAA,8OAAC;4CAAI,WAAW,CAAC,yCAAyC,EAAE,aAAa,SAAS,EAAE;sDAClF,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;0CAG1C,8OAAC;gCAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,KAAK,EAAE;0CAC7G;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAEjD,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,mBAAmB,EAAE,IAAI;wBAChC,WAAU;kCACX;;;;;;kCAGD,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,mBAAmB,EAAE,GAAG,MAAM,CAAC;wBACtC,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/PaymentCard.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\n\r\ninterface PaymentCardProps {\r\n  id: string;\r\n  title: string;\r\n  amount: string;\r\n  dueDate: string;\r\n  status: 'Due' | 'Overdue' | 'Paid';\r\n  description?: string;\r\n}\r\n\r\nconst PaymentCard: React.FC<PaymentCardProps> = ({\r\n  id,\r\n  title,\r\n  amount,\r\n  dueDate,\r\n  status,\r\n  description\r\n}) => {\r\n  const getStatusStyles = (status: string) => {\r\n    switch (status) {\r\n      case 'Due':\r\n        return {\r\n          bgColor: 'bg-yellow-50',\r\n          borderColor: 'border-yellow-100',\r\n          iconBg: 'bg-yellow-100',\r\n          iconColor: 'text-yellow-600',\r\n          badgeBg: 'bg-yellow-100',\r\n          badgeText: 'text-yellow-800'\r\n        };\r\n      case 'Overdue':\r\n        return {\r\n          bgColor: 'bg-red-50',\r\n          borderColor: 'border-red-100',\r\n          iconBg: 'bg-red-100',\r\n          iconColor: 'text-red-600',\r\n          badgeBg: 'bg-red-100',\r\n          badgeText: 'text-red-800'\r\n        };\r\n      case 'Paid':\r\n        return {\r\n          bgColor: 'bg-green-50',\r\n          borderColor: 'border-green-100',\r\n          iconBg: 'bg-green-100',\r\n          iconColor: 'text-green-600',\r\n          badgeBg: 'bg-green-100',\r\n          badgeText: 'text-green-800'\r\n        };\r\n      default:\r\n        return {\r\n          bgColor: 'bg-gray-50',\r\n          borderColor: 'border-gray-100',\r\n          iconBg: 'bg-gray-100',\r\n          iconColor: 'text-gray-600',\r\n          badgeBg: 'bg-gray-100',\r\n          badgeText: 'text-gray-800'\r\n        };\r\n    }\r\n  };\r\n\r\n  const statusStyles = getStatusStyles(status);\r\n\r\n  return (\r\n    <div className={`${statusStyles.bgColor} border ${statusStyles.borderColor} rounded-lg p-4`}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center\">\r\n          <div className={`w-8 h-8 flex items-center justify-center rounded-full ${statusStyles.iconBg} ${statusStyles.iconColor}`}>\r\n            <i className=\"ri-calendar-line\"></i>\r\n          </div>\r\n          <div className=\"ml-3\">\r\n            <p className=\"text-sm font-medium text-gray-900\">{title}</p>\r\n            <p className=\"text-xs text-gray-500\">{description || dueDate}</p>\r\n          </div>\r\n        </div>\r\n        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles.badgeBg} ${statusStyles.badgeText}`}>\r\n          {amount}\r\n        </span>\r\n      </div>\r\n      {status !== 'Paid' && (\r\n        <div className=\"mt-2 text-right\">\r\n          <Link \r\n            href={`/customer/payments/${id}`} \r\n            className=\"text-xs font-medium text-primary hover:text-primary\"\r\n          >\r\n            Pay Now\r\n          </Link>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PaymentCard;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,cAA0C,CAAC,EAC/C,EAAE,EACF,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACZ;IACC,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,SAAS;oBACT,aAAa;oBACb,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;oBACT,aAAa;oBACb,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;oBACT,aAAa;oBACb,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,WAAW;gBACb;YACF;gBACE,OAAO;oBACL,SAAS;oBACT,aAAa;oBACb,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,WAAW;gBACb;QACJ;IACF;IAEA,MAAM,eAAe,gBAAgB;IAErC,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,CAAC,QAAQ,EAAE,aAAa,WAAW,CAAC,eAAe,CAAC;;0BACzF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,sDAAsD,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE,aAAa,SAAS,EAAE;0CACtH,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAyB,eAAe;;;;;;;;;;;;;;;;;;kCAGzD,8OAAC;wBAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,OAAO,CAAC,CAAC,EAAE,aAAa,SAAS,EAAE;kCACzI;;;;;;;;;;;;YAGJ,WAAW,wBACV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAM,CAAC,mBAAmB,EAAE,IAAI;oBAChC,WAAU;8BACX;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 1971, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/customer-api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport { processApiResponse } from './authUtils';\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Create axios instance for customer portal (same as staff portal)\r\nconst customerApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 120000, // Increased timeout to match main API client (120 seconds)\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Create auth-specific client (same as staff portal)\r\nconst customerAuthApiClient: AxiosInstance = axios.create({\r\n  baseURL: `${API_BASE_URL}/auth`,\r\n  timeout: 120000, // Increased timeout to match main API client\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Add debug logging to auth client (only in development)\r\ncustomerAuthApiClient.interceptors.request.use(\r\n  (config) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Request:', {\r\n        url: `${config.baseURL}${config.url}`,\r\n        method: config.method,\r\n        headers: config.headers,\r\n        data: config.data\r\n      });\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('Customer Auth API Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\ncustomerAuthApiClient.interceptors.response.use(\r\n  (response) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Response Success:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.config.url\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Customer Auth API Interceptor Error:', {\r\n        message: error?.message || 'Unknown error',\r\n        code: error?.code || 'NO_CODE',\r\n        status: error?.response?.status || 'NO_STATUS',\r\n        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',\r\n        url: error?.config?.url || 'NO_URL',\r\n        method: error?.config?.method || 'NO_METHOD',\r\n        baseURL: error?.config?.baseURL || 'NO_BASE_URL',\r\n        isAxiosError: error?.isAxiosError || false,\r\n        responseData: error?.response?.data || 'NO_RESPONSE_DATA',\r\n        requestData: error?.config?.data || 'NO_REQUEST_DATA',\r\n        headers: error?.config?.headers || 'NO_HEADERS'\r\n      });\r\n    }\r\n\r\n    // Don't handle 401 here, let the login method handle it\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Request interceptor to add auth token\r\ncustomerApiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get('auth_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling with retry logic\r\ncustomerApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    return response;\r\n  },\r\n  async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosError['config'] & {\r\n      _retry?: boolean;\r\n      _retryCount?: number;\r\n    };\r\n\r\n    // Handle 429 Rate Limiting\r\n    if (error.response?.status === 429) {\r\n      if (!originalRequest._retry) {\r\n        originalRequest._retry = true;\r\n        \r\n        // Get retry delay from headers or use exponential backoff\r\n        const retryAfter = error.response.headers['retry-after'];\r\n        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n        \r\n        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n        \r\n        // Don't retry more than 3 times\r\n        if (originalRequest._retryCount <= 3) {\r\n          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n          \r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return customerApiClient(originalRequest);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle 401 Unauthorized\r\n    if (error.response?.status === 401) {\r\n      // Clear auth token and redirect to login\r\n      Cookies.remove('auth_token');\r\n      Cookies.remove('auth_user');\r\n      window.location.href = '/auth/login';\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API Service Class\r\nexport class CustomerApiService {\r\n  public api: AxiosInstance;\r\n  private pendingRequests: Map<string, Promise<unknown>> = new Map();\r\n\r\n  constructor() {\r\n    this.api = customerApiClient;\r\n  }\r\n\r\n  // Request deduplication helper\r\n  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key) as Promise<T>;\r\n    }\r\n\r\n    const promise = requestFn().finally(() => {\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, promise);\r\n    return promise;\r\n  }\r\n\r\n  // Set auth token\r\n  setAuthToken(token: string) {\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Remove auth token\r\n  removeAuthToken() {\r\n    delete this.api.defaults.headers.common['Authorization'];\r\n  }\r\n\r\n  async logout() {\r\n    const response = await customerAuthApiClient.post('/logout');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async refreshToken() {\r\n    const response = await customerAuthApiClient.post('/refresh');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // 2FA endpoints\r\n  async generateTwoFactorCode(userId: string, action: string) {\r\n    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async verify2FA(data: { user_id: string; code: string; unique: string }) {\r\n    const response = await customerAuthApiClient.post('/verify-2fa', data);\r\n\r\n    // Handle response structure consistently with login\r\n    if (processApiResponse(response)?.data) {\r\n      const authData = processApiResponse(response).data;\r\n      \r\n      // Map backend field names to frontend expected format\r\n      const mappedAuthData = {\r\n        access_token: authData.access_token,\r\n        user: {\r\n          id: authData.user.user_id,\r\n          firstName: authData.user.first_name,\r\n          lastName: authData.user.last_name,\r\n          email: authData.user.email,\r\n          roles: authData.user.roles || [],\r\n          isAdmin: (authData.user.roles || []).includes('administrator'),\r\n          profileImage: authData.user.profile_image,\r\n          createdAt: authData.user.created_at || new Date().toISOString(),\r\n          lastLogin: authData.user.last_login,\r\n          organizationName: authData.user.organization_name,\r\n          two_factor_enabled: authData.user.two_factor_enabled\r\n        }\r\n      };\r\n      \r\n      return mappedAuthData;\r\n    }\r\n\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: { access_token: string; user_id: string }) {\r\n    const response = await customerAuthApiClient.post('/setup-2fa', data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // User profile endpoints\r\n  async getProfile() {\r\n    return this.deduplicateRequest('getProfile', async () => {\r\n      const response = await this.api.get('/users/profile');\r\n      return processApiResponse(response);\r\n    });\r\n  }\r\n\r\n  async updateProfile(profileData: ProfileUpdateData) {\r\n    const response = await this.api.put('/users/profile', profileData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deactivateAccount(deactivationData: DeactivateAccountData) {\r\n    const response = await this.api.post('/users/deactivate', deactivationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Addressing endpoints\r\n  async getAddresses() {\r\n    const response = await this.api.get('/address/all');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createAddress(addressData: CreateAddressData) {\r\n    const response = await this.api.post('/address/create', addressData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddress(id: string) {\r\n    const response = await this.api.get(`/address/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async editAddress(addressData: EditAddressData) {\r\n    const { address_id, ...updateData } = addressData;\r\n    if (!address_id) {\r\n      throw new Error('Address ID is required for updating');\r\n    }\r\n    const response = await this.api.put(`/address/${address_id}`, updateData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddressesByEntity(entityType: string, entityId: string) {\r\n    const response = await this.api.get(`/address/all?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deleteAddress(id: string) {\r\n    const response = await this.api.delete(`/address/soft/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes) {\r\n    const response = await this.api.post('/postal-codes/search', searchParams);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License endpoints\r\n  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/licenses', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicense(id: string) {\r\n    const response = await this.api.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createLicenseApplication(applicationData: LicenseApplicationData) {\r\n    const response = await this.api.post('/license-applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Types endpoints\r\n  async getLicenseTypes(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-types', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseType(id: string) {\r\n    const response = await this.api.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Categories endpoints\r\n  async getLicenseCategories(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-categories', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoriesByType(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoryTree(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategory(id: string) {\r\n    const response = await this.api.get(`/license-categories/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Application endpoints\r\n  async getApplications(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/applications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplication(id: string) {\r\n    const response = await this.api.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createApplication(applicationData: any) {\r\n    const response = await this.api.post('/applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {\r\n    const response = await this.api.put(`/applications/${id}`, applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Payment endpoints\r\n  async getPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/payments', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPayment(id: string) {\r\n    const response = await this.api.get(`/payments/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createPayment(paymentData: PaymentCreateData) {\r\n    const response = await this.api.post('/payments', paymentData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadProofOfPayment(paymentId: string, formData: FormData) {\r\n    const response = await this.api.post(`/payments/${paymentId}/proof-of-payment`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPaymentStatistics() {\r\n    const response = await this.api.get('/payments/statistics');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Document endpoints\r\n  async getDocuments(params?: { type?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/documents', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadDocument(id: string) {\r\n    const response = await this.api.get(`/documents/${id}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Dashboard statistics\r\n  async getDashboardStats() {\r\n    const response = await this.api.get('/dashboard/stats');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Notifications\r\n  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/notifications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async markNotificationAsRead(id: string) {\r\n    const response = await this.api.patch(`/notifications/${id}/read`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Procurement endpoints\r\n  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/tenders', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getTender(id: string) {\r\n    const response = await this.api.get(`/procurement/tenders/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {\r\n    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadTenderDocument(documentId: string) {\r\n    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/my-bids', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getBid(id: string) {\r\n    const response = await this.api.get(`/procurement/bids/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitBid(formData: FormData) {\r\n    const response = await this.api.post('/procurement/bids', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateBid(id: string, formData: FormData) {\r\n    const response = await this.api.put(`/procurement/bids/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/payments', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayment(id: string) {\r\n    const response = await this.api.get(`/procurement/payments/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  // Consumer Affairs endpoints\r\n  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/consumer-affairs/complaints', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getComplaint(id: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitComplaint(complaintData: ComplaintData) {\r\n    const formData = new FormData();\r\n    formData.append('title', complaintData.title);\r\n    formData.append('description', complaintData.description);\r\n    formData.append('category', complaintData.category);\r\n\r\n    if (complaintData.attachments) {\r\n      complaintData.attachments.forEach((file, index) => {\r\n        formData.append(`attachments[${index}]`, file);\r\n      });\r\n    }\r\n\r\n    const response = await this.api.post('/consumer-affairs/complaints', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateComplaint(id: string, updates: Partial<ComplaintData>) {\r\n    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const customerApi = new CustomerApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { customerApiClient };\r\n\r\n// Export types\r\nexport interface ApiResponse<T = unknown> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  errors?: string[] | { [key: string]: string[] } | string;\r\n}\r\n\r\nexport interface PaginatedResponse<T = unknown> {\r\n  data: T[];\r\n  total: number;\r\n  page: number;\r\n  limit: number;\r\n  totalPages: number;\r\n}\r\n\r\nexport interface License {\r\n  id: string;\r\n  licenseNumber: string;\r\n  type: string;\r\n  status: 'active' | 'expired' | 'suspended' | 'pending';\r\n  issueDate: string;\r\n  expirationDate: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Application {\r\n  id: string;\r\n  applicationNumber: string;\r\n  type: string;\r\n  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';\r\n  submittedDate: string;\r\n  lastUpdated: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Payment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'paid' | 'overdue' | 'cancelled';\r\n  dueDate: string;\r\n  paidDate?: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface User {\r\n  user_id: string;\r\n  email: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  status?: string;\r\n  profile_image?: string;\r\n  roles: string[];\r\n  isAdmin: boolean;\r\n  isCustomer?: boolean;\r\n  organizationName?: string;\r\n  createdAt?: string;\r\n  lastLogin?: string;\r\n  address?: string;\r\n  city?: string;\r\n  country?: string;\r\n  two_factor_enabled?: boolean;\r\n}\r\n\r\nexport interface ProfileUpdateData {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n  organizationName?: string;\r\n  profileImage?: string;\r\n}\r\n\r\nexport interface DeactivateAccountData {\r\n  reason: string;\r\n  feedback?: string;\r\n  user_id?: string;\r\n}\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  entity_type?: string;\r\n  entity_id?:string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n\r\nexport interface LicenseApplicationData {\r\n  type: string;\r\n  organizationName: string;\r\n  description?: string;\r\n  contactEmail?: string;\r\n  contactPhone?: string;\r\n  businessAddress?: string;\r\n  businessType?: string;\r\n  requestedStartDate?: string;\r\n  additionalDocuments?: string[];\r\n  notes?: string;\r\n}\r\n\r\nexport interface PaymentCreateData {\r\n  amount: number;\r\n  currency: string;\r\n  dueDate: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface TenderPaymentData {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface ComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  attachments?: File[];\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,oBAAoB;AACpB,MAAM,eAAe,6DAAmC;AAExD,mEAAmE;AACnE,MAAM,oBAAmC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,qDAAqD;AACrD,MAAM,wBAAuC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxD,SAAS,GAAG,aAAa,KAAK,CAAC;IAC/B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,sBAAsB,YAAY,CAAC,OAAO,CAAC,GAAG,CAC5C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,8BAA8B;YACxC,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;YACrC,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO;YACvB,MAAM,OAAO,IAAI;QACnB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,oCAAoC;IAClD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sBAAsB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uCAAuC;YACjD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;QAC1B;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,wCAAwC;YACpD,SAAS,OAAO,WAAW;YAC3B,MAAM,OAAO,QAAQ;YACrB,QAAQ,OAAO,UAAU,UAAU;YACnC,YAAY,OAAO,UAAU,cAAc;YAC3C,KAAK,OAAO,QAAQ,OAAO;YAC3B,QAAQ,OAAO,QAAQ,UAAU;YACjC,SAAS,OAAO,QAAQ,WAAW;YACnC,cAAc,OAAO,gBAAgB;YACrC,cAAc,OAAO,UAAU,QAAQ;YACvC,aAAa,OAAO,QAAQ,QAAQ;YACpC,SAAS,OAAO,QAAQ,WAAW;QACrC;IACF;IAEA,wDAAwD;IACxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wCAAwC;AACxC,kBAAkB,YAAY,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2DAA2D;AAC3D,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAKpC,2BAA2B;IAC3B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,gBAAgB,MAAM,GAAG;YAEzB,0DAA0D;YAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;YACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;YAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;YAEnE,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,IAAI,GAAG;gBACpC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;gBAE/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB;YAC3B;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yCAAyC;QACzC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAClB,kBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,+BAA+B;IAC/B,MAAc,mBAAsB,GAAW,EAAE,SAA2B,EAAc;QACxF,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;IACT;IAEA,iBAAiB;IACjB,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAC1D;IAEA,MAAM,SAAS;QACb,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAE;QAC1D,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,IAAuD,EAAE;QACvE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,eAAe;QAEjE,oDAAoD;QACpD,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;YACtC,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAElD,sDAAsD;YACtD,MAAM,iBAAiB;gBACrB,cAAc,SAAS,YAAY;gBACnC,MAAM;oBACJ,IAAI,SAAS,IAAI,CAAC,OAAO;oBACzB,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,SAAS,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC;oBAC9C,cAAc,SAAS,IAAI,CAAC,aAAa;oBACzC,WAAW,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,kBAAkB,SAAS,IAAI,CAAC,iBAAiB;oBACjD,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,IAA+C,EAAE;QACxE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,cAAc;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;IACF;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,gBAAuC,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,YAAY,WAA4B,EAAE;QAC9C,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;QACtC,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,UAAkB,EAAE,QAAgB,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,mBAAmB,YAAY,WAAW,EAAE,mBAAmB,WAAW;QAC1I,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,YAA6B,EAAE;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,yBAAyB,eAAuC,EAAE;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAA0C,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,MAA0C,EAAE;QACrE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACpE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,2BAA2B,aAAqB,EAAE;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC1F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;QAC5F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,EAAU,EAAE;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAA2D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;YAAE;QAAO;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,eAAoB,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,EAAU,EAAE,eAAgD,EAAE;QACpF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,SAAiB,EAAE,QAAkB,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,iBAAiB,CAAC,EAAE,UAAU;YACxF,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAyD,EAAE;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc;YAAE;QAAO;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,iBAAiB,EAAU,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE;YAC/D,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,MAA0D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,EAAU,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,WAAW,MAA8E,EAAE;QAC/F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,EAAU,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,mBAAmB,QAAgB,EAAE,WAA8B,EAAE;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,WAAW,CAAC,EAAE;QACpF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,UAAkB,EAAE;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,SAAS,CAAC,EAAE;YACnF,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,MAA2D,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,OAAO,EAAU,EAAE;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,QAAkB,EAAE;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,EAAU,EAAE,QAAkB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE,UAAU;YACvE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,MAA2D,EAAE;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,6BAA6B;IAC7B,MAAM,cAAc,MAA8E,EAAE;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC;YAAE;QAAO;QAC7E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,aAA4B,EAAE;QAClD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS,cAAc,KAAK;QAC5C,SAAS,MAAM,CAAC,eAAe,cAAc,WAAW;QACxD,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;QAElD,IAAI,cAAc,WAAW,EAAE;YAC7B,cAAc,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBACvC,SAAS,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;YAC3C;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,EAAU,EAAE,OAA+B,EAAE;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,4BAA4B,WAAmB,EAAE,YAAoB,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,aAAa,SAAS,CAAC,EAAE;YACtH,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 2436, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/performance.ts"], "sourcesContent": ["// Performance monitoring utilities for customer portal\r\n\r\ninterface PerformanceMetric {\r\n  name: string;\r\n  startTime: number;\r\n  endTime?: number;\r\n  duration?: number;\r\n}\r\n\r\nclass PerformanceMonitor {\r\n  private metrics: Map<string, PerformanceMetric> = new Map();\r\n\r\n  // Start timing a metric\r\n  start(name: string): void {\r\n    this.metrics.set(name, {\r\n      name,\r\n      startTime: performance.now()\r\n    });\r\n  }\r\n\r\n  // End timing a metric\r\n  end(name: string): number | null {\r\n    const metric = this.metrics.get(name);\r\n    if (!metric) {\r\n      console.warn(`Performance metric \"${name}\" not found`);\r\n      return null;\r\n    }\r\n\r\n    const endTime = performance.now();\r\n    const duration = endTime - metric.startTime;\r\n\r\n    this.metrics.set(name, {\r\n      ...metric,\r\n      endTime,\r\n      duration\r\n    });\r\n\r\n    // Log in development\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`⚡ ${name}: ${duration.toFixed(2)}ms`);\r\n    }\r\n\r\n    return duration;\r\n  }\r\n\r\n  // Get metric duration\r\n  getDuration(name: string): number | null {\r\n    const metric = this.metrics.get(name);\r\n    return metric?.duration || null;\r\n  }\r\n\r\n  // Get all metrics\r\n  getAllMetrics(): PerformanceMetric[] {\r\n    return Array.from(this.metrics.values()).filter(m => m.duration !== undefined);\r\n  }\r\n\r\n  // Clear all metrics\r\n  clear(): void {\r\n    this.metrics.clear();\r\n  }\r\n\r\n  // Log performance summary\r\n  logSummary(): void {\r\n    const metrics = this.getAllMetrics();\r\n    if (metrics.length === 0) return;\r\n\r\n    console.group('🚀 Performance Summary');\r\n    metrics.forEach(metric => {\r\n      console.log(`${metric.name}: ${metric.duration?.toFixed(2)}ms`);\r\n    });\r\n    console.groupEnd();\r\n  }\r\n}\r\n\r\n// Global performance monitor instance\r\nexport const performanceMonitor = new PerformanceMonitor();\r\n\r\n// Utility functions for common performance measurements\r\nexport const measurePageLoad = (pageName: string) => {\r\n  performanceMonitor.start(`page-load-${pageName}`);\r\n  \r\n  return () => {\r\n    performanceMonitor.end(`page-load-${pageName}`);\r\n  };\r\n};\r\n\r\nexport const measureApiCall = (apiName: string) => {\r\n  performanceMonitor.start(`api-${apiName}`);\r\n  \r\n  return () => {\r\n    performanceMonitor.end(`api-${apiName}`);\r\n  };\r\n};\r\n\r\nexport const measureComponentRender = (componentName: string) => {\r\n  performanceMonitor.start(`render-${componentName}`);\r\n  \r\n  return () => {\r\n    performanceMonitor.end(`render-${componentName}`);\r\n  };\r\n};\r\n\r\n// React hook for measuring component performance\r\nexport const usePerformanceMetric = (name: string) => {\r\n  const start = () => performanceMonitor.start(name);\r\n  const end = () => performanceMonitor.end(name);\r\n  const getDuration = () => performanceMonitor.getDuration(name);\r\n\r\n  return { start, end, getDuration };\r\n};\r\n\r\n// Web Vitals monitoring (if needed)\r\nexport const measureWebVitals = () => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  // Measure First Contentful Paint\r\n  const observer = new PerformanceObserver((list) => {\r\n    for (const entry of list.getEntries()) {\r\n      if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {\r\n        console.log(`🎨 First Contentful Paint: ${entry.startTime.toFixed(2)}ms`);\r\n      }\r\n    }\r\n  });\r\n\r\n  try {\r\n    observer.observe({ entryTypes: ['paint'] });\r\n  } catch {\r\n    // Browser doesn't support this API\r\n  }\r\n};\r\n\r\nexport default performanceMonitor;"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;;;;;AASvD,MAAM;IACI,UAA0C,IAAI,MAAM;IAE5D,wBAAwB;IACxB,MAAM,IAAY,EAAQ;QACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;YACrB;YACA,WAAW,YAAY,GAAG;QAC5B;IACF;IAEA,sBAAsB;IACtB,IAAI,IAAY,EAAiB;QAC/B,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,QAAQ;YACX,QAAQ,IAAI,CAAC,CAAC,oBAAoB,EAAE,KAAK,WAAW,CAAC;YACrD,OAAO;QACT;QAEA,MAAM,UAAU,YAAY,GAAG;QAC/B,MAAM,WAAW,UAAU,OAAO,SAAS;QAE3C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;YACrB,GAAG,MAAM;YACT;YACA;QACF;QAEA,qBAAqB;QACrB,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE,CAAC;QACnD;QAEA,OAAO;IACT;IAEA,sBAAsB;IACtB,YAAY,IAAY,EAAiB;QACvC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,OAAO,QAAQ,YAAY;IAC7B;IAEA,kBAAkB;IAClB,gBAAqC;QACnC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IACtE;IAEA,oBAAoB;IACpB,QAAc;QACZ,IAAI,CAAC,OAAO,CAAC,KAAK;IACpB;IAEA,0BAA0B;IAC1B,aAAmB;QACjB,MAAM,UAAU,IAAI,CAAC,aAAa;QAClC,IAAI,QAAQ,MAAM,KAAK,GAAG;QAE1B,QAAQ,KAAK,CAAC;QACd,QAAQ,OAAO,CAAC,CAAA;YACd,QAAQ,GAAG,CAAC,GAAG,OAAO,IAAI,CAAC,EAAE,EAAE,OAAO,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC;QAChE;QACA,QAAQ,QAAQ;IAClB;AACF;AAGO,MAAM,qBAAqB,IAAI;AAG/B,MAAM,kBAAkB,CAAC;IAC9B,mBAAmB,KAAK,CAAC,CAAC,UAAU,EAAE,UAAU;IAEhD,OAAO;QACL,mBAAmB,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU;IAChD;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,mBAAmB,KAAK,CAAC,CAAC,IAAI,EAAE,SAAS;IAEzC,OAAO;QACL,mBAAmB,GAAG,CAAC,CAAC,IAAI,EAAE,SAAS;IACzC;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,mBAAmB,KAAK,CAAC,CAAC,OAAO,EAAE,eAAe;IAElD,OAAO;QACL,mBAAmB,GAAG,CAAC,CAAC,OAAO,EAAE,eAAe;IAClD;AACF;AAGO,MAAM,uBAAuB,CAAC;IACnC,MAAM,QAAQ,IAAM,mBAAmB,KAAK,CAAC;IAC7C,MAAM,MAAM,IAAM,mBAAmB,GAAG,CAAC;IACzC,MAAM,cAAc,IAAM,mBAAmB,WAAW,CAAC;IAEzD,OAAO;QAAE;QAAO;QAAK;IAAY;AACnC;AAGO,MAAM,mBAAmB;IAC9B,wCAAmC;;IAEnC,iCAAiC;IACjC,MAAM;AAaR;uCAEe", "debugId": null}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport LicenseCard from '@/components/customer/LicenseCard';\r\nimport PaymentCard from '@/components/customer/PaymentCard';\r\nimport Loader from '@/components/Loader';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { customerApi, License, Application, Payment } from '@/lib/customer-api';\r\nimport { measurePageLoad, measureApiCall } from '@/utils/performance';\r\n\r\nconst CustomerDashboard = () => {\r\n  const { user, isAuthenticated } = useAuth();\r\n  const router = useRouter();\r\n\r\n  // Performance monitoring\r\n  useEffect(() => {\r\n    const endPageLoad = measurePageLoad('customer-dashboard');\r\n    return endPageLoad;\r\n  }, []);\r\n\r\n  const [dashboardData, setDashboardData] = useState({\r\n    licenses: [] as License[],\r\n    applications: [] as Application[],\r\n    payments: [] as Payment[],\r\n    stats: {\r\n      activeLicenses: 0,\r\n      pendingApplications: 0,\r\n      expiringSoon: 0,\r\n      paymentsDue: 0,\r\n      totalPaymentAmount: 0\r\n    }\r\n  });\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n\r\n  // Fetch dashboard data with optimized parallel requests\r\n  useEffect(() => {\r\n    const fetchDashboardData = async () => {\r\n      if (!isAuthenticated) return;\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        setError('');\r\n\r\n        // Measure API performance\r\n        const endApiCall = measureApiCall('dashboard-data');\r\n\r\n        // Use Promise.all for better performance\r\n        const [licensesRes, applicationsRes, paymentsRes, statsRes] = await Promise.all([\r\n          customerApi.getLicenses({ limit: 10 }).catch(() => ({ data: [] })),\r\n          customerApi.getApplications({ limit: 10 }).catch(() => ({ data: [] })),\r\n          customerApi.getPayments({ limit: 10 }).catch(() => ({ data: [] })),\r\n          customerApi.getDashboardStats().catch(() => ({}))\r\n        ]);\r\n\r\n        endApiCall();\r\n\r\n        // Process data efficiently\r\n        const licenses = licensesRes.data || licensesRes || [];\r\n        const applications = applicationsRes.data || applicationsRes || [];\r\n        const payments = paymentsRes.data || paymentsRes || [];\r\n\r\n        // Process stats or calculate from data\r\n        let stats;\r\n        if (statsRes && Object.keys(statsRes).length > 0) {\r\n          stats = statsRes.data || statsRes;\r\n        } else {\r\n          // Calculate stats from fetched data\r\n          const activeLicenses = licenses.filter((l: License) => l.status === 'active').length;\r\n          const pendingApplications = applications.filter((a: Application) =>\r\n            ['submitted', 'under_review'].includes(a.status)\r\n          ).length;\r\n\r\n          // Check for licenses expiring in next 30 days\r\n          const thirtyDaysFromNow = new Date();\r\n          thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\r\n          const expiringSoon = licenses.filter((l: License) => {\r\n            const expirationDate = new Date(l.expirationDate);\r\n            return l.status === 'active' && expirationDate <= thirtyDaysFromNow;\r\n          }).length;\r\n\r\n          const pendingPayments = payments.filter((p: Payment) =>\r\n            ['pending', 'overdue'].includes(p.status)\r\n          );\r\n          const totalPaymentAmount = pendingPayments.reduce((sum: number, p: Payment) => sum + p.amount, 0);\r\n\r\n          stats = {\r\n            activeLicenses,\r\n            pendingApplications,\r\n            expiringSoon,\r\n            paymentsDue: pendingPayments.length,\r\n            totalPaymentAmount\r\n          };\r\n        }\r\n\r\n        setDashboardData({\r\n          licenses,\r\n          applications,\r\n          payments,\r\n          stats\r\n        });\r\n\r\n      } catch (err: unknown) {\r\n        console.error('Error fetching dashboard data:', err);\r\n        setError('Failed to load dashboard data. Please try refreshing the page.');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDashboardData();\r\n  }, [isAuthenticated]);\r\n\r\n  // Memoize expensive calculations\r\n  const quickStats = useMemo(() => {\r\n    const { licenses, applications, payments } = dashboardData;\r\n    \r\n    return {\r\n      totalLicenses: licenses.length,\r\n      activeLicenses: licenses.filter(l => l.status === 'active').length,\r\n      pendingApplications: applications.filter(a => ['submitted', 'under_review'].includes(a.status)).length,\r\n      overduePayments: payments.filter(p => p.status === 'overdue').length\r\n    };\r\n  }, [dashboardData]);\r\n\r\n  // Memoize filtered data for display\r\n  const displayData = useMemo(() => {\r\n    const { licenses, applications, payments } = dashboardData;\r\n    \r\n    return {\r\n      recentLicenses: licenses.slice(0, 3),\r\n      recentApplications: applications.slice(0, 3),\r\n      urgentPayments: payments.filter(p => ['pending', 'overdue'].includes(p.status)).slice(0, 3)\r\n    };\r\n  }, [dashboardData]);\r\n\r\n  // Show loading state\r\n  if ( isLoading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <Loader message=\"Loading your dashboard...\" />\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\">\r\n          <p>{error}</p>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => window.location.reload()}\r\n            className=\"mt-2 text-sm underline hover:no-underline\"\r\n          >\r\n            Try again\r\n          </button>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Prepare status data with real values\r\n  const statusData = [\r\n    {\r\n      title: 'Active Licenses',\r\n      value: dashboardData.stats.activeLicenses,\r\n      icon: (\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-white',\r\n      iconBgColor: 'bg-green-100',\r\n      iconTextColor: 'text-green-600',\r\n      linkText: 'View all',\r\n      linkHref: '/customer/licenses'\r\n    },\r\n    {\r\n      title: 'Pending Applications',\r\n      value: dashboardData.stats.pendingApplications,\r\n      icon: (\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-white',\r\n      iconBgColor: 'bg-yellow-100',\r\n      iconTextColor: 'text-yellow-600',\r\n      linkText: 'View all',\r\n      linkHref: '/customer/applications'\r\n    },\r\n    {\r\n      title: 'Expiring Soon',\r\n      value: dashboardData.stats.expiringSoon,\r\n      icon: (\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-white',\r\n      iconBgColor: 'bg-orange-100',\r\n      iconTextColor: 'text-orange-600',\r\n      linkText: 'View all',\r\n      linkHref: '/customer/licenses?filter=expiring'\r\n    },\r\n    {\r\n      title: 'Payments Due',\r\n      value: dashboardData.stats.totalPaymentAmount > 0\r\n        ? `MK${dashboardData.stats.totalPaymentAmount.toLocaleString()}`\r\n        : 'MK0',\r\n      icon: (\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-white',\r\n      iconBgColor: 'bg-red-100',\r\n      iconTextColor: 'text-red-600',\r\n      linkText: 'View all',\r\n      linkHref: '/customer/payments'\r\n    }\r\n  ];\r\n\r\n  // Transform license data for display (using memoized data)\r\n  const recentLicenses = displayData.recentLicenses.slice(0, 2).map((license: License) => {\r\n    // Determine display status\r\n    let displayStatus: 'Active' | 'Expiring Soon' | 'Expired' | 'Pending';\r\n    if (license.status === 'active') {\r\n      // Check if expiring soon (within 30 days)\r\n      const expirationDate = new Date(license.expirationDate);\r\n      const thirtyDaysFromNow = new Date();\r\n      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\r\n      displayStatus = expirationDate <= thirtyDaysFromNow ? 'Expiring Soon' : 'Active';\r\n    } else if (license.status === 'expired') {\r\n      displayStatus = 'Expired';\r\n    } else {\r\n      displayStatus = 'Pending';\r\n    }\r\n\r\n    return {\r\n      id: license.id,\r\n      title: license.type || 'License',\r\n      licenseNumber: license.licenseNumber,\r\n      status: displayStatus,\r\n      issueDate: new Date(license.issueDate).toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'short',\r\n        day: 'numeric'\r\n      }),\r\n      expirationDate: new Date(license.expirationDate).toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'short',\r\n        day: 'numeric'\r\n      })\r\n    };\r\n  });\r\n\r\n  // Transform payment data for display (using memoized data)\r\n  const upcomingPayments = displayData.urgentPayments\r\n    .map((payment: Payment) => {\r\n      const dueDate = new Date(payment.dueDate);\r\n      const today = new Date();\r\n      const diffTime = dueDate.getTime() - today.getTime();\r\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n\r\n      let dueDateText;\r\n      if (diffDays < 0) {\r\n        dueDateText = `Overdue by ${Math.abs(diffDays)} days`;\r\n      } else if (diffDays === 0) {\r\n        dueDateText = 'Due today';\r\n      } else if (diffDays === 1) {\r\n        dueDateText = 'Due tomorrow';\r\n      } else {\r\n        dueDateText = `Due in ${diffDays} days`;\r\n      }\r\n\r\n      return {\r\n        id: payment.id,\r\n        title: payment.description || `Payment for ${payment.relatedLicense || payment.relatedApplication || 'Service'}`,\r\n        amount: `MK${payment.amount.toLocaleString()}`,\r\n        dueDate: dueDateText,\r\n        status: payment.status === 'overdue' ? 'Overdue' as const : 'Due' as const,\r\n        description: dueDateText\r\n      };\r\n    });\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page header */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                Welcome, {user?.first_name || 'Customer'}!\r\n              </h1>\r\n              <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n                Manage your licenses and applications from your personal dashboard.\r\n              </p>\r\n            </div>\r\n            <div className=\"flex space-x-3\">\r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap\"\r\n                >\r\n                  <div className=\"w-4 h-4 flex items-center justify-center mr-2\">\r\n                    <i className=\"ri-calendar-line\"></i>\r\n                  </div>\r\n                  {new Date().toLocaleDateString('en-US', { \r\n                    month: 'short', \r\n                    day: 'numeric', \r\n                    year: 'numeric' \r\n                  })}\r\n                </button>\r\n              </div>\r\n              <Link\r\n                href=\"/customer/applications\"\r\n                className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\"\r\n              >\r\n                <div className=\"w-4 h-4 flex items-center justify-center mr-2\">\r\n                  <i className=\"ri-add-line\"></i>\r\n                </div>\r\n                New Application\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Key Metrics Section */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden\">\r\n          <div className=\"p-6\">\r\n            <h3 className=\"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4\">Key Metrics</h3>\r\n            <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\r\n              {statusData.map((item, index) => (\r\n                <div key={index} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className={`flex-shrink-0 ${item.iconBgColor} ${item.iconBgColor.includes('green') ? 'dark:bg-green-900' : item.iconBgColor.includes('yellow') ? 'dark:bg-yellow-900' : item.iconBgColor.includes('orange') ? 'dark:bg-orange-900' : item.iconBgColor.includes('red') ? 'dark:bg-red-900' : 'dark:bg-gray-600'} rounded-md p-3`}>\r\n                      <div className={`w-6 h-6 flex items-center justify-center ${item.iconTextColor} ${item.iconTextColor.includes('green') ? 'dark:text-green-400' : item.iconTextColor.includes('yellow') ? 'dark:text-yellow-400' : item.iconTextColor.includes('orange') ? 'dark:text-orange-400' : item.iconTextColor.includes('red') ? 'dark:text-red-400' : 'dark:text-gray-400'}`}>\r\n                        {item.icon}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">{item.title}</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{item.value}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link \r\n                      href={item.linkHref} \r\n                      className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']\"\r\n                    >\r\n                      {item.linkText}\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n    \r\n\r\n        {/* Main dashboard content */}\r\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\r\n          {/* Left Column */}\r\n          <div className=\"lg:col-span-2 space-y-6\">\r\n            {/* My Licenses */}\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\r\n              <div className=\"p-6\">\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">My Licenses</h3>\r\n                  <Link href=\"/customer/licenses\" className=\"text-sm text-primary hover:text-primary\">\r\n                    View all →\r\n                  </Link>\r\n                </div>\r\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\r\n                  {recentLicenses.map((license) => (\r\n                    <LicenseCard key={license.id} {...license} />\r\n                  ))}\r\n                </div>\r\n                {recentLicenses.length === 0 && (\r\n                  <div className=\"text-center py-8\">\r\n                    <div className=\"w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4\">\r\n                      <i className=\"ri-key-line text-2xl text-gray-400 dark:text-gray-500\"></i>\r\n                    </div>\r\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">No licenses yet</h4>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                      Start by submitting your first license application using the &ldquo;New Application&rdquo; button above.\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column */}\r\n          <div className=\"space-y-6\">\r\n            {/* Application Process */}\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n              <div className=\"p-6\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">License Application Process</h3>\r\n                <div className=\"space-y-4\">\r\n                  {[\r\n                    {\r\n                      step: 1,\r\n                      title: 'Submit Application',\r\n                      description: 'Fill out the application form with your details and submit required documents.',\r\n                      icon: 'ri-file-edit-line',\r\n                      bgColor: 'bg-blue-100 dark:bg-blue-900',\r\n                      textColor: 'text-blue-600 dark:text-blue-400'\r\n                    },\r\n                    {\r\n                      step: 2,\r\n                      title: 'Application Review',\r\n                      description: 'Our team reviews your application and may request additional information if needed.',\r\n                      icon: 'ri-search-eye-line',\r\n                      bgColor: 'bg-yellow-100 dark:bg-yellow-900',\r\n                      textColor: 'text-yellow-600 dark:text-yellow-400'\r\n                    },\r\n                    {\r\n                      step: 3,\r\n                      title: 'Payment',\r\n                      description: 'Once approved, you\\'ll receive an invoice for the license fee that must be paid.',\r\n                      icon: 'ri-bank-card-line',\r\n                      bgColor: 'bg-green-100 dark:bg-green-900',\r\n                      textColor: 'text-green-600 dark:text-green-400'\r\n                    },\r\n                    {\r\n                      step: 4,\r\n                      title: 'License Issuance',\r\n                      description: 'After payment confirmation, your license will be issued and available for download.',\r\n                      icon: 'ri-award-line',\r\n                      bgColor: 'bg-purple-100 dark:bg-purple-900',\r\n                      textColor: 'text-purple-600 dark:text-purple-400'\r\n                    }\r\n                  ].map((item) => (\r\n                    <div key={item.step} className=\"flex items-start\">\r\n                      <div className=\"flex-shrink-0\">\r\n                        <div className={`flex items-center justify-center h-10 w-10 rounded-full ${item.bgColor} ${item.textColor}`}>\r\n                          <i className={`${item.icon} text-lg`}></i>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"ml-4\">\r\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{item.title}</h4>\r\n                        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">{item.description}</p>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Upcoming Payments */}\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n              <div className=\"p-6\">\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Upcoming Payments</h3>\r\n                  <Link href=\"/customer/payments\" className=\"text-sm text-primary hover:text-primary\">\r\n                    View all →\r\n                  </Link>\r\n                </div>\r\n                <div className=\"space-y-4\">\r\n                  {upcomingPayments.map((payment) => (\r\n                    <PaymentCard key={payment.id} {...payment} />\r\n                  ))}\r\n                  {upcomingPayments.length === 0 && (\r\n                    <div className=\"text-center py-6\">\r\n                      <div className=\"w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3\">\r\n                        <i className=\"ri-money-dollar-circle-line text-xl text-gray-400 dark:text-gray-500\"></i>\r\n                      </div>\r\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400\">No pending payments</p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default CustomerDashboard;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAaA,MAAM,oBAAoB;IACxB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE;QACpC,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,UAAU,EAAE;QACZ,cAAc,EAAE;QAChB,UAAU,EAAE;QACZ,OAAO;YACL,gBAAgB;YAChB,qBAAqB;YACrB,cAAc;YACd,aAAa;YACb,oBAAoB;QACtB;IACF;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,IAAI,CAAC,iBAAiB;YAEtB,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,0BAA0B;gBAC1B,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;gBAElC,yCAAyC;gBACzC,MAAM,CAAC,aAAa,iBAAiB,aAAa,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC9E,6HAAA,CAAA,cAAW,CAAC,WAAW,CAAC;wBAAE,OAAO;oBAAG,GAAG,KAAK,CAAC,IAAM,CAAC;4BAAE,MAAM,EAAE;wBAAC,CAAC;oBAChE,6HAAA,CAAA,cAAW,CAAC,eAAe,CAAC;wBAAE,OAAO;oBAAG,GAAG,KAAK,CAAC,IAAM,CAAC;4BAAE,MAAM,EAAE;wBAAC,CAAC;oBACpE,6HAAA,CAAA,cAAW,CAAC,WAAW,CAAC;wBAAE,OAAO;oBAAG,GAAG,KAAK,CAAC,IAAM,CAAC;4BAAE,MAAM,EAAE;wBAAC,CAAC;oBAChE,6HAAA,CAAA,cAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;iBAChD;gBAED;gBAEA,2BAA2B;gBAC3B,MAAM,WAAW,YAAY,IAAI,IAAI,eAAe,EAAE;gBACtD,MAAM,eAAe,gBAAgB,IAAI,IAAI,mBAAmB,EAAE;gBAClE,MAAM,WAAW,YAAY,IAAI,IAAI,eAAe,EAAE;gBAEtD,uCAAuC;gBACvC,IAAI;gBACJ,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;oBAChD,QAAQ,SAAS,IAAI,IAAI;gBAC3B,OAAO;oBACL,oCAAoC;oBACpC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,IAAe,EAAE,MAAM,KAAK,UAAU,MAAM;oBACpF,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAC,IAC/C;4BAAC;4BAAa;yBAAe,CAAC,QAAQ,CAAC,EAAE,MAAM,GAC/C,MAAM;oBAER,8CAA8C;oBAC9C,MAAM,oBAAoB,IAAI;oBAC9B,kBAAkB,OAAO,CAAC,kBAAkB,OAAO,KAAK;oBACxD,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC;wBACpC,MAAM,iBAAiB,IAAI,KAAK,EAAE,cAAc;wBAChD,OAAO,EAAE,MAAM,KAAK,YAAY,kBAAkB;oBACpD,GAAG,MAAM;oBAET,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,IACvC;4BAAC;4BAAW;yBAAU,CAAC,QAAQ,CAAC,EAAE,MAAM;oBAE1C,MAAM,qBAAqB,gBAAgB,MAAM,CAAC,CAAC,KAAa,IAAe,MAAM,EAAE,MAAM,EAAE;oBAE/F,QAAQ;wBACN;wBACA;wBACA;wBACA,aAAa,gBAAgB,MAAM;wBACnC;oBACF;gBACF;gBAEA,iBAAiB;oBACf;oBACA;oBACA;oBACA;gBACF;YAEF,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAgB;IAEpB,iCAAiC;IACjC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG;QAE7C,OAAO;YACL,eAAe,SAAS,MAAM;YAC9B,gBAAgB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;YAClE,qBAAqB,aAAa,MAAM,CAAC,CAAA,IAAK;oBAAC;oBAAa;iBAAe,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;YACtG,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QACtE;IACF,GAAG;QAAC;KAAc;IAElB,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG;QAE7C,OAAO;YACL,gBAAgB,SAAS,KAAK,CAAC,GAAG;YAClC,oBAAoB,aAAa,KAAK,CAAC,GAAG;YAC1C,gBAAgB,SAAS,MAAM,CAAC,CAAA,IAAK;oBAAC;oBAAW;iBAAU,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,GAAG;QAC3F;IACF,GAAG;QAAC;KAAc;IAElB,qBAAqB;IACrB,IAAK,WAAW;QACd,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;oBAAC,SAAQ;;;;;;;;;;;;;;;;IAIxB;IAEA,mBAAmB;IACnB,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,uCAAuC;IACvC,MAAM,aAAa;QACjB;YACE,OAAO;YACP,OAAO,cAAc,KAAK,CAAC,cAAc;YACzC,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC7E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,aAAa;YACb,eAAe;YACf,UAAU;YACV,UAAU;QACZ;QACA;YACE,OAAO;YACP,OAAO,cAAc,KAAK,CAAC,mBAAmB;YAC9C,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC7E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,aAAa;YACb,eAAe;YACf,UAAU;YACV,UAAU;QACZ;QACA;YACE,OAAO;YACP,OAAO,cAAc,KAAK,CAAC,YAAY;YACvC,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC7E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,aAAa;YACb,eAAe;YACf,UAAU;YACV,UAAU;QACZ;QACA;YACE,OAAO;YACP,OAAO,cAAc,KAAK,CAAC,kBAAkB,GAAG,IAC5C,CAAC,EAAE,EAAE,cAAc,KAAK,CAAC,kBAAkB,CAAC,cAAc,IAAI,GAC9D;YACJ,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC7E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,aAAa;YACb,eAAe;YACf,UAAU;YACV,UAAU;QACZ;KACD;IAED,2DAA2D;IAC3D,MAAM,iBAAiB,YAAY,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QACjE,2BAA2B;QAC3B,IAAI;QACJ,IAAI,QAAQ,MAAM,KAAK,UAAU;YAC/B,0CAA0C;YAC1C,MAAM,iBAAiB,IAAI,KAAK,QAAQ,cAAc;YACtD,MAAM,oBAAoB,IAAI;YAC9B,kBAAkB,OAAO,CAAC,kBAAkB,OAAO,KAAK;YACxD,gBAAgB,kBAAkB,oBAAoB,kBAAkB;QAC1E,OAAO,IAAI,QAAQ,MAAM,KAAK,WAAW;YACvC,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;QAEA,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,IAAI,IAAI;YACvB,eAAe,QAAQ,aAAa;YACpC,QAAQ;YACR,WAAW,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;gBACjE,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;YACA,gBAAgB,IAAI,KAAK,QAAQ,cAAc,EAAE,kBAAkB,CAAC,SAAS;gBAC3E,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;QACF;IACF;IAEA,2DAA2D;IAC3D,MAAM,mBAAmB,YAAY,cAAc,CAChD,GAAG,CAAC,CAAC;QACJ,MAAM,UAAU,IAAI,KAAK,QAAQ,OAAO;QACxC,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,QAAQ,OAAO,KAAK,MAAM,OAAO;QAClD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI;QACJ,IAAI,WAAW,GAAG;YAChB,cAAc,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,UAAU,KAAK,CAAC;QACvD,OAAO,IAAI,aAAa,GAAG;YACzB,cAAc;QAChB,OAAO,IAAI,aAAa,GAAG;YACzB,cAAc;QAChB,OAAO;YACL,cAAc,CAAC,OAAO,EAAE,SAAS,KAAK,CAAC;QACzC;QAEA,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,WAAW,IAAI,CAAC,YAAY,EAAE,QAAQ,cAAc,IAAI,QAAQ,kBAAkB,IAAI,WAAW;YAChH,QAAQ,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,cAAc,IAAI;YAC9C,SAAS;YACT,QAAQ,QAAQ,MAAM,KAAK,YAAY,YAAqB;YAC5D,aAAa;QACf;IACF;IAEF,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAA0D;4CAC5D,MAAM,cAAc;4CAAW;;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAI/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;gDAEd,IAAI,OAAO,kBAAkB,CAAC,SAAS;oDACtC,OAAO;oDACP,KAAK;oDACL,MAAM;gDACR;;;;;;;;;;;;kDAGJ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;4CACT;;;;;;;;;;;;;;;;;;;;;;;;8BAQd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsE;;;;;;0CACpF,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,cAAc,EAAE,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,QAAQ,CAAC,WAAW,sBAAsB,KAAK,WAAW,CAAC,QAAQ,CAAC,YAAY,uBAAuB,KAAK,WAAW,CAAC,QAAQ,CAAC,YAAY,uBAAuB,KAAK,WAAW,CAAC,QAAQ,CAAC,SAAS,oBAAoB,mBAAmB,eAAe,CAAC;kEAClU,cAAA,8OAAC;4DAAI,WAAW,CAAC,yCAAyC,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,QAAQ,CAAC,WAAW,wBAAwB,KAAK,aAAa,CAAC,QAAQ,CAAC,YAAY,yBAAyB,KAAK,aAAa,CAAC,QAAQ,CAAC,YAAY,yBAAyB,KAAK,aAAa,CAAC,QAAQ,CAAC,SAAS,sBAAsB,sBAAsB;sEACjW,KAAK,IAAI;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAwD,KAAK,KAAK;;;;;;0EAChF,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EAA2D,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0DAI1F,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,QAAQ;oDACnB,WAAU;8DAET,KAAK,QAAQ;;;;;;;;;;;;uCAnBV;;;;;;;;;;;;;;;;;;;;;8BA+BlB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuD;;;;;;8DACrE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;8DAA0C;;;;;;;;;;;;sDAItF,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC,6IAAA,CAAA,UAAW;oDAAmB,GAAG,OAAO;mDAAvB,QAAQ,EAAE;;;;;;;;;;wCAG/B,eAAe,MAAM,KAAK,mBACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAC1E,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUlE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC;gDAAI,WAAU;0DACZ;oDACC;wDACE,MAAM;wDACN,OAAO;wDACP,aAAa;wDACb,MAAM;wDACN,SAAS;wDACT,WAAW;oDACb;oDACA;wDACE,MAAM;wDACN,OAAO;wDACP,aAAa;wDACb,MAAM;wDACN,SAAS;wDACT,WAAW;oDACb;oDACA;wDACE,MAAM;wDACN,OAAO;wDACP,aAAa;wDACb,MAAM;wDACN,SAAS;wDACT,WAAW;oDACb;oDACA;wDACE,MAAM;wDACN,OAAO;wDACP,aAAa;wDACb,MAAM;wDACN,SAAS;wDACT,WAAW;oDACb;iDACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAW,CAAC,wDAAwD,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;8EACzG,cAAA,8OAAC;wEAAE,WAAW,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;0EAGxC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAwD,KAAK,KAAK;;;;;;kFAChF,8OAAC;wEAAE,WAAU;kFAAiD,KAAK,WAAW;;;;;;;;;;;;;uDARxE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;8CAiB3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuD;;;;;;kEACrE,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAqB,WAAU;kEAA0C;;;;;;;;;;;;0DAItF,8OAAC;gDAAI,WAAU;;oDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,6IAAA,CAAA,UAAW;4DAAmB,GAAG,OAAO;2DAAvB,QAAQ,EAAE;;;;;oDAE7B,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;;;;;;;;;;0EAEf,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9E;uCAEe", "debugId": null}}]}