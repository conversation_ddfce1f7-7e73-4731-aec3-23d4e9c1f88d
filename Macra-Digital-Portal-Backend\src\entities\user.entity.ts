import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>reateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  JoinTable,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Role } from './role.entity';
import { UserIdentification } from './user-identification.entity';
import { Employee } from './employee.entity';
import { Organization } from './organization.entity';
import { Department } from './department.entity';

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

@Entity('users')
export class User {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  user_id: string;

  @Column({ unique: true, type: 'varchar', length: 255 })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  password: string;

  @Column({ type: 'varchar', length: 100 })
  first_name: string;

  @Column({ type: 'varchar', length: 100 })
  last_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  middle_name?: string;

  @Column({ type: 'varchar', length: 20 })
  phone: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  department_id?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  organization_id?: string;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({ type: 'text', nullable: true })
  profile_image?: string;

  // Note: role_id field removed - now using many-to-many relationship

  @Column({ type: 'date', nullable: true })
  two_factor_next_verification?: Date;

  @Column({ type: 'varchar', length: 65, nullable: true })
  two_factor_code?: string;

  @Column({ type: 'boolean', default: false })
  two_factor_enabled: boolean;

  @Column({ type: 'varchar', length: 64, nullable: true })
  two_factor_temp?: string;

  @Column({ type: 'timestamp', nullable: true })
  email_verified_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @Column({ type: 'timestamp', nullable: true })
  last_login?: Date;

  // Relations
  @ManyToMany(() => Role, (role) => role.users, { nullable: true })
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'user_id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'role_id' },
  })
  roles?: Role[];

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToOne(() => Organization, { nullable: true })
  @JoinColumn({ name: 'organization_id' })
  organization?: Organization;

  @ManyToOne(() => Department, { nullable: true })
  @JoinColumn({ name: 'department_id' })
  department?: Department;

  @OneToMany(() => UserIdentification, (identification) => identification.user)
  identifications: UserIdentification[];

  @OneToMany(() => Employee, (employee) => employee.user)
  employee_records: Employee[];

  @BeforeInsert()
  generateId() {
    if (!this.user_id) {
      this.user_id = uuidv4();
    }
  }
}
