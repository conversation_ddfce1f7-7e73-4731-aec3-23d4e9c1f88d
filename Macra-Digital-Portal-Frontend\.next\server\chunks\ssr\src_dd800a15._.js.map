{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: {\r\n    applicant_id: string;\r\n    name: string;\r\n    business_registration_number: string;\r\n    tpin: string;\r\n    website: string;\r\n    email: string;\r\n    phone: string;\r\n    fax?: string;\r\n    level_of_insurance_cover?: string;\r\n    date_incorporation: string;\r\n    place_incorporation: string;\r\n    created_at: string;\r\n    updated_at: string;\r\n  };\r\n  license_category?: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type?: {\r\n      license_type_id: string;\r\n      name: string;\r\n      code: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAsFO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\n\r\nexport interface AppNotification {\r\n  notification_id: string;\r\n  user_id: string;\r\n  application_id: string;\r\n  application_number: string;\r\n  license_category_name: string;\r\n  title: string;\r\n  message: string;\r\n  type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection';\r\n  status: 'unread' | 'read';\r\n  priority: 'low' | 'medium' | 'high';\r\n  created_at: string;\r\n  read_at?: string;\r\n  metadata?: {\r\n    old_status?: ApplicationStatus;\r\n    new_status?: ApplicationStatus;\r\n    step?: number;\r\n    progress_percentage?: number;\r\n  };\r\n}\r\n\r\nexport interface NotificationSummary {\r\n  total_count: number;\r\n  unread_count: number;\r\n  notifications: AppNotification[];\r\n}\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications?${queryParams.toString()}`;\r\n      console.log(`[NotificationService] Fetching notifications from: ${endpoint}`);\r\n      \r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response);\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.total_count || 0,\r\n        unread_count: data.unread_count || 0,\r\n        notifications: Array.isArray(data.notifications) ? data.notifications : []\r\n      };\r\n    } catch (error: any) {\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications?${new URLSearchParams(params || {}).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read\r\n  async markAllAsRead(): Promise<void> {\r\n    const response = await apiClient.patch('/notifications/mark-all-read');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<AppNotification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: ApplicationStatus,\r\n  newStatus: ApplicationStatus,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AA6BO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,CAAC,eAAe,EAAE,YAAY,QAAQ,IAAI;YAC3D,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,UAAU;YAE5E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAEhC,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,WAAW,IAAI;gBACjC,cAAc,KAAK,YAAY,IAAI;gBACnC,eAAe,MAAM,OAAO,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,GAAG,EAAE;YAC5E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,MAAM,QAAQ,EAAE,QAAQ;gBAClC,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,CAAC,eAAe,EAAE,IAAI,gBAAgB,UAAU,CAAC,GAAG,QAAQ,IAAI;YAC5E;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,KAAK,CAAC;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iCAAiC;IACjC,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;QACvC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,gBAAgB;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,CAAC,EAAE,EAAE,mBAAmB,WAAW,CAAC,GAAG;IACjF,MAAM,WAAW,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG;IAE5C,MAAM,WAAW;QACf,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6DAA6D,CAAC;YACrI,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,iCAAiC,EAAE,eAAe,SAAS,kCAAkC,CAAC;YACrK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8BAA8B,EAAE,eAAe,SAAS,sCAAsC,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,uDAAuD,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,wEAAwE,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,kCAAkC,CAAC;YAC1G,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6BAA6B,EAAE,UAAU,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;QACpI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAU,IAAI;AAChC;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAE3C,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,CAAC,IAAI,EAAE,mBAAmB;QAC1C,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,uHAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { notificationService, AppNotification } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({ isOpen, onClose }) => {\r\n  const { user } = useAuth();\r\n  const { showError, showSuccess } = useToast();\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('all');\r\n\r\n  // Fetch notifications\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!user) return;\r\n    \r\n    setLoading(true);\r\n    try {\r\n      const data = await notificationService.getUserNotifications({ \r\n        limit: 50,\r\n        status: filter === 'unread' ? 'unread' : undefined\r\n      });\r\n      \r\n      // Validate response data\r\n      if (data && Array.isArray(data.notifications)) {\r\n        setNotifications(data.notifications);\r\n      } else {\r\n        console.warn('Invalid notification data received:', data);\r\n        setNotifications([]);\r\n      }\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      const errorResponse = error && typeof error === 'object' && 'response' in error ? error.response : null;\r\n      \r\n      console.error('Error fetching notifications:', {\r\n        error: errorMessage,\r\n        response: errorResponse && typeof errorResponse === 'object' && 'data' in errorResponse ? errorResponse.data : null,\r\n        status: errorResponse && typeof errorResponse === 'object' && 'status' in errorResponse ? errorResponse.status : null,\r\n        filter\r\n      });\r\n      \r\n      setNotifications([]);\r\n      showError('Failed to load notifications. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [user, filter, showError]);\r\n\r\n  // Initial fetch and refetch when filter changes\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isOpen, fetchNotifications]);\r\n\r\n  // Mark notification as read\r\n  const markAsRead = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.markAsRead(notificationId);\r\n      setNotifications(prev => \r\n        prev.map(notification => \r\n          notification.notification_id === notificationId \r\n            ? { ...notification, status: 'read' as const }\r\n            : notification\r\n        )\r\n      );\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  // Mark all as read\r\n  const markAllAsRead = async () => {\r\n    try {\r\n      await notificationService.markAllAsRead();\r\n      setNotifications(prev => \r\n        prev.map(notification => ({ ...notification, status: 'read' as const }))\r\n      );\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  // Delete notification\r\n  const deleteNotification = async (notificationId: string) => {\r\n    try {\r\n      await notificationService.deleteNotification(notificationId);\r\n      setNotifications(prev => \r\n        prev.filter(notification => notification.notification_id !== notificationId)\r\n      );\r\n      showSuccess('Notification deleted');\r\n    } catch (error) {\r\n      console.error('Error deleting notification:', error);\r\n      showError('Failed to delete notification');\r\n    }\r\n  };\r\n\r\n  // Get notification icon based on type\r\n  const getNotificationIcon = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'status_change':\r\n        return 'ri-information-line';\r\n      case 'approval':\r\n        return 'ri-check-double-line';\r\n      case 'rejection':\r\n        return 'ri-close-circle-line';\r\n      case 'document_required':\r\n        return 'ri-file-text-line';\r\n      case 'reminder':\r\n        return 'ri-alarm-line';\r\n      default:\r\n        return 'ri-notification-line';\r\n    }\r\n  };\r\n\r\n  // Get notification color based on type\r\n  const getNotificationColor = (type: AppNotification['type']) => {\r\n    switch (type) {\r\n      case 'approval':\r\n        return 'text-green-600';\r\n      case 'rejection':\r\n        return 'text-red-600';\r\n      case 'document_required':\r\n        return 'text-orange-600';\r\n      case 'reminder':\r\n        return 'text-yellow-600';\r\n      default:\r\n        return 'text-blue-600';\r\n    }\r\n  };\r\n\r\n  // Format time ago\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) return 'Just now';\r\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\r\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\r\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\r\n    \r\n    return date.toLocaleDateString();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              <i className=\"ri-notification-line mr-2\"></i>\r\n              Notifications\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Filters and Actions */}\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'all'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                All\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\r\n                  filter === 'unread'\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                Unread\r\n              </button>\r\n            </div>\r\n\r\n            {notifications.some(n => n.status === 'unread') && (\r\n              <button\r\n                onClick={markAllAsRead}\r\n                className=\"text-sm text-primary hover:text-primary-dark focus:outline-none\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n\r\n          {/* Notifications List */}\r\n          <div className=\"max-h-96 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-8 text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n                <p className=\"mt-4 text-sm text-gray-500 dark:text-gray-400\">Loading notifications...</p>\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-8 text-center\">\r\n                <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-4\"></i>\r\n                <p className=\"text-gray-500 dark:text-gray-400\">\r\n                  {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-2\">\r\n                {notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.notification_id}\r\n                    className={`p-4 rounded-lg border transition-colors ${\r\n                      notification.status === 'unread' \r\n                        ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' \r\n                        : 'bg-white border-gray-200 dark:bg-gray-700 dark:border-gray-600'\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className={`flex-shrink-0 ${getNotificationColor(notification.type)}`}>\r\n                          <i className={`${getNotificationIcon(notification.type)} text-xl`}></i>\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                              {notification.title}\r\n                            </h4>\r\n                            {notification.status === 'unread' && (\r\n                              <div className=\"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2\"></div>\r\n                            )}\r\n                          </div>\r\n                          <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\r\n                            {notification.message}\r\n                          </p>\r\n                          <div className=\"mt-2 flex items-center justify-between\">\r\n                            <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                              {formatTimeAgo(notification.created_at)}\r\n                            </p>\r\n                            <div className=\"flex space-x-2\">\r\n                              {notification.status === 'unread' && (\r\n                                <button\r\n                                  onClick={() => markAsRead(notification.notification_id)}\r\n                                  className=\"text-xs text-primary hover:text-primary-dark focus:outline-none\"\r\n                                >\r\n                                  Mark as read\r\n                                </button>\r\n                              )}\r\n                              <button\r\n                                onClick={() => deleteNotification(notification.notification_id)}\r\n                                className=\"text-xs text-red-600 hover:text-red-700 focus:outline-none\"\r\n                              >\r\n                                Delete\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          <div className=\"flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Close\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,oBAAsD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAC9E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvD,sBAAsB;IACtB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;gBAC1D,OAAO;gBACP,QAAQ,WAAW,WAAW,WAAW;YAC3C;YAEA,yBAAyB;YACzB,IAAI,QAAQ,MAAM,OAAO,CAAC,KAAK,aAAa,GAAG;gBAC7C,iBAAiB,KAAK,aAAa;YACrC,OAAO;gBACL,QAAQ,IAAI,CAAC,uCAAuC;gBACpD,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,gBAAgB,SAAS,OAAO,UAAU,YAAY,cAAc,QAAQ,MAAM,QAAQ,GAAG;YAEnG,QAAQ,KAAK,CAAC,iCAAiC;gBAC7C,OAAO;gBACP,UAAU,iBAAiB,OAAO,kBAAkB,YAAY,UAAU,gBAAgB,cAAc,IAAI,GAAG;gBAC/G,QAAQ,iBAAiB,OAAO,kBAAkB,YAAY,YAAY,gBAAgB,cAAc,MAAM,GAAG;gBACjH;YACF;YAEA,iBAAiB,EAAE;YACnB,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAM;QAAQ;KAAU;IAE5B,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAmB;IAE/B,4BAA4B;IAC5B,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;YACrC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,eAAe,KAAK,iBAC7B;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,IAC3C;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,mBAAmB;IACnB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,aAAa;YACvC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;wBAAE,GAAG,YAAY;wBAAE,QAAQ;oBAAgB,CAAC;YAExE,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ;IACF;IAEA,sBAAsB;IACtB,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;YAC7C,iBAAiB,CAAA,OACf,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,eAAe,KAAK;YAE/D,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,UAAU;QACZ;IACF;IAEA,sCAAsC;IACtC,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;QAC5E,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;QAE/E,OAAO,KAAK,kBAAkB;IAChC;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAAgC;;;;;;;0CAG/C,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,QACP,0BACA,0GACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,WACP,0BACA,0GACJ;kDACH;;;;;;;;;;;;4BAKF,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,2BACpC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;mCAE7D,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAE,WAAU;8CACV,WAAW,WAAW,4BAA4B;;;;;;;;;;;iDAIvD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,WAAW,CAAC,wCAAwC,EAClD,aAAa,MAAM,KAAK,WACpB,wEACA,kEACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,cAAc,EAAE,qBAAqB,aAAa,IAAI,GAAG;8DACxE,cAAA,8OAAC;wDAAE,WAAW,GAAG,oBAAoB,aAAa,IAAI,EAAE,QAAQ,CAAC;;;;;;;;;;;8DAEnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,aAAa,KAAK;;;;;;gEAEpB,aAAa,MAAM,KAAK,0BACvB,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAGnB,8OAAC;4DAAE,WAAU;sEACV,aAAa,OAAO;;;;;;sEAEvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,cAAc,aAAa,UAAU;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;wEACZ,aAAa,MAAM,KAAK,0BACvB,8OAAC;4EACC,SAAS,IAAM,WAAW,aAAa,eAAe;4EACtD,WAAU;sFACX;;;;;;sFAIH,8OAAC;4EACC,SAAS,IAAM,mBAAmB,aAAa,eAAe;4EAC9D,WAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAxCN,aAAa,eAAe;;;;;;;;;;;;;;;kCAuD3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from './NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,wBAAwB;QACrD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,WAAW;0BAErC,cAAA,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,8OAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,8OAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,iJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 1599, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: string): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application with improved error handling\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    try {\r\n      console.log('Updating application:', id, 'with data:', data);\r\n      const response = await apiClient.put(`/applications/${id}`, data, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error updating application:', error);\r\n\r\n      // Handle specific error cases\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n\r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid application data';\r\n        console.error('400 Bad Request details:', error.response?.data);\r\n        throw new Error(`Bad Request: ${message}`);\r\n      }\r\n\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      // Use dedicated endpoint that explicitly filters by current user\r\n      const response = await apiClient.get('/applications/user-applications');\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Handle paginated response structure\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        // Single application or other structure\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      return applications;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application status\r\n  async updateStatus(applicationId: string, status: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/status`, { status });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign application to an officer\r\n  async assignApplication(applicationId: string, assignedTo: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/assign`, { assignedTo });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error assigning application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAc;QAC1C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAc;QACtD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,IAAI,cAAc;YACvD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE,MAAM;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,8BAA8B;YAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACjD,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE;gBAC1D,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS;YAC3C;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM;QACJ,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,IAAI,eAAe,EAAE;YACrB,IAAI,mBAAmB,MAAM;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,wCAAwC;gBACxC,eAAe;oBAAC;iBAAkB;YACpC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YAErC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAO;YACzF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAkB,aAAqB,EAAE,UAAkB;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAW;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1887, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useApplicationNotifications.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { notificationService, getStatusChangeMessage } from '@/services/notificationService';\r\nimport { ApplicationStatus } from '@/types/license';\r\n\r\ninterface ApplicationStatusChange {\r\n  application_id: string;\r\n  application_number: string;\r\n  license_category_name: string;\r\n  old_status: ApplicationStatus;\r\n  new_status: ApplicationStatus;\r\n  step?: number;\r\n  progress_percentage?: number;\r\n}\r\n\r\nexport const useApplicationNotifications = () => {\r\n  const { user } = useAuth();\r\n  const { showSuccess, showInfo, showWarning, showError } = useToast();\r\n\r\n  // Show toast notification for status changes\r\n  const showStatusChangeNotification = useCallback(\r\n    (statusChange: ApplicationStatusChange) => {\r\n      const { title, message, type } = getStatusChangeMessage(\r\n        statusChange.application_number,\r\n        statusChange.license_category_name,\r\n        statusChange.old_status,\r\n        statusChange.new_status,\r\n        statusChange.step,\r\n        statusChange.progress_percentage\r\n      );\r\n\r\n      const duration = type === 'success' ? 8000 : 6000; // Show success messages longer\r\n\r\n      switch (type) {\r\n        case 'success':\r\n          showSuccess(message, duration);\r\n          break;\r\n        case 'warning':\r\n          showWarning(message, duration);\r\n          break;\r\n        case 'error':\r\n          showError(message, duration);\r\n          break;\r\n        default:\r\n          showInfo(message, duration);\r\n      }\r\n    },\r\n    [showSuccess, showInfo, showWarning, showError]\r\n  );\r\n\r\n  // Poll for new notifications and show toasts\r\n  const checkForNewNotifications = useCallback(async () => {\r\n    if (!user) return;\r\n\r\n    try {\r\n      const data = await notificationService.getUserNotifications({ \r\n        limit: 5, \r\n        status: 'unread' \r\n      });\r\n\r\n      // Validate the response data\r\n      if (!data || !Array.isArray(data.notifications)) {\r\n        console.warn('Invalid notification data received:', data);\r\n        return;\r\n      }\r\n\r\n      // Show toast for recent unread notifications (within last 5 minutes)\r\n      const recentNotifications = data.notifications.filter(notification => {\r\n        try {\r\n          const createdAt = new Date(notification.created_at);\r\n          const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);\r\n          return createdAt > fiveMinutesAgo && notification.type === 'status_change';\r\n        } catch (error) {\r\n          console.warn('Error filtering notification:', notification, error);\r\n          return false;\r\n        }\r\n      });\r\n\r\n      recentNotifications.forEach(notification => {\r\n        try {\r\n          if (notification.metadata?.old_status && notification.metadata?.new_status) {\r\n            showStatusChangeNotification({\r\n              application_id: notification.application_id,\r\n              application_number: notification.application_number,\r\n              license_category_name: notification.license_category_name,\r\n              old_status: notification.metadata.old_status,\r\n              new_status: notification.metadata.new_status,\r\n              step: notification.metadata.step,\r\n              progress_percentage: notification.metadata.progress_percentage\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.warn('Error showing notification:', notification, error);\r\n        }\r\n      });\r\n    } catch (error: any) {\r\n      console.error('Error checking for new notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        userId: user?.id || 'unknown'\r\n      });\r\n      \r\n      // Don't show error toast for background notification checks\r\n      // as it would be annoying for users\r\n    }\r\n  }, [user, showStatusChangeNotification]);\r\n\r\n  // Check for notifications when component mounts and every 30 seconds\r\n  useEffect(() => {\r\n    if (user) {\r\n      // Initial check with a small delay to allow auth to settle\r\n      const initialTimeout = setTimeout(checkForNewNotifications, 1000);\r\n      \r\n      // Regular polling every 30 seconds\r\n      const interval = setInterval(checkForNewNotifications, 30000);\r\n      \r\n      return () => {\r\n        clearTimeout(initialTimeout);\r\n        clearInterval(interval);\r\n      };\r\n    }\r\n  }, [user, checkForNewNotifications]);\r\n\r\n  return {\r\n    showStatusChangeNotification,\r\n    checkForNewNotifications\r\n  };\r\n};\r\n\r\n// Hook for tracking application status changes in components\r\nexport const useApplicationStatusTracker = (applicationId?: string) => {\r\n  const { showStatusChangeNotification } = useApplicationNotifications();\r\n\r\n  const trackStatusChange = useCallback(\r\n    (\r\n      applicationNumber: string,\r\n      licenseCategoryName: string,\r\n      oldStatus: ApplicationStatus,\r\n      newStatus: ApplicationStatus,\r\n      step?: number,\r\n      progressPercentage?: number\r\n    ) => {\r\n      if (!applicationId) return;\r\n\r\n      // Show immediate toast notification\r\n      showStatusChangeNotification({\r\n        application_id: applicationId,\r\n        application_number: applicationNumber,\r\n        license_category_name: licenseCategoryName,\r\n        old_status: oldStatus,\r\n        new_status: newStatus,\r\n        step,\r\n        progress_percentage: progressPercentage\r\n      });\r\n\r\n      // Create notification record (this would typically be done by the backend)\r\n      notificationService.createStatusChangeNotification(\r\n        applicationId,\r\n        oldStatus,\r\n        newStatus,\r\n        step,\r\n        progressPercentage\r\n      ).catch(error => {\r\n        console.error('Error creating notification record:', error);\r\n      });\r\n    },\r\n    [applicationId, showStatusChangeNotification]\r\n  );\r\n\r\n  return { trackStatusChange };\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;AAkBO,MAAM,8BAA8B;IACzC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEjE,6CAA6C;IAC7C,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7C,CAAC;QACC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,yBAAsB,AAAD,EACpD,aAAa,kBAAkB,EAC/B,aAAa,qBAAqB,EAClC,aAAa,UAAU,EACvB,aAAa,UAAU,EACvB,aAAa,IAAI,EACjB,aAAa,mBAAmB;QAGlC,MAAM,WAAW,SAAS,YAAY,OAAO,MAAM,+BAA+B;QAElF,OAAQ;YACN,KAAK;gBACH,YAAY,SAAS;gBACrB;YACF,KAAK;gBACH,YAAY,SAAS;gBACrB;YACF,KAAK;gBACH,UAAU,SAAS;gBACnB;YACF;gBACE,SAAS,SAAS;QACtB;IACF,GACA;QAAC;QAAa;QAAU;QAAa;KAAU;IAGjD,6CAA6C;IAC7C,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;gBAC1D,OAAO;gBACP,QAAQ;YACV;YAEA,6BAA6B;YAC7B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,aAAa,GAAG;gBAC/C,QAAQ,IAAI,CAAC,uCAAuC;gBACpD;YACF;YAEA,qEAAqE;YACrE,MAAM,sBAAsB,KAAK,aAAa,CAAC,MAAM,CAAC,CAAA;gBACpD,IAAI;oBACF,MAAM,YAAY,IAAI,KAAK,aAAa,UAAU;oBAClD,MAAM,iBAAiB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK;oBACtD,OAAO,YAAY,kBAAkB,aAAa,IAAI,KAAK;gBAC7D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,iCAAiC,cAAc;oBAC5D,OAAO;gBACT;YACF;YAEA,oBAAoB,OAAO,CAAC,CAAA;gBAC1B,IAAI;oBACF,IAAI,aAAa,QAAQ,EAAE,cAAc,aAAa,QAAQ,EAAE,YAAY;wBAC1E,6BAA6B;4BAC3B,gBAAgB,aAAa,cAAc;4BAC3C,oBAAoB,aAAa,kBAAkB;4BACnD,uBAAuB,aAAa,qBAAqB;4BACzD,YAAY,aAAa,QAAQ,CAAC,UAAU;4BAC5C,YAAY,aAAa,QAAQ,CAAC,UAAU;4BAC5C,MAAM,aAAa,QAAQ,CAAC,IAAI;4BAChC,qBAAqB,aAAa,QAAQ,CAAC,mBAAmB;wBAChE;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,+BAA+B,cAAc;gBAC5D;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yCAAyC;gBACrD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,MAAM,QAAQ,EAAE,QAAQ;gBAClC,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,QAAQ,MAAM,MAAM;YACtB;QAEA,4DAA4D;QAC5D,oCAAoC;QACtC;IACF,GAAG;QAAC;QAAM;KAA6B;IAEvC,qEAAqE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,2DAA2D;YAC3D,MAAM,iBAAiB,WAAW,0BAA0B;YAE5D,mCAAmC;YACnC,MAAM,WAAW,YAAY,0BAA0B;YAEvD,OAAO;gBACL,aAAa;gBACb,cAAc;YAChB;QACF;IACF,GAAG;QAAC;QAAM;KAAyB;IAEnC,OAAO;QACL;QACA;IACF;AACF;AAGO,MAAM,8BAA8B,CAAC;IAC1C,MAAM,EAAE,4BAA4B,EAAE,GAAG;IAEzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CACE,mBACA,qBACA,WACA,WACA,MACA;QAEA,IAAI,CAAC,eAAe;QAEpB,oCAAoC;QACpC,6BAA6B;YAC3B,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QAEA,2EAA2E;QAC3E,sIAAA,CAAA,sBAAmB,CAAC,8BAA8B,CAChD,eACA,WACA,WACA,MACA,oBACA,KAAK,CAAC,CAAA;YACN,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF,GACA;QAAC;QAAe;KAA6B;IAG/C,OAAO;QAAE;IAAkB;AAC7B", "debugId": null}}, {"offset": {"line": 2034, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/my-licenses/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport Loader from '@/components/Loader';\r\nimport { useApplicationNotifications } from '@/hooks/useApplicationNotifications';\r\nimport { Application } from '@/types/license';\r\n\r\n\r\n// Status Modal Component\r\ninterface StatusModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  application: Application | null;\r\n  onContinueApplication?: (application: Application) => void;\r\n  canContinueApplication?: (application: Application) => boolean;\r\n  onStatusUpdate?: () => void; // Callback to refresh the applications list\r\n}\r\n\r\nconst StatusModal: React.FC<StatusModalProps> = React.memo(({\r\n  isOpen,\r\n  onClose,\r\n  application,\r\n  onContinueApplication,\r\n  canContinueApplication,\r\n  onStatusUpdate\r\n}) => {\r\n  const [currentApplication, setCurrentApplication] = useState<Application | null>(application);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchLatestApplicationStatus = useCallback(async () => {\r\n    if (!application?.application_id) return;\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const latestApplication = await applicationService.getApplication(application.application_id);\r\n      setCurrentApplication(latestApplication);\r\n\r\n      // Don't call onStatusUpdate here to prevent render loop\r\n      // Only call it when user explicitly refreshes\r\n    } catch (err: unknown) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n      setError(`Failed to load latest status: ${errorMessage}`);\r\n      // Fallback to the passed application data\r\n      setCurrentApplication(application);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [application]);\r\n\r\n  // Reset modal state when it's closed\r\n  useEffect(() => {\r\n    if (!isOpen) {\r\n      setCurrentApplication(null);\r\n      setError(null);\r\n      setLoading(false);\r\n    }\r\n  }, [isOpen]);\r\n\r\n  // Fetch latest application status from database when modal opens\r\n  useEffect(() => {\r\n    if (isOpen && application?.application_id) {\r\n      fetchLatestApplicationStatus();\r\n    }\r\n  }, [isOpen, application?.application_id, fetchLatestApplicationStatus]);\r\n\r\n  if (!isOpen || !application) {\r\n    console.log('🚫 StatusModal not rendering - isOpen:', isOpen, 'application:', !!application);\r\n    return null;\r\n  }\r\n\r\n  // Use currentApplication (from database) if available, otherwise fallback to passed application\r\n  const displayApplication = currentApplication || application;\r\n\r\n  const steps = [\r\n    { id: 1, name: 'Draft', description: 'Application incomplete', icon: 'ri-file-text-line' },\r\n    { id: 2, name: 'Submitted', description: 'Application received and logged', icon: 'ri-file-text-line' },\r\n    { id: 3, name: 'Under Review', description: 'Being reviewed by MACRA team', icon: 'ri-search-line' },\r\n    { id: 4, name: 'Evaluation', description: 'Technical evaluation in progress', icon: 'ri-clipboard-line' },\r\n    { id: 5, name: 'Approved', description: 'License approved and issued', icon: 'ri-check-line' },\r\n    { id: 6, name: 'Rejected', description: 'License rejected', icon: 'ri-check-line' }\r\n  ];\r\n\r\n  const getStepStatus = (stepId: number) => {\r\n    const status = displayApplication.status;\r\n\r\n    // Map database status to step progression\r\n    switch (status) {\r\n      case 'draft':\r\n        return stepId === 1 ? 'current' : 'upcoming';\r\n      case 'submitted':\r\n        return stepId <= 2 ? (stepId === 2 ? 'current' : 'complete') : 'upcoming';\r\n      case 'under_review':\r\n        return stepId <= 3 ? (stepId === 3 ? 'current' : 'complete') : 'upcoming';\r\n      case 'evaluation':\r\n        return stepId <= 4 ? (stepId === 4 ? 'current' : 'complete') : 'upcoming';\r\n      case 'approved':\r\n        return stepId <= 5 ? 'complete' : 'upcoming';\r\n      case 'rejected':\r\n        return stepId === 6 ? 'error' : stepId < 6 ? 'complete' : 'upcoming';\r\n      default:\r\n        return stepId === 1 ? 'current' : 'upcoming';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div\r\n          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-out sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\r\n          <div className=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n            {/* Loading indicator */}\r\n            {loading && (\r\n              <div className=\"flex items-center justify-center py-4\">\r\n                <Loader message=\"Loading latest status...\" />\r\n              </div>\r\n            )}\r\n\r\n            {/* Error message */}\r\n            {error && (\r\n              <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md\">\r\n                <p className=\"text-red-700 dark:text-red-200 text-sm\">{error}</p>\r\n              </div>\r\n            )}\r\n\r\n            {/* Modal header */}\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                Application Status\r\n                {currentApplication && (\r\n                  <span className=\"ml-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                    (Live from database)\r\n                  </span>\r\n                )}\r\n              </h3>\r\n              <button\r\n                type=\"button\"\r\n                title=\"Close modal\"\r\n                onClick={onClose}\r\n                className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n              >\r\n                <i className=\"ri-close-line text-xl\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            {/* Application info */}\r\n            <div className=\"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n              <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                {displayApplication.application_number}\r\n              </h4>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {displayApplication.license_category?.name || 'License Category'}\r\n              </p>\r\n              <div className=\"mt-2 flex items-center\">\r\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                  Current Status:\r\n                </span>\r\n                <span className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full ${\r\n                  displayApplication.status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\r\n                  displayApplication.status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\r\n                  displayApplication.status === 'under_review' || displayApplication.status === 'evaluation' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :\r\n                  displayApplication.status === 'submitted' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :\r\n                  'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'\r\n                }`}>\r\n                  {displayApplication.status?.replace('_', ' ').toUpperCase() || 'DRAFT'}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Status tracker */}\r\n            <div className=\"relative mb-6\">\r\n              {/* Progress line */}\r\n              <div className=\"absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600\">\r\n                <div\r\n                  className={`h-full bg-primary transition-all duration-500 ${\r\n                    displayApplication.status === 'approved' ? 'w-full' :\r\n                    displayApplication.status === 'evaluation' ? 'w-4/5' :\r\n                    displayApplication.status === 'under_review' ? 'w-3/5' :\r\n                    displayApplication.status === 'submitted' ? 'w-2/5' :\r\n                    displayApplication.status === 'draft' ? 'w-1/5' :\r\n                    'w-0'\r\n                  }`}\r\n                />\r\n              </div>\r\n\r\n              {/* Steps */}\r\n              <div className=\"relative flex justify-between\">\r\n                {steps.map((step) => {\r\n                  const stepStatus = getStepStatus(step.id);\r\n                  return (\r\n                    <div key={step.id} className=\"flex flex-col items-center\">\r\n                      <div className={`\r\n                        w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300\r\n                        ${stepStatus === 'complete' ? 'bg-primary border-primary text-white' :\r\n                          stepStatus === 'current' ? 'bg-primary border-primary text-white animate-pulse' :\r\n                          stepStatus === 'error' ? 'bg-red-500 border-red-500 text-white' :\r\n                          'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'}\r\n                      `}>\r\n                        <i className={step.icon}></i>\r\n                      </div>\r\n                      <div className=\"mt-3 text-center\">\r\n                        <div className={`text-sm font-medium ${\r\n                          stepStatus === 'complete' || stepStatus === 'current' ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'\r\n                        }`}>\r\n                          {step.name}\r\n                        </div>\r\n                        <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20\">\r\n                          {step.description}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Status details */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n                <div>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">Current Status</p>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                    {displayApplication.status?.replace('_', ' ').toUpperCase() || 'DRAFT'}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">Submitted</p>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                    {displayApplication.submitted_at\r\n                      ? new Date(displayApplication.submitted_at).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'long',\r\n                          day: 'numeric'\r\n                        })\r\n                      : displayApplication.created_at\r\n                      ? new Date(displayApplication.created_at).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'long',\r\n                          day: 'numeric'\r\n                        })\r\n                      : 'Not available'\r\n                    }\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">Estimated Time</p>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                    30-45 business days\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Modal footer */}\r\n          <div className=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm\"\r\n            >\r\n              Close\r\n            </button>\r\n\r\n            {/* Continue Application Button */}\r\n            {displayApplication && canContinueApplication && onContinueApplication && canContinueApplication(displayApplication) && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  onContinueApplication(displayApplication);\r\n                  onClose();\r\n                }}\r\n                className=\"w-full inline-flex justify-center rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mr-3 sm:w-auto sm:text-sm\"\r\n              >\r\n                <i className=\"ri-edit-line mr-2\"></i>\r\n                Continue Application\r\n              </button>\r\n            )}\r\n\r\n            {/* Refresh Status Button */}\r\n            <button\r\n              type=\"button\"\r\n              onClick={async () => {\r\n                await fetchLatestApplicationStatus();\r\n                // Only call onStatusUpdate when user explicitly refreshes\r\n                if (onStatusUpdate) {\r\n                  onStatusUpdate();\r\n                }\r\n              }}\r\n              disabled={loading}\r\n              className=\"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mr-3 sm:w-auto sm:text-sm disabled:opacity-50\"\r\n            >\r\n              <i className={`ri-refresh-line mr-2 ${loading ? 'animate-spin' : ''}`}></i>\r\n              Refresh Status\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nStatusModal.displayName = 'StatusModal';\r\n\r\nconst MyLicensesPage: React.FC = () => {\r\n  const { isAuthenticated, user } = useAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  \r\n  // Enable application notifications\r\n  useApplicationNotifications();\r\n  \r\n  const [applications, setApplications] = useState<Application[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [activeFilter, setActiveFilter] = useState<string>('all');\r\n  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);\r\n  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);\r\n  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);\r\n  const modalOpeningRef = React.useRef<boolean>(false);\r\n\r\n  // Check for success message from URL params\r\n  useEffect(() => {\r\n    if (searchParams.get('submitted') === 'true') {\r\n      setShowSuccessMessage(true);\r\n      // Remove the parameter from URL\r\n      const newUrl = new URL(window.location.href);\r\n      newUrl.searchParams.delete('submitted');\r\n      window.history.replaceState({}, '', newUrl.toString());\r\n\r\n      // Hide success message after 5 seconds\r\n      setTimeout(() => setShowSuccessMessage(false), 5000);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  const breadcrumbs = [\r\n    { label: 'Dashboard', href: '/customer' },\r\n    { label: 'My Licenses', href: '/customer/my-licenses' }\r\n  ];\r\n\r\n  // Modal functions\r\n  const openStatusModal = (application: Application) => {\r\n    if (modalOpeningRef.current) {\r\n      console.log('🚫 Modal already opening, ignoring duplicate click');\r\n      return;\r\n    }\r\n    \r\n    modalOpeningRef.current = true;\r\n    console.log('🔄 Opening status modal for application:', application.application_id);\r\n    setSelectedApplication(application);\r\n    // Small delay to ensure state is stable\r\n    setTimeout(() => {\r\n      setIsModalOpen(true);\r\n      modalOpeningRef.current = false;\r\n    }, 50);\r\n  };\r\n\r\n  const closeStatusModal = () => {\r\n    console.log('🔄 Closing status modal');\r\n    modalOpeningRef.current = false;\r\n    setIsModalOpen(false);\r\n    // Delay clearing the selected application to prevent flashing\r\n    setTimeout(() => {\r\n      setSelectedApplication(null);\r\n    }, 300);\r\n  };\r\n\r\n  // Function to continue working on an application\r\n  const handleContinueApplication = async (application: Application) => {\r\n    // Clear any existing errors\r\n    setError(null)\r\n    if (!application || !application.license_category_id) {\r\n      console.error('License category ID not found in application data:', application);\r\n      setError('Unable to continue application: License category information is missing. Please contact support.');\r\n      return;\r\n    }\r\n    // Build the continue URL using query parameters\r\n    const continueUrl = `/customer/applications/apply/applicant-info?license_category_id=${application.license_category_id}&application_id=${application.application_id}`;\r\n    router.push(continueUrl);\r\n  };\r\n\r\n  // Check if application can be continued (not submitted and incomplete)\r\n  const canContinueApplication = (application: Application) => {\r\n    const excludedStatuses = ['draft'];\r\n    return excludedStatuses.includes(application.status);\r\n  };\r\n\r\n  // Function to fetch user applications\r\n  const fetchUserApplications = useCallback(async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n       const applicationsData = await applicationService.getUserApplications();\r\n      // Ensure we have an array\r\n      const applications = Array.isArray(applicationsData) ? applicationsData : [];\r\n\r\n\r\n      // Fix empty status values and add default values\r\n      const processedApplications = applications.map((app: Application) => ({\r\n        ...app,\r\n        status: app.status || 'draft', // Default empty status to 'draft'\r\n        progress_percentage: app.progress_percentage || 0,\r\n        current_step: app.current_step || 1,\r\n        application_number: app.application_number || `APP-${app.application_id?.slice(0, 8)}`,\r\n        license_category: app.license_category ? {\r\n          ...app.license_category, // Preserve all license_category properties including license_type_id\r\n          name: app.license_category.name || 'License Category'\r\n        } : undefined // Set to undefined if no license_category data\r\n      }));\r\n\r\n      // Log status distribution for debugging\r\n      if (processedApplications.length > 0) {\r\n        const statusCounts = processedApplications.reduce((acc: Record<string, number>, app) => {\r\n          acc[app.status] = (acc[app.status] || 0) + 1;\r\n          return acc;\r\n        }, {});\r\n      }\r\n\r\n      setApplications(processedApplications);\r\n\r\n    } catch (err: unknown) {\r\n      console.error('Applications fetch error:', err);\r\n      const error = err as { response?: { status?: number; data?: { message?: string } }; message?: string };\r\n\r\n      if (error.response?.status === 404) {\r\n        // No applications found - this is okay\r\n        setApplications([]);\r\n        setError(null);\r\n      } else if (error.response?.status === 401) {\r\n        setError('Authentication required. Please log in again.');\r\n        router.push('/customer/auth/login');\r\n        return;\r\n      } else {\r\n        setError(error.response?.data?.message || error.message || 'Failed to fetch applications');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, router, user]);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isAuthenticated) {\r\n      fetchUserApplications(); // Load user applications    \r\n    }\r\n  }, [isAuthenticated, fetchUserApplications]);\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return (\r\n      <CustomerLayout breadcrumbs={breadcrumbs}>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto\"></div>\r\n            <p className=\"mt-4 text-gray-600\">Loading your licenses...</p>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout breadcrumbs={breadcrumbs}>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-red-600 dark:text-red-400 mb-4\">\r\n              <i className=\"ri-error-warning-line text-4xl\"></i>\r\n            </div>\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n              Failed to load applications\r\n            </h3>\r\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">{error}</p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                // Add delay to avoid rate limiting\r\n                setTimeout(() => fetchUserApplications(), 500);\r\n              }}\r\n              className=\"bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors\"\r\n            >\r\n              Try Again\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Client-side filtering with proper debugging\r\n  const filteredApplications = applications.filter((app: Application) => {\r\n    if (activeFilter === 'all') return true;\r\n\r\n    // Special handling for \"in_progress\" filter\r\n    if (activeFilter === 'in_progress') {\r\n      return canContinueApplication(app);\r\n    }\r\n\r\n    const matches = app.status === activeFilter;\r\n\r\n    // Debug logging for submitted filter\r\n    if (activeFilter === 'submitted') {\r\n      console.log(`App ${app.application_number}: status=\"${app.status}\", matches=${matches}`);\r\n    }\r\n\r\n    return matches;\r\n  });\r\n\r\n  // Debug: Log filter results\r\n  console.log(`Active filter: ${activeFilter}, Total apps: ${applications.length}, Filtered: ${filteredApplications.length}`);\r\n\r\n  // Debug: Show all unique statuses in the data\r\n  const uniqueStatuses = [...new Set(applications.map(app => app.status))];\r\n  console.log('Unique statuses in data:', uniqueStatuses);\r\n\r\n  const getStatusBadge = (status: string): React.ReactElement => {\r\n    const statusConfig = {\r\n      'draft': { color: 'bg-blue-100 text-blue-800', label: 'Draft' },\r\n      'submitted': { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },\r\n      'under_review': { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },\r\n      'evaluation': { color: 'bg-purple-100 text-purple-800', label: 'Evaluation' },\r\n      'approved': { color: 'bg-green-100 text-green-800', label: 'Approved' },\r\n      'rejected': { color: 'bg-red-100 text-red-800', label: 'Rejected' }\r\n    };\r\n\r\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.submitted;\r\n    return (\r\n      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${config.color}`}>\r\n        {config.label}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <CustomerLayout breadcrumbs={breadcrumbs}>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page header */}\r\n        <div className=\"mb-8\">\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">My Licenses</h1>\r\n            <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\r\n              Track your license applications and manage your approved licenses.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Success Message */}\r\n        {showSuccessMessage && (\r\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 text-xl mr-3\"></i>\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\r\n                  Application Submitted Successfully!\r\n                </h3>\r\n                <p className=\"text-sm text-green-700 dark:text-green-300 mt-1\">\r\n                  Your license application has been submitted and is now under review. You will receive email notifications about status updates.\r\n                </p>\r\n              </div>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setShowSuccessMessage(false)}\r\n                className=\"ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200\"\r\n                title=\"Close success message\"\r\n                aria-label=\"Close success message\"\r\n              >\r\n                <i className=\"ri-close-line text-lg\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Filter tabs */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8\">\r\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4\">Filter Applications</h2>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {[\r\n              { key: 'all', label: 'All Applications' },\r\n              { key: 'in_progress', label: 'In Progress' },\r\n              { key: 'submitted', label: 'Submitted' },\r\n              { key: 'under_review', label: 'Under Review' },\r\n              { key: 'evaluation', label: 'Evaluation' },\r\n              { key: 'approved', label: 'Approved' },\r\n              { key: 'rejected', label: 'Rejected' },\r\n              { key: 'draft', label: 'Draft' }\r\n            ].map(filter => (\r\n              <button\r\n                key={filter.key}\r\n                type=\"button\"\r\n                onClick={() => setActiveFilter(filter.key)}\r\n                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\r\n                  activeFilter === filter.key\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                {filter.label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Applications table */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">Applications Overview</h2>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n              View and track all your license applications\r\n            </p>\r\n          </div>\r\n\r\n          {filteredApplications.length === 0 ? (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"text-gray-400 dark:text-gray-500 mb-4\">\r\n                <i className=\"ri-file-list-line text-4xl\"></i>\r\n              </div>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                No applications found\r\n              </h3>\r\n              <p className=\"text-gray-600 dark:text-gray-400\">\r\n                {activeFilter === 'all'\r\n                  ? \"You haven't submitted any license applications yet.\"\r\n                  : `No applications with status \"${activeFilter.replace('_', ' ')}\" found.`\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                  <tr>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Application Details\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      License Category\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Status\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Progress\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Submitted\r\n                    </th>\r\n                    <th scope=\"col\" className=\"relative px-6 py-3\">\r\n                      <span className=\"sr-only\">Actions</span>\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                  {filteredApplications.map((application) => (\r\n                    <tr key={application.application_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3\">\r\n                            <i className=\"ri-file-text-line text-blue-600 dark:text-blue-400\"></i>\r\n                          </div>\r\n                          <div>\r\n                            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                              {application?.applicant?.name}\r\n                            </div>\r\n                            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                              Application Number: {application.application_number.slice(0, 8)}...\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                          {application.license_category?.name || 'License Category'}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          {application.license_category?.license_type?.name || 'License Type'}\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        {getStatusBadge(application.status)}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 relative overflow-hidden\">\r\n                            <div\r\n                              className={`${\r\n                                canContinueApplication(application) ? 'bg-blue-500' : 'bg-primary'\r\n                              } h-2 rounded-full transition-all duration-300 absolute top-0 left-0 ${\r\n                                (application.progress_percentage || 0) >= 100 ? 'w-full' :\r\n                                (application.progress_percentage || 0) >= 75 ? 'w-3/4' :\r\n                                (application.progress_percentage || 0) >= 50 ? 'w-1/2' :\r\n                                (application.progress_percentage || 0) >= 25 ? 'w-1/4' :\r\n                                (application.progress_percentage || 0) > 0 ? 'w-1/12' : 'w-0'\r\n                              }`}\r\n                            />\r\n                          </div>\r\n                          <div className=\"flex flex-col\">\r\n                            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                              {application.progress_percentage || 0}%\r\n                            </span>\r\n                            {canContinueApplication(application) && (\r\n                              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\r\n                                In Progress\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                          {application.submitted_at\r\n                            ? new Date(application.submitted_at).toLocaleDateString()\r\n                            : new Date(application.created_at).toLocaleDateString()\r\n                          }\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                        <div className=\"flex items-center justify-end space-x-2\">\r\n                          {/* Continue Application Button - for in-progress applications */}\r\n                          <button\r\n                            type=\"button\"\r\n                            title=\"Continue working on this application\"\r\n                            onClick={() => openStatusModal(application)}\r\n                            className=\"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-200 transition-colors\"\r\n                          >\r\n                            <i className=\"ri-eye-line\"></i>\r\n                            View\r\n                          </button>\r\n\r\n                          {/* Download License Button - for approved applications */}\r\n                          {application.status === 'approved' && (\r\n                            <button\r\n                              type=\"button\"\r\n                              title=\"Download license\"\r\n                              className=\"text-primary hover:text-red-700 transition-colors\"\r\n                            >\r\n                              <i className=\"ri-download-line\"></i>\r\n                            </button>\r\n                          )}\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Status Modal */}\r\n      {selectedApplication && (\r\n        <StatusModal\r\n          key={selectedApplication.application_id}\r\n          isOpen={isModalOpen}\r\n          onClose={closeStatusModal}\r\n          application={selectedApplication}\r\n          onContinueApplication={handleContinueApplication}\r\n          canContinueApplication={canContinueApplication}\r\n          onStatusUpdate={fetchUserApplications}\r\n        />\r\n      )}\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default MyLicensesPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAsBA,MAAM,4BAA0C,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAC1D,MAAM,EACN,OAAO,EACP,WAAW,EACX,qBAAqB,EACrB,sBAAsB,EACtB,cAAc,EACf;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/C,IAAI,CAAC,aAAa,gBAAgB;QAElC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,oBAAoB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,YAAY,cAAc;YAC5F,sBAAsB;QAEtB,wDAAwD;QACxD,8CAA8C;QAChD,EAAE,OAAO,KAAc;YACrB,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,8BAA8B,EAAE,cAAc;YACxD,0CAA0C;YAC1C,sBAAsB;QACxB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAY;IAEhB,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;YACX,sBAAsB;YACtB,SAAS;YACT,WAAW;QACb;IACF,GAAG;QAAC;KAAO;IAEX,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,aAAa,gBAAgB;YACzC;QACF;IACF,GAAG;QAAC;QAAQ,aAAa;QAAgB;KAA6B;IAEtE,IAAI,CAAC,UAAU,CAAC,aAAa;QAC3B,QAAQ,GAAG,CAAC,0CAA0C,QAAQ,gBAAgB,CAAC,CAAC;QAChF,OAAO;IACT;IAEA,gGAAgG;IAChG,MAAM,qBAAqB,sBAAsB;IAEjD,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,MAAM;YAAS,aAAa;YAA0B,MAAM;QAAoB;QACzF;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;YAAmC,MAAM;QAAoB;QACtG;YAAE,IAAI;YAAG,MAAM;YAAgB,aAAa;YAAgC,MAAM;QAAiB;QACnG;YAAE,IAAI;YAAG,MAAM;YAAc,aAAa;YAAoC,MAAM;QAAoB;QACxG;YAAE,IAAI;YAAG,MAAM;YAAY,aAAa;YAA+B,MAAM;QAAgB;QAC7F;YAAE,IAAI;YAAG,MAAM;YAAY,aAAa;YAAoB,MAAM;QAAgB;KACnF;IAED,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,mBAAmB,MAAM;QAExC,0CAA0C;QAC1C,OAAQ;YACN,KAAK;gBACH,OAAO,WAAW,IAAI,YAAY;YACpC,KAAK;gBACH,OAAO,UAAU,IAAK,WAAW,IAAI,YAAY,aAAc;YACjE,KAAK;gBACH,OAAO,UAAU,IAAK,WAAW,IAAI,YAAY,aAAc;YACjE,KAAK;gBACH,OAAO,UAAU,IAAK,WAAW,IAAI,YAAY,aAAc;YACjE,KAAK;gBACH,OAAO,UAAU,IAAI,aAAa;YACpC,KAAK;gBACH,OAAO,WAAW,IAAI,UAAU,SAAS,IAAI,aAAa;YAC5D;gBACE,OAAO,WAAW,IAAI,YAAY;QACtC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAEZ,yBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;wCAAC,SAAQ;;;;;;;;;;;gCAKnB,uBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAuD;gDAElE,oCACC,8OAAC;oDAAK,WAAU;8DAAgD;;;;;;;;;;;;sDAKpE,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAKjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,mBAAmB,kBAAkB;;;;;;sDAExC,8OAAC;4CAAE,WAAU;sDACV,mBAAmB,gBAAgB,EAAE,QAAQ;;;;;;sDAEhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC;oDAAK,WAAW,CAAC,kDAAkD,EAClE,mBAAmB,MAAM,KAAK,aAAa,sEAC3C,mBAAmB,MAAM,KAAK,aAAa,8DAC3C,mBAAmB,MAAM,KAAK,kBAAkB,mBAAmB,MAAM,KAAK,eAAe,0EAC7F,mBAAmB,MAAM,KAAK,cAAc,kEAC5C,iEACA;8DACC,mBAAmB,MAAM,EAAE,QAAQ,KAAK,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;8CAMrE,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAW,CAAC,8CAA8C,EACxD,mBAAmB,MAAM,KAAK,aAAa,WAC3C,mBAAmB,MAAM,KAAK,eAAe,UAC7C,mBAAmB,MAAM,KAAK,iBAAiB,UAC/C,mBAAmB,MAAM,KAAK,cAAc,UAC5C,mBAAmB,MAAM,KAAK,UAAU,UACxC,OACA;;;;;;;;;;;sDAKN,8OAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC;gDACV,MAAM,aAAa,cAAc,KAAK,EAAE;gDACxC,qBACE,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;4DAAI,WAAW,CAAC;;wBAEf,EAAE,eAAe,aAAa,yCAC5B,eAAe,YAAY,uDAC3B,eAAe,UAAU,yCACzB,+EAA+E;sBACnF,CAAC;sEACC,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAW,CAAC,oBAAoB,EACnC,eAAe,cAAc,eAAe,YAAY,qCAAqC,oCAC7F;8EACC,KAAK,IAAI;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;8EACZ,KAAK,WAAW;;;;;;;;;;;;;mDAjBb,KAAK,EAAE;;;;;4CAsBrB;;;;;;;;;;;;8CAKJ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEACV,mBAAmB,MAAM,EAAE,QAAQ,KAAK,KAAK,iBAAiB;;;;;;;;;;;;0DAGnE,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEACV,mBAAmB,YAAY,GAC5B,IAAI,KAAK,mBAAmB,YAAY,EAAE,kBAAkB,CAAC,SAAS;4DACpE,MAAM;4DACN,OAAO;4DACP,KAAK;wDACP,KACA,mBAAmB,UAAU,GAC7B,IAAI,KAAK,mBAAmB,UAAU,EAAE,kBAAkB,CAAC,SAAS;4DAClE,MAAM;4DACN,OAAO;4DACP,KAAK;wDACP,KACA;;;;;;;;;;;;0DAIR,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;gCAKA,sBAAsB,0BAA0B,yBAAyB,uBAAuB,qCAC/F,8OAAC;oCACC,MAAK;oCACL,SAAS;wCACP,sBAAsB;wCACtB;oCACF;oCACA,WAAU;;sDAEV,8OAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;8CAMzC,8OAAC;oCACC,MAAK;oCACL,SAAS;wCACP,MAAM;wCACN,0DAA0D;wCAC1D,IAAI,gBAAgB;4CAClB;wCACF;oCACF;oCACA,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAE,WAAW,CAAC,qBAAqB,EAAE,UAAU,iBAAiB,IAAI;;;;;;wCAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzF;AAEA,YAAY,WAAW,GAAG;AAE1B,MAAM,iBAA2B;IAC/B,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,mCAAmC;IACnC,CAAA,GAAA,2IAAA,CAAA,8BAA2B,AAAD;IAE1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,kBAAkB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAU;IAE9C,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,GAAG,CAAC,iBAAiB,QAAQ;YAC5C,sBAAsB;YACtB,gCAAgC;YAChC,MAAM,SAAS,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;YAC3C,OAAO,YAAY,CAAC,MAAM,CAAC;YAC3B,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,OAAO,QAAQ;YAEnD,uCAAuC;YACvC,WAAW,IAAM,sBAAsB,QAAQ;QACjD;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,cAAc;QAClB;YAAE,OAAO;YAAa,MAAM;QAAY;QACxC;YAAE,OAAO;YAAe,MAAM;QAAwB;KACvD;IAED,kBAAkB;IAClB,MAAM,kBAAkB,CAAC;QACvB,IAAI,gBAAgB,OAAO,EAAE;YAC3B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,gBAAgB,OAAO,GAAG;QAC1B,QAAQ,GAAG,CAAC,4CAA4C,YAAY,cAAc;QAClF,uBAAuB;QACvB,wCAAwC;QACxC,WAAW;YACT,eAAe;YACf,gBAAgB,OAAO,GAAG;QAC5B,GAAG;IACL;IAEA,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,gBAAgB,OAAO,GAAG;QAC1B,eAAe;QACf,8DAA8D;QAC9D,WAAW;YACT,uBAAuB;QACzB,GAAG;IACL;IAEA,iDAAiD;IACjD,MAAM,4BAA4B,OAAO;QACvC,4BAA4B;QAC5B,SAAS;QACT,IAAI,CAAC,eAAe,CAAC,YAAY,mBAAmB,EAAE;YACpD,QAAQ,KAAK,CAAC,sDAAsD;YACpE,SAAS;YACT;QACF;QACA,gDAAgD;QAChD,MAAM,cAAc,CAAC,gEAAgE,EAAE,YAAY,mBAAmB,CAAC,gBAAgB,EAAE,YAAY,cAAc,EAAE;QACrK,OAAO,IAAI,CAAC;IACd;IAEA,uEAAuE;IACvE,MAAM,yBAAyB,CAAC;QAC9B,MAAM,mBAAmB;YAAC;SAAQ;QAClC,OAAO,iBAAiB,QAAQ,CAAC,YAAY,MAAM;IACrD;IAEA,sCAAsC;IACtC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,WAAW;QACX,SAAS;QAET,IAAI;YACD,MAAM,mBAAmB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,mBAAmB;YACtE,0BAA0B;YAC1B,MAAM,eAAe,MAAM,OAAO,CAAC,oBAAoB,mBAAmB,EAAE;YAG5E,iDAAiD;YACjD,MAAM,wBAAwB,aAAa,GAAG,CAAC,CAAC,MAAqB,CAAC;oBACpE,GAAG,GAAG;oBACN,QAAQ,IAAI,MAAM,IAAI;oBACtB,qBAAqB,IAAI,mBAAmB,IAAI;oBAChD,cAAc,IAAI,YAAY,IAAI;oBAClC,oBAAoB,IAAI,kBAAkB,IAAI,CAAC,IAAI,EAAE,IAAI,cAAc,EAAE,MAAM,GAAG,IAAI;oBACtF,kBAAkB,IAAI,gBAAgB,GAAG;wBACvC,GAAG,IAAI,gBAAgB;wBACvB,MAAM,IAAI,gBAAgB,CAAC,IAAI,IAAI;oBACrC,IAAI,UAAU,+CAA+C;gBAC/D,CAAC;YAED,wCAAwC;YACxC,IAAI,sBAAsB,MAAM,GAAG,GAAG;gBACpC,MAAM,eAAe,sBAAsB,MAAM,CAAC,CAAC,KAA6B;oBAC9E,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI;oBAC3C,OAAO;gBACT,GAAG,CAAC;YACN;YAEA,gBAAgB;QAElB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,QAAQ;YAEd,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,uCAAuC;gBACvC,gBAAgB,EAAE;gBAClB,SAAS;YACX,OAAO,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBACzC,SAAS;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF,OAAO;gBACL,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;YAC7D;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAiB;QAAQ;KAAK;IAIlC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,yBAAyB,6BAA6B;QACxD;IACF,GAAG;QAAC;QAAiB;KAAsB;IAE3C,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,8OAAC,gJAAA,CAAA,UAAc;YAAC,aAAa;sBAC3B,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,mBAAmB;IACnB,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;YAAC,aAAa;sBAC3B,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCACtD,8OAAC;4BACC,MAAK;4BACL,SAAS;gCACP,mCAAmC;gCACnC,WAAW,IAAM,yBAAyB;4BAC5C;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,8CAA8C;IAC9C,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAC;QAChD,IAAI,iBAAiB,OAAO,OAAO;QAEnC,4CAA4C;QAC5C,IAAI,iBAAiB,eAAe;YAClC,OAAO,uBAAuB;QAChC;QAEA,MAAM,UAAU,IAAI,MAAM,KAAK;QAE/B,qCAAqC;QACrC,IAAI,iBAAiB,aAAa;YAChC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;QACzF;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa,cAAc,EAAE,aAAa,MAAM,CAAC,YAAY,EAAE,qBAAqB,MAAM,EAAE;IAE1H,8CAA8C;IAC9C,MAAM,iBAAiB;WAAI,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;KAAG;IACxE,QAAQ,GAAG,CAAC,4BAA4B;IAExC,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAA6B,OAAO;YAAQ;YAC9D,aAAa;gBAAE,OAAO;gBAA6B,OAAO;YAAY;YACtE,gBAAgB;gBAAE,OAAO;gBAAiC,OAAO;YAAe;YAChF,cAAc;gBAAE,OAAO;gBAAiC,OAAO;YAAa;YAC5E,YAAY;gBAAE,OAAO;gBAA+B,OAAO;YAAW;YACtE,YAAY;gBAAE,OAAO;gBAA2B,OAAO;YAAW;QACpE;QAEA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,SAAS;QAC1F,qBACE,8OAAC;YAAK,WAAW,CAAC,mEAAmE,EAAE,OAAO,KAAK,EAAE;sBAClG,OAAO,KAAK;;;;;;IAGnB;IAEA,qBACE,8OAAC,gJAAA,CAAA,UAAc;QAAC,aAAa;;0BAC3B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CACpE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;;;;;;oBAOxD,oCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyD;;;;;;sDAGvE,8OAAC;4CAAE,WAAU;sDAAkD;;;;;;;;;;;;8CAIjE,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,sBAAsB;oCACrC,WAAU;oCACV,OAAM;oCACN,cAAW;8CAEX,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAC5E,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,KAAK;wCAAO,OAAO;oCAAmB;oCACxC;wCAAE,KAAK;wCAAe,OAAO;oCAAc;oCAC3C;wCAAE,KAAK;wCAAa,OAAO;oCAAY;oCACvC;wCAAE,KAAK;wCAAgB,OAAO;oCAAe;oCAC7C;wCAAE,KAAK;wCAAc,OAAO;oCAAa;oCACzC;wCAAE,KAAK;wCAAY,OAAO;oCAAW;oCACrC;wCAAE,KAAK;wCAAY,OAAO;oCAAW;oCACrC;wCAAE,KAAK;wCAAS,OAAO;oCAAQ;iCAChC,CAAC,GAAG,CAAC,CAAA,uBACJ,8OAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,gBAAgB,OAAO,GAAG;wCACzC,WAAW,CAAC,2DAA2D,EACrE,iBAAiB,OAAO,GAAG,GACvB,0BACA,0GACJ;kDAED,OAAO,KAAK;uCATR,OAAO,GAAG;;;;;;;;;;;;;;;;kCAgBvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDACvE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;4BAK9D,qBAAqB,MAAM,KAAK,kBAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,QACd,wDACA,CAAC,6BAA6B,EAAE,aAAa,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC;;;;;;;;;;;qDAKhF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEACxB,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAM,WAAU;sDACd,qBAAqB,GAAG,CAAC,CAAC,4BACzB,8OAAC;oDAAoC,WAAU;;sEAC7C,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAE,WAAU;;;;;;;;;;;kFAEf,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FACZ,aAAa,WAAW;;;;;;0FAE3B,8OAAC;gFAAI,WAAU;;oFAA2C;oFACnC,YAAY,kBAAkB,CAAC,KAAK,CAAC,GAAG;oFAAG;;;;;;;;;;;;;;;;;;;;;;;;sEAKxE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;8EACZ,YAAY,gBAAgB,EAAE,QAAQ;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;8EACZ,YAAY,gBAAgB,EAAE,cAAc,QAAQ;;;;;;;;;;;;sEAGzD,8OAAC;4DAAG,WAAU;sEACX,eAAe,YAAY,MAAM;;;;;;sEAEpC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EACC,WAAW,GACT,uBAAuB,eAAe,gBAAgB,aACvD,oEAAoE,EACnE,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,MAAM,WAChD,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,KAAK,UAC/C,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,KAAK,UAC/C,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,KAAK,UAC/C,CAAC,YAAY,mBAAmB,IAAI,CAAC,IAAI,IAAI,WAAW,OACxD;;;;;;;;;;;kFAGN,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;;oFACb,YAAY,mBAAmB,IAAI;oFAAE;;;;;;;4EAEvC,uBAAuB,8BACtB,8OAAC;gFAAK,WAAU;0FAAuD;;;;;;;;;;;;;;;;;;;;;;;sEAO/E,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;0EACZ,YAAY,YAAY,GACrB,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,KACrD,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;;;;;;sEAI3D,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFAEb,8OAAC;wEACC,MAAK;wEACL,OAAM;wEACN,SAAS,IAAM,gBAAgB;wEAC/B,WAAU;;0FAEV,8OAAC;gFAAE,WAAU;;;;;;4EAAkB;;;;;;;oEAKhC,YAAY,MAAM,KAAK,4BACtB,8OAAC;wEACC,MAAK;wEACL,OAAM;wEACN,WAAU;kFAEV,cAAA,8OAAC;4EAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAlFd,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAiGhD,qCACC,8OAAC;gBAEC,QAAQ;gBACR,SAAS;gBACT,aAAa;gBACb,uBAAuB;gBACvB,wBAAwB;gBACxB,gBAAgB;eANX,oBAAoB,cAAc;;;;;;;;;;;AAWjD;uCAEe", "debugId": null}}]}