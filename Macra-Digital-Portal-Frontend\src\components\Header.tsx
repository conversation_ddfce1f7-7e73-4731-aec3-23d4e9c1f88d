'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '../contexts/AuthContext';
import UserMenu from './UserMenu';
import LogoutButton from './LogoutButton';
import { getUserInitials } from '../utils/imageUtils';
import { useNotifications } from '../hooks/useNotifications';
import NotificationModal from './notifications/NotificationModal';

interface HeaderProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  onMobileMenuToggle?: () => void;
}

const Header = ({ activeTab = 'overview', onTabChange, onMobileMenuToggle }: HeaderProps) => {
  const { isAuthenticated, user } = useAuth();
  const { unreadCount } = useNotifications();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notificationModalOpen, setNotificationModalOpen] = useState(false);
  const pathname = usePathname();

  // Only show dashboard tabs when on dashboard routes
  const showDashboardTabs = pathname.startsWith('/dashboard');

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const toggleNotificationModal = () => {
    setNotificationModalOpen(!notificationModalOpen);
  };

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'licenses', label: 'Licenses' },
    { id: 'users', label: 'Users' },
    { id: 'transactions', label: 'Transactions' },
    { id: 'spectrum', label: 'Spectrum' },
    { id: 'compliance', label: 'Compliance' },
  ];

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm z-10">
      <div className="flex items-center justify-between h-16 px-4 sm:px-6">
        <button
          id="mobileMenuBtn"
          type="button"
          onClick={onMobileMenuToggle}
          className="md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none"
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <i className="ri-menu-line ri-lg"></i>
          </div>
        </button>
        <div className="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
          <div className="max-w-lg w-full">
            <label htmlFor="search" className="sr-only">Search</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <div className="w-5 h-5 flex items-center justify-center text-gray-400 dark:text-gray-500">
                  <i className="ri-search-line"></i>
                </div>
              </div>
              <input
                id="search"
                name="search"
                className="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white dark:hover:bg-gray-600 transition-colors"
                placeholder="Search for licenses, users, or transactions..."
                type="search"
              />
            </div>
          </div>
        </div>
        <div className="flex items-center">
          <button
            type="button"
            onClick={toggleNotificationModal}
            className="flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative transition-colors duration-200"
            title={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
          >
            <span className="sr-only">View notifications</span>
            <div className="w-6 h-6 flex items-center justify-center">
              <i className="ri-notification-3-line ri-lg"></i>
            </div>
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 flex items-center justify-center h-5 w-5 text-xs font-bold text-white bg-red-500 rounded-full ring-2 ring-white dark:ring-gray-800">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>
          <div className="dropdown relative">
            <button
              type="button"
              onClick={toggleDropdown}
              className="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <span className="sr-only">Open user menu</span>
              {user?.profile_image ? (
                <img
                  className="h-8 w-8 rounded-full object-cover"
                  src={user.profile_image}
                  alt="Profile"
                  onError={(e) => {
                    // Fallback to initials if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              <div className={`h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user?.profile_image ? 'hidden' : ''}`}>
                {user ? getUserInitials(user.first_name, user.last_name) : 'U'}
              </div>
            </button>
            <div
              className={`dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5 ${
                dropdownOpen ? 'show' : ''
              }`}
            >
              <div className="py-1">
                <Link
                  href="/profile"
                  className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Your Profile
                </Link>
                <Link
                  href="/settings"
                  className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Settings
                </Link>
                <div className="px-4 py-2">
                  <LogoutButton
                    variant="text"
                    size="sm"
                    className="w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    showConfirmation={true}
                    redirectTo="/auth/login"
                  >
                    Sign out
                  </LogoutButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Secondary navigation - only show on dashboard routes */}
      {showDashboardTabs && (
        <div className="border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6">
          <div className="py-3 flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => {
                  onTabChange?.(tab.id);
                  // Dispatch custom event for dashboard to listen to
                  window.dispatchEvent(new CustomEvent('tabChange', { detail: { tab: tab.id } }));
                }}
                className={`tab-button text-sm px-1 py-2 ${
                  activeTab === tab.id ? 'active' : 'text-gray-500'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Notification Modal */}
      <NotificationModal
        isOpen={notificationModalOpen}
        onClose={() => setNotificationModalOpen(false)}
      />
    </header>
  );
};

export default Header;