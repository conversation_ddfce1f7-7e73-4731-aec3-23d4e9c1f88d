'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { useDebouncedCallback } from 'use-debounce';
import { customerApi, EditAddressData, PostalCodeLookupResult, SearchPostcodes } from '@/lib/customer-api';
import { CreateAddressData } from '@/lib/customer-api';

// Types based on backend DTOs

export interface CreateApplicantData {
  name: string;
  business_registration_number: string;
  tpin: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  level_of_insurance_cover?: string;
  physical_address_id?: string;
  postal_address_id?: string;
  contact_id?: string;
  date_incorporation: string; // ISO date string
  place_incorporation: string;
}

export interface CreateApplicationData {
  application_number: string;
  applicant_id: string;
  license_category_id: string;
  status?: 'submitted' | 'under_review' | 'evaluation' | 'approved' | 'rejected';
  current_step?: number;
  progress_percentage?: number;
  submitted_at?: string;
}

// Define LicenseCategory type (update fields as needed to match your backend DTO)
export interface LicenseCategory {
  license_category_id: string;
  name: string;
  code: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Application {
  application_id: string;
  application_number: string;
  applicant_id: string;
  license_category_id: string;
  status: string;
  current_step: number;
  progress_percentage: number;
  submitted_at?: string;
  created_at: string;
  updated_at: string;
  applicant?: Applicant;
  license_category?: LicenseCategory;
}

export interface Applicant {
  applicant_id: string;
  name: string;
  business_registration_number: string;
  tpin: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  level_of_insurance_cover?: string;
  date_incorporation: string;
  place_incorporation: string;
  created_at: string;
  updated_at: string;
}

export interface Address {
  address_id: string;
  address_type: string;
  entity_type: string;
  entity_id: string;
  address_line_1: string;
  address_line_2?: string;
  postal_code: string;
  country: string;
  city: string;
  created_at: string;
  updated_at: string;
}

export type ShareholderType = {
  name: string;
  nationality: string;
  address: string;
  shareholding: string;
};

export type DirectorType = {
  name: string;
  nationality: string;
  address: string;
};

export type ManagementTeamType = {
  name: string;
  address: string;
  nationality: string;
  qualifications: string;
};

// Application service functions using customer API
const applicationService = {
  // Applicant methods
  async createApplicant(data: CreateApplicantData): Promise<Applicant> {
    const response = await customerApi.api.post('/applicants', data);
    return response.data;
  },

  async getApplicants(params?: { page?: number; limit?: number }): Promise<{ data: Applicant[]; meta: Record<string, unknown> }> {
    const response = await customerApi.api.get('/applicants', { params });
    return response.data;
  },

  async getApplicant(id: string): Promise<Applicant> {
    const response = await customerApi.api.get(`/applicants/${id}`);
    return response.data;
  },

  // Application methods
  async createApplication(data: CreateApplicationData): Promise<Application> {
    const response = await customerApi.api.post('/applications', data);
    return response.data;
  },

  async getApplications(params?: { page?: number; limit?: number; status?: string }): Promise<{ data: Application[]; meta: Record<string, unknown> }> {
    const response = await customerApi.api.get('/applications', { params });
    return response.data;
  },

  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {
    const response = await customerApi.api.get(`/applications/by-applicant/${applicantId}`);
    return response.data;
  },

  async getApplication(id: string): Promise<Application> {
    const response = await customerApi.api.get(`/applications/${id}`);
    return response.data;
  },

  async updateApplication(id: string, data: Partial<CreateApplicationData>): Promise<Application> {
    const response = await customerApi.api.put(`/applications/${id}`, data);
    return response.data;
  },

  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {
    const response = await customerApi.api.patch(`/applications/${id}/progress`, {
      current_step: currentStep,
      progress_percentage: progressPercentage
    });
    return response.data;
  },

  async submitApplication(id: string): Promise<Application> {
    const response = await customerApi.api.patch(`/applications/${id}/status`, {
      status: 'submitted',
      submitted_at: new Date().toISOString()
    });
    return response.data;
  },

  // Generate application number - Format: COU-2024-001 or PST-2024-001
  generateApplicationNumber(licenseTypeCode: string): string {
    const year = new Date().getFullYear();
    const randomNum = Math.floor(Math.random() * 999) + 1;
    // Format must match: ^[A-Z]{2,3}-[0-9]{4}-[0-9]{2,3}$
    return `${licenseTypeCode}-${year}-${randomNum.toString().padStart(3, '0')}`;
  }
};

// Address service using customer API
const addressService = {
  async createAddress(data: CreateAddressData): Promise<Address> {
    const response = await customerApi.createAddress(data);
    return response.data;
  },

  async getAddress(id: string): Promise<Address> {
    const response = await customerApi.getAddress(id);
    return response.data;
  },

  async editAddress(data: EditAddressData): Promise<Address> {
    const response = await customerApi.editAddress(data);
    return response.data;
  },

  
  async searchPostcodes(searchParams: SearchPostcodes): Promise<PostalCodeLookupResult[]> {
    const response = await customerApi.searchPostcodes(searchParams);
    return response.data;
  },

}

// Hook for managing applications
export const useApplications = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchApplications = async (params?: { page?: number; limit?: number; status?: string }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await applicationService.getApplications(params);
      setApplications(response.data || []);
    } catch (err: unknown) {
      console.error('Applications fetch error:', err);

      // Handle rate limiting
      type ErrorWithResponse = {
        response?: {
          status?: number;
          data?: { message?: string };
        };
        message?: string;
      };

      const errorObj = err as ErrorWithResponse;

      if (typeof err === 'object' && err !== null && 'response' in err && errorObj.response?.status === 429) {
        setError('Too many requests. Please wait a moment and try again.');
        toast.error('Rate limited. Please wait a moment before refreshing.');
      } else if (typeof err === 'object' && err !== null && 'response' in err && errorObj.response?.status === 404) {
        // No applications found - this is okay
        setApplications([]);
        setError(null);
      } else if (typeof err === 'object' && err !== null && 'response' in err && errorObj.response?.data?.message) {
        setError(errorObj.response.data.message || errorObj.message || 'Failed to fetch applications');
        toast.error('Failed to fetch applications');
      } else if (typeof err === 'object' && err !== null && 'message' in err) {
        setError(errorObj.message || 'Failed to fetch applications');
        toast.error('Failed to fetch applications');
      } else {
        setError('Failed to fetch applications');
        toast.error('Failed to fetch applications');
      }
    } finally {
      setLoading(false);
    }
  };

  const createApplication = async (data: CreateApplicationData) => {
    setLoading(true);
    setError(null);
    try {
      const newApplication = await applicationService.createApplication(data);
      setApplications(prev => [newApplication, ...prev]);
      toast.success('Application created successfully');
      return newApplication;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to create application');
      }
      toast.error('Failed to create application');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateApplicationProgress = async (id: string, currentStep: number, progressPercentage: number) => {
    try {
      const updatedApplication = await applicationService.updateApplicationProgress(id, currentStep, progressPercentage);
      setApplications(prev => prev.map(app => app.application_id === id ? updatedApplication : app));
      return updatedApplication;
    } catch (err: unknown) {
      toast.error('Failed to update application progress');
      throw err;
    }
  };

  const submitApplication = async (id: string) => {
    try {
      const submittedApplication = await applicationService.submitApplication(id);
      setApplications(prev => prev.map(app => app.application_id === id ? submittedApplication : app));
      toast.success('Application submitted successfully');
      return submittedApplication;
    } catch (err: unknown) {
      toast.error('Failed to submit application');
      throw err;
    }
  };

  return {
    applications,
    loading,
    error,
    fetchApplications,
    createApplication,
    updateApplicationProgress,
    submitApplication,
    applicationService,
  };
};

// Hook for managing applicants
export const useApplicants = () => {
  const [applicants, setApplicants] = useState<Applicant[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchApplicants = async (params?: { page?: number; limit?: number }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await applicationService.getApplicants(params);
      setApplicants(response.data);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to fetch applicants');
      }
      toast.error('Failed to fetch applicants');
    } finally {
      setLoading(false);
    }
  };

  const createApplicant = async (data: CreateApplicantData) => {
    setLoading(true);
    setError(null);
    try {
      const newApplicant = await applicationService.createApplicant(data);
      setApplicants(prev => [newApplicant, ...prev]);
      toast.success('Applicant created successfully');
      return newApplicant;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to create applicant');
      }
      toast.error('Failed to create applicant');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    applicants,
    loading,
    error,
    fetchApplicants,
    createApplicant,
    applicationService,
  };
};

// Hook for managing addresses
export const useAddresses = (initialSearchParams?: SearchPostcodes) => {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useState<SearchPostcodes>(initialSearchParams || {});

  const [postcodeSuggestions, setPostcodeSuggestions] = useState<PostalCodeLookupResult[]>([]);
  const [searching, setSearching] = useState(false);

  // Fetch address list when searchParams change
  useEffect(() => {
    const fetchAddresses = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await addressService.searchPostcodes(searchParams);
        setPostcodeSuggestions(response || []);
      } catch (err: unknown) {
        console.error('Address fetch error:', err);
        if (err instanceof Error) {
          setError(err.message || 'Failed to fetch addresses');
        } else {
          setError('Failed to fetch addresses');
        }
        toast.error('Failed to fetch addresses');
      } finally {
        setLoading(false);
      }
    };

    if (Object.keys(searchParams).length > 0) {
      fetchAddresses();
    }
  }, [searchParams]);

  // Postcode suggestions (live lookup, debounced)
  const debouncedSearchPostcodes = useDebouncedCallback(async (params: SearchPostcodes) => {
    setSearching(true);
    try {
      const response = await customerApi.searchPostcodes(params);
      setPostcodeSuggestions(response.data || []);
    } catch (err) {
      console.error('Postcode search failed:', err);
    } finally {
      setSearching(false);
    }
  }, 500); // debounce for 500ms

  // Manual search trigger to update addresses based on params
  const searchAddresses = (params: SearchPostcodes) => {
    setSearchParams(params);
  };

  // Create new address
  const createAddress = async (data: CreateAddressData) => {
    setLoading(true);
    setError(null);
    try {
      const newAddress = await addressService.createAddress(data);
      setAddresses(prev => [newAddress, ...prev]);
      toast.success('Address created successfully');
      return newAddress;
    } catch (err: unknown) {
      console.error('Address create error:', err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to create address');
      } else {
        setError('Failed to create address');
      }
      toast.error('Failed to create address');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const editAddress = async (data: EditAddressData) => {
    setLoading(true);
    setError(null);
    try {
      const updatedAddress = await addressService.editAddress(data);
      setAddresses(prev =>
        prev.map(addr => (addr.address_id === data.address_id ? updatedAddress : addr))
      );
      toast.success('Address updated successfully');
      return updatedAddress;
    } catch (err: unknown) {
      console.error('Address edit error:', err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to update address');
      } else {
        setError('Failed to update address');
      }
      toast.error('Failed to update address');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    // State
    addresses,
    postcodeSuggestions,
    searching,
    loading,
    error,
    searchParams,

    // Setters / Triggers
    setSearchParams,
    debouncedSearchPostcodes,
    searchAddresses,

    // CRUD
    createAddress,
    editAddress,

    // Raw service (if needed)
    addressService,
  };
};
