

Analyze the provided HTML prototype code for a web application. From this analysis, create a JSON database schema that represents the data structure needed to support the application. The schema should include:

Entities and Attributes: Identify all key entities (e.g., users, products, orders) based on the data displayed in the HTML (e.g., text, tables, lists) and form inputs (e.g., text fields, dropdowns, checkboxes). For each entity, define attributes, including their data types (e.g., string, number, boolean, date), and specify whether they are required or optional.

Relationships: Identify relationships between entities (e.g., one-to-many, many-to-one) based on how data is presented or linked in the HTML (e.g., foreign keys implied by IDs or references in forms).
Form Data: For each form in the HTML, list the input fields (e.g., name, type, constraints like required or maxlength), their purpose, and how they map to the schema's entities and attributes.
Displayed Data: Extract data displayed in the HTML (e.g., in tables, lists, or text) and map it to the schema, ensuring all displayed fields are accounted for.
Constraints and Validations: Include any constraints or validations implied by the HTML (e.g., required fields, email formats, numeric ranges).
JSON Schema Format: Provide the output as a valid JSON schema (following JSON Schema Draft 2020-12 or later) with clear entity definitions, properties, and relationships.
Please ensure the schema is normalized to avoid redundancy and follows best practices for database design. If there are ambiguities in the HTML (e.g., unclear data types or relationships), make reasonable assumptions and document them in comments within the schema. Provide a brief explanation of your analysis process and any assumptions made.