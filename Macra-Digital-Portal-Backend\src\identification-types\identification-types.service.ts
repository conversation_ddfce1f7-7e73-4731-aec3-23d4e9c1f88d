import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IdentificationType } from '../entities/identification-type.entity';
import { CreateIdentificationTypeDto } from '../dto/identification-types/create-identification-type.dto';
import { UpdateIdentificationTypeDto } from '../dto/identification-types/update-identification-type.dto';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';

@Injectable()
export class IdentificationTypesService {
  constructor(
    @InjectRepository(IdentificationType)
    private identificationTypesRepository: Repository<IdentificationType>,
  ) {}

  async findAll(query: PaginateQuery): Promise<PaginatedResult<IdentificationType>> {
    const config: PaginateConfig<IdentificationType> = {
      sortableColumns: ['name', 'created_at'],
      searchableColumns: ['name'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      relations: ['creator', 'updater'],
    };

    const result = await paginate(query, this.identificationTypesRepository, config);
    return PaginationTransformer.transform<IdentificationType>(result);
  }

  async findOne(id: string): Promise<IdentificationType> {
    const identificationType = await this.identificationTypesRepository.findOne({
      where: { identification_type_id: id },
      relations: ['creator', 'updater', 'user_identifications'],
    });

    if (!identificationType) {
      throw new NotFoundException('Identification type not found');
    }

    return identificationType;
  }

  async create(createIdentificationTypeDto: CreateIdentificationTypeDto, userId?: string): Promise<IdentificationType> {
    // Check if identification type with same name already exists
    const existingIdentificationType = await this.identificationTypesRepository.findOne({
      where: { name: createIdentificationTypeDto.name },
    });

    if (existingIdentificationType) {
      throw new ConflictException('Identification type with this name already exists');
    }

    const identificationType = this.identificationTypesRepository.create({
      ...createIdentificationTypeDto,
      created_by: userId,
    });

    return this.identificationTypesRepository.save(identificationType);
  }

  async update(id: string, updateIdentificationTypeDto: UpdateIdentificationTypeDto, userId?: string): Promise<IdentificationType> {
    const identificationType = await this.findOne(id);

    // Check if name is being updated and if it conflicts with existing identification type
    if (updateIdentificationTypeDto.name && updateIdentificationTypeDto.name !== identificationType.name) {
      const existingIdentificationType = await this.identificationTypesRepository.findOne({
        where: { name: updateIdentificationTypeDto.name },
      });

      if (existingIdentificationType) {
        throw new ConflictException('Identification type with this name already exists');
      }
    }

    await this.identificationTypesRepository.update(id, {
      ...updateIdentificationTypeDto,
      updated_by: userId,
    });

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const identificationType = await this.findOne(id);
    
    // Check if this identification type is being used by any user identifications
    if (identificationType.user_identifications && identificationType.user_identifications.length > 0) {
      throw new ConflictException('Cannot delete identification type that is being used by users');
    }

    await this.identificationTypesRepository.softDelete(id);
  }

  async findAllSimple(): Promise<IdentificationType[]> {
    return this.identificationTypesRepository.find({
      select: ['identification_type_id', 'name'],
      order: { name: 'ASC' },
    });
  }
}
