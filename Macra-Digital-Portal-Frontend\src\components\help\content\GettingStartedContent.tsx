'use client';

export default function GettingStartedContent() {
  return (
    <>
      <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
              <i className="ri-rocket-line text-red-600 dark:text-red-400"></i>
            </div>
          </div>
          <div className="ml-3">
            <h2 className="text-xl font-medium text-gray-900 dark:text-gray-100">Getting Started with MACRA Digital Portal</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">Learn the basics of using the portal</p>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        <div className="space-y-8">
          {/* What is MACRA Digital Portal */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">What is MACRA Digital Portal?</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              MACRA Digital Portal is a comprehensive platform for managing telecommunications licenses,
              spectrum allocations, and financial transactions. It provides a centralized dashboard for
              monitoring all your regulatory compliance needs with the Malawi Communications Regulatory Authority.
            </p>
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <i className="ri-information-line text-blue-600 dark:text-blue-400"></i>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">Key Features</h4>
                  <ul className="mt-2 text-sm text-blue-700 dark:text-blue-300 list-disc list-inside space-y-1">
                    <li>Online license applications and renewals</li>
                    <li>Real-time spectrum monitoring and management</li>
                    <li>Integrated payment processing</li>
                    <li>Comprehensive reporting and analytics</li>
                    <li>Document management and storage</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* How to Navigate the Dashboard */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">How to Navigate the Dashboard</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              The dashboard provides an overview of your licenses, spectrum allocations, and recent transactions.
              Here's how to navigate effectively:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <i className="ri-menu-line text-red-600 dark:text-red-400 mr-2"></i>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Sidebar Navigation</h4>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Use the sidebar menu to access different sections like License Management,
                  Spectrum Management, and Financial Transactions.
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <i className="ri-search-line text-red-600 dark:text-red-400 mr-2"></i>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Search Functionality</h4>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  The top search bar allows you to quickly find specific licenses,
                  applications, or documents across the platform.
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <i className="ri-dashboard-line text-red-600 dark:text-red-400 mr-2"></i>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Dashboard Cards</h4>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Dashboard cards show key metrics and status updates.
                  Click on any card to view more detailed information.
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <i className="ri-notification-line text-red-600 dark:text-red-400 mr-2"></i>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Notifications</h4>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Stay updated with important alerts about license renewals,
                  payment due dates, and system announcements.
                </p>
              </div>
            </div>
          </div>

          {/* Setting Up Your Account */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Setting Up Your Account</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              To get the most out of MACRA Digital Portal, follow these steps to set up your account:
            </p>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-red-600 dark:text-red-400">1</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Complete Your Profile</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Add your personal information, contact details, and organization data to ensure
                    accurate communication and compliance.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-red-600 dark:text-red-400">2</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Set Up Notification Preferences</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Configure how you want to receive alerts about license renewals,
                    payment reminders, and important updates.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-red-600 dark:text-red-400">3</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Add Team Members</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Invite colleagues and assign appropriate roles to manage different
                    aspects of your organization's regulatory compliance.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-red-600 dark:text-red-400">4</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Import Existing Data</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Upload existing licenses, spectrum allocations, and relevant documents
                    to have all your information in one place.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Start Checklist */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Quick Start Checklist</h3>
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="space-y-3">
                <div className="flex items-center">
                  <input type="checkbox" className="h-4 w-4 text-green-600 dark:text-green-400 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" />
                  <label className="ml-3 text-sm text-green-800 dark:text-green-300">Complete profile setup</label>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="h-4 w-4 text-green-600 dark:text-green-400 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" />
                  <label className="ml-3 text-sm text-green-800 dark:text-green-300">Verify email address</label>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="h-4 w-4 text-green-600 dark:text-green-400 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" />
                  <label className="ml-3 text-sm text-green-800 dark:text-green-300">Set up two-factor authentication</label>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="h-4 w-4 text-green-600 dark:text-green-400 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" />
                  <label className="ml-3 text-sm text-green-800 dark:text-green-300">Configure notification preferences</label>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="h-4 w-4 text-green-600 dark:text-green-400 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" />
                  <label className="ml-3 text-sm text-green-800 dark:text-green-300">Take a tour of the dashboard</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
