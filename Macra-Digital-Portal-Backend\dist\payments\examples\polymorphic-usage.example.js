"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationService = exports.PaymentExamples = void 0;
const payment_entity_1 = require("../entities/payment.entity");
class PaymentExamples {
    paymentsService;
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    async createApplicationPayment(applicationId, userId, createdBy) {
        const paymentDto = {
            invoice_number: `APP-${Date.now()}`,
            amount: 1000.00,
            currency: payment_entity_1.Currency.MWK,
            payment_type: payment_entity_1.PaymentType.APPLICATION_FEE,
            description: 'Application processing fee for telecommunications license',
            due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            issue_date: new Date().toISOString(),
            user_id: userId,
            created_by: createdBy,
            entity_type: 'application',
            entity_id: applicationId,
        };
        return await this.paymentsService.createPayment(paymentDto);
    }
    async createLicenseRenewalPayment(licenseId, userId, createdBy) {
        const paymentDto = {
            invoice_number: `LIC-${Date.now()}`,
            amount: 5000.00,
            currency: payment_entity_1.Currency.MWK,
            payment_type: payment_entity_1.PaymentType.RENEWAL_FEE,
            description: 'Annual license renewal fee',
            due_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
            issue_date: new Date().toISOString(),
            user_id: userId,
            created_by: createdBy,
            entity_type: 'license',
            entity_id: licenseId,
        };
        return await this.paymentsService.createPayment(paymentDto);
    }
    async createProcurementPayment(procurementId, userId, createdBy, amount) {
        const paymentDto = {
            invoice_number: `PROC-${Date.now()}`,
            amount,
            currency: payment_entity_1.Currency.MWK,
            payment_type: payment_entity_1.PaymentType.PROCUREMENT_FEE,
            description: 'Procurement participation fee',
            due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
            issue_date: new Date().toISOString(),
            user_id: userId,
            created_by: createdBy,
            entity_type: 'procurement',
            entity_id: procurementId,
        };
        return await this.paymentsService.createPayment(paymentDto);
    }
    async createInspectionPayment(inspectionId, userId, createdBy) {
        const paymentDto = {
            invoice_number: `INSP-${Date.now()}`,
            amount: 500.00,
            currency: payment_entity_1.Currency.MWK,
            payment_type: payment_entity_1.PaymentType.INSPECTION_FEE,
            description: 'Site inspection fee',
            due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            issue_date: new Date().toISOString(),
            user_id: userId,
            created_by: createdBy,
            entity_type: 'inspection',
            entity_id: inspectionId,
        };
        return await this.paymentsService.createPayment(paymentDto);
    }
    async getApplicationPayments(applicationId) {
        return await this.paymentsService.getPaymentsByEntity('application', applicationId, { page: 1, limit: 10 });
    }
    async getLicensePayments(licenseId) {
        return await this.paymentsService.getPaymentsByEntity('license', licenseId, { page: 1, limit: 10 });
    }
    async createPaymentForAnyEntity(entityType, entityId, userId, createdBy, paymentType, amount, description) {
        return await this.paymentsService.createPaymentForEntity(entityType, entityId, {
            invoice_number: `${entityType.toUpperCase()}-${Date.now()}`,
            amount,
            currency: payment_entity_1.Currency.MWK,
            payment_type: paymentType,
            description,
            due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            issue_date: new Date().toISOString(),
            user_id: userId,
            created_by: createdBy,
        });
    }
    async handleApplicationSubmission(applicationId, userId, adminId) {
        const payment = await this.createApplicationPayment(applicationId, userId, adminId);
        console.log(`Payment created for application ${applicationId}:`, {
            paymentId: payment.payment_id,
            invoiceNumber: payment.invoice_number,
            amount: payment.amount,
            dueDate: payment.due_date,
        });
        return payment;
    }
    async handleLicenseRenewal(licenseId, userId, adminId) {
        const payment = await this.createLicenseRenewalPayment(licenseId, userId, adminId);
        const existingPayments = await this.getLicensePayments(licenseId);
        console.log(`Renewal payment created for license ${licenseId}:`, {
            newPayment: payment.payment_id,
            totalPayments: existingPayments.total,
        });
        return {
            newPayment: payment,
            paymentHistory: existingPayments,
        };
    }
}
exports.PaymentExamples = PaymentExamples;
class ApplicationService {
    paymentsService;
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    async submitApplication(applicationData, userId) {
        const payment = await this.paymentsService.createPaymentForEntity('application', applicationData.id, {
            invoice_number: `APP-${applicationData.id.slice(-8)}`,
            amount: 1000.00,
            currency: payment_entity_1.Currency.MWK,
            payment_type: payment_entity_1.PaymentType.APPLICATION_FEE,
            description: `Application fee for ${applicationData.licenseType}`,
            due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            issue_date: new Date().toISOString(),
            user_id: userId,
            created_by: userId,
        });
        return {
            application: applicationData,
            payment: payment,
        };
    }
}
exports.ApplicationService = ApplicationService;
//# sourceMappingURL=polymorphic-usage.example.js.map