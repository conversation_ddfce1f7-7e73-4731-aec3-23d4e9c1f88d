"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const payments_service_1 = require("./payments.service");
const create_payment_dto_1 = require("./dto/create-payment.dto");
const update_payment_dto_1 = require("./dto/update-payment.dto");
const payment_entity_1 = require("./entities/payment.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let PaymentsController = class PaymentsController {
    paymentsService;
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    async create(createPaymentDto) {
        return this.paymentsService.createPayment(createPaymentDto);
    }
    async findAll(query, status, paymentType, dateRange, search, req) {
        const filters = {
            status,
            paymentType,
            dateRange,
            search,
            userId: req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId,
        };
        const result = await this.paymentsService.getPayments(filters, {
            page: query.page || 1,
            limit: query.limit || 10,
        });
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async getStatistics(req) {
        const userId = req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId;
        return this.paymentsService.getPaymentStatistics(userId);
    }
    async findOne(id) {
        return this.paymentsService.getPaymentById(id);
    }
    async update(id, updatePaymentDto, req) {
        return this.paymentsService.updatePayment(id, updatePaymentDto, req.user.userId);
    }
    async remove(id) {
        await this.paymentsService.deletePayment(id);
        return { message: 'Payment deleted successfully' };
    }
    async markOverduePayments() {
        return this.paymentsService.markOverduePayments();
    }
    async getPaymentsByEntity(entityType, entityId, query) {
        const result = await this.paymentsService.getPaymentsByEntity(entityType, entityId, {
            page: query.page || 1,
            limit: query.limit || 10,
        });
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async createPaymentForEntity(entityType, entityId, createPaymentDto) {
        return this.paymentsService.createPaymentForEntity(entityType, entityId, createPaymentDto);
    }
};
exports.PaymentsController = PaymentsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new payment' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Invoice number already exists' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Created new payment',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_dto_1.CreatePaymentDto]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all payments with optional filters' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payments retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: payment_entity_1.PaymentStatus }),
    (0, swagger_1.ApiQuery)({ name: 'paymentType', required: false, enum: payment_entity_1.PaymentType }),
    (0, swagger_1.ApiQuery)({ name: 'dateRange', required: false, enum: ['last-30', 'last-90', 'last-year'] }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('paymentType')),
    __param(3, (0, common_1.Query)('dateRange')),
    __param(4, (0, common_1.Query)('search')),
    __param(5, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payment statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payment by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Payment ID', type: String }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Viewed payment details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update payment' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Payment ID', type: String }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Updated payment',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_payment_dto_1.UpdatePaymentDto, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete payment' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Payment ID', type: String }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Deleted payment',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('mark-overdue'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark overdue payments (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Overdue payments marked successfully' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Marked overdue payments',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "markOverduePayments", null);
__decorate([
    (0, common_1.Get)('entity/:entityType/:entityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payments for a specific entity' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Entity payments retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String }),
    (0, swagger_1.ApiParam)({ name: 'entityId', description: 'Entity ID', type: String }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __param(2, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getPaymentsByEntity", null);
__decorate([
    (0, common_1.Post)('entity/:entityType/:entityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Create payment for a specific entity' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment created successfully for entity' }),
    (0, swagger_1.ApiParam)({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String }),
    (0, swagger_1.ApiParam)({ name: 'entityId', description: 'Entity ID', type: String }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Created payment for entity',
    }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "createPaymentForEntity", null);
exports.PaymentsController = PaymentsController = __decorate([
    (0, swagger_1.ApiTags)('Payments'),
    (0, common_1.Controller)('payments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [payments_service_1.PaymentsService])
], PaymentsController);
//# sourceMappingURL=payments.controller.js.map