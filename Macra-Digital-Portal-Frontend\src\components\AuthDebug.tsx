'use client';

import { useAuth } from '../contexts/AuthContext';

export default function AuthDebug() {
  const { user, token, loading, isAuthenticated } = useAuth();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h4 className="font-bold mb-2">Auth Debug Info</h4>
      <div className="space-y-1">
        <div>Loading: {loading ? 'true' : 'false'}</div>
        <div>Authenticated: {isAuthenticated ? 'true' : 'false'}</div>
        <div>Has Token: {token ? 'true' : 'false'}</div>
        <div>Has User: {user ? 'true' : 'false'}</div>
        {user && (
          <div>
            <div>User ID: {user.user_id}</div>
            <div>Email: {user.email}</div>
            <div>Roles: {user.roles?.join(', ') || 'none'}</div>
            <div>Is Admin: {user.isAdmin ? 'true' : 'false'}</div>
          </div>
        )}
      </div>
    </div>
  );
}
