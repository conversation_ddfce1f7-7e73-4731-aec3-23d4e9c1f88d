import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
  Res,
  HttpStatus,
  BadRequestException,
  ParseUUIDPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { PaymentsService, PaymentFilters } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { CreateProofOfPaymentDto, UpdateProofOfPaymentStatusDto } from './dto/create-proof-of-payment.dto';
import { PaymentStatus, PaymentType } from './entities/payment.entity';
import { ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import * as fs from 'fs';

@ApiTags('Payments')
@Controller('payments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new payment' })
  @ApiResponse({ status: 201, description: 'Payment created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Invoice number already exists' })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Created new payment',
  })
  async create(@Body() createPaymentDto: CreatePaymentDto) {
    return this.paymentsService.createPayment(createPaymentDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all payments with optional filters' })
  @ApiResponse({ status: 200, description: 'Payments retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, enum: PaymentStatus })
  @ApiQuery({ name: 'paymentType', required: false, enum: PaymentType })
  @ApiQuery({ name: 'dateRange', required: false, enum: ['last-30', 'last-90', 'last-year'] })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Query('status') status?: PaymentStatus,
    @Query('paymentType') paymentType?: PaymentType,
    @Query('dateRange') dateRange?: 'last-30' | 'last-90' | 'last-year',
    @Query('search') search?: string,
    @Request() req?: any,
  ): Promise<PaginatedResult<any>> {
    const filters: PaymentFilters = {
      status,
      paymentType,
      dateRange,
      search,
      // For customers, only show their own payments
      userId: req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId,
    };

    const result = await this.paymentsService.getPayments(filters, {
      page: query.page || 1,
      limit: query.limit || 10,
    });

    return PaginationTransformer.transform(result);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get payment statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getStatistics(@Request() req: any) {
    // For customers, only show their own statistics
    const userId = req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId;
    return this.paymentsService.getPaymentStatistics(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get payment by ID' })
  @ApiResponse({ status: 200, description: 'Payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Viewed payment details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.paymentsService.getPaymentById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update payment' })
  @ApiResponse({ status: 200, description: 'Payment updated successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Updated payment',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePaymentDto: UpdatePaymentDto,
  ) {
    return this.paymentsService.updatePayment(id, updatePaymentDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete payment' })
  @ApiResponse({ status: 200, description: 'Payment deleted successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Deleted payment',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.paymentsService.deletePayment(id);
    return { message: 'Payment deleted successfully' };
  }

  @Post(':id/proof-of-payment')
  @ApiOperation({ summary: 'Upload proof of payment' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        transaction_reference: {
          type: 'string',
        },
        amount: {
          type: 'number',
        },
        currency: {
          type: 'string',
        },
        payment_method: {
          type: 'string',
          enum: ['Bank Transfer', 'Mobile Money', 'Credit Card', 'Cash', 'Cheque'],
        },
        payment_date: {
          type: 'string',
          format: 'date',
        },
        notes: {
          type: 'string',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Proof of payment uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @UseInterceptors(FileInterceptor('file', {
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB
    },
    fileFilter: (_req, file, cb) => {
      if (file.mimetype.match(/\/(jpg|jpeg|png|pdf)$/)) {
        cb(null, true);
      } else {
        cb(new BadRequestException('Only image files and PDFs are allowed'), false);
      }
    },
  }))
  async uploadProofOfPayment(
    @Param('id', ParseUUIDPipe) paymentId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() createProofOfPaymentDto: CreateProofOfPaymentDto,
    @Request() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    // Set the payment_id from the URL parameter
    createProofOfPaymentDto.payment_id = paymentId;

    return this.paymentsService.uploadProofOfPayment(
      createProofOfPaymentDto,
      file,
      req.user.userId,
    );
  }

  @Get('proof-of-payment/list')
  @ApiOperation({ summary: 'Get proof of payments' })
  @ApiResponse({ status: 200, description: 'Proof of payments retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, enum: ProofOfPaymentStatus })
  @ApiQuery({ name: 'paymentId', required: false, type: String })
  async getProofOfPayments(
    @Paginate() query: PaginateQuery,
    @Query('status') status?: ProofOfPaymentStatus,
    @Query('paymentId') paymentId?: string,
    @Request() req?: any,
  ): Promise<PaginatedResult<any>> {
    const filters = {
      status,
      paymentId,
      // For customers, only show their own proof of payments
      userId: req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId,
    };

    const result = await this.paymentsService.getProofOfPayments(filters, {
      page: query.page || 1,
      limit: query.limit || 10,
    });

    return PaginationTransformer.transform(result);
  }

  @Get('proof-of-payment/:id')
  @ApiOperation({ summary: 'Get proof of payment by ID' })
  @ApiResponse({ status: 200, description: 'Proof of payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Proof of payment not found' })
  @ApiParam({ name: 'id', description: 'Proof of payment ID', type: String })
  async getProofOfPayment(@Param('id', ParseUUIDPipe) id: string) {
    return this.paymentsService.getProofOfPaymentById(id);
  }

  @Put('proof-of-payment/:id/status')
  @ApiOperation({ summary: 'Update proof of payment status (admin only)' })
  @ApiResponse({ status: 200, description: 'Proof of payment status updated successfully' })
  @ApiResponse({ status: 404, description: 'Proof of payment not found' })
  @ApiParam({ name: 'id', description: 'Proof of payment ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'ProofOfPayment',
    description: 'Updated proof of payment status',
  })
  async updateProofOfPaymentStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStatusDto: UpdateProofOfPaymentStatusDto,
    @Request() req: any,
  ) {
    return this.paymentsService.updateProofOfPaymentStatus(
      id,
      updateStatusDto,
      req.user.userId,
    );
  }

  @Get('proof-of-payment/:id/download')
  @ApiOperation({ summary: 'Download proof of payment document' })
  @ApiResponse({ status: 200, description: 'Document downloaded successfully' })
  @ApiResponse({ status: 404, description: 'Proof of payment or document not found' })
  async downloadProofOfPayment(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
    @Res() res: Response,
  ) {
    try {
      const { filePath, filename } = await this.paymentsService.downloadProofOfPayment(
        id,
        req.user.userId,
      );

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return res.status(HttpStatus.NOT_FOUND).json({
          message: 'Document file not found',
        });
      }

      // Get file stats
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;

      // Set appropriate headers
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', fileSize.toString());

      // Stream the file
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to download document',
        error: error.message,
      });
    }
  }

  @Post('mark-overdue')
  @ApiOperation({ summary: 'Mark overdue payments (admin only)' })
  @ApiResponse({ status: 200, description: 'Overdue payments marked successfully' })
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Marked overdue payments',
  })
  async markOverduePayments() {
    return this.paymentsService.markOverduePayments();
  }
}