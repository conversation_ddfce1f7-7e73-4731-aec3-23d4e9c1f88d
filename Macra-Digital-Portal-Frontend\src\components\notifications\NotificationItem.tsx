'use client';

import React from 'react';
import { Notification } from '@/hooks/useNotifications';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onNotificationClick?: (notification: Notification) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onNotificationClick,
}) => {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'email':
        return 'ri-mail-line';
      case 'sms':
        return 'ri-message-3-line';
      case 'in_app':
        return 'ri-notification-3-line';
      case 'application_status':
        return 'ri-file-list-3-line';
      case 'evaluation_assigned':
        return 'ri-user-settings-line';
      case 'payment_due':
        return 'ri-money-dollar-circle-line';
      case 'license_expiry':
        return 'ri-calendar-event-line';
      case 'system_alert':
        return 'ri-alert-line';
      default:
        return 'ri-notification-3-line';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgent':
        return 'text-red-600 dark:text-red-400';
      case 'high':
        return 'text-orange-600 dark:text-orange-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'low':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const handleItemClick = () => {
    if (!notification.is_read) {
      onMarkAsRead(notification.notification_id);
    }
    if (onNotificationClick) {
      onNotificationClick(notification);
    }
  };

  const handleMarkAsReadClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.notification_id);
  };

  return (
    <div
      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${
        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={handleItemClick}
    >
      <div className="flex items-start space-x-3">
        {/* Notification Icon */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          !notification.is_read 
            ? 'bg-blue-100 dark:bg-blue-900/50' 
            : 'bg-gray-100 dark:bg-gray-700'
        }`}>
          <i className={`${getNotificationIcon(notification.type)} text-sm ${
            !notification.is_read 
              ? 'text-blue-600 dark:text-blue-400' 
              : 'text-gray-600 dark:text-gray-400'
          }`}></i>
        </div>

        {/* Notification Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className={`text-sm font-medium ${
                !notification.is_read 
                  ? 'text-gray-900 dark:text-gray-100' 
                  : 'text-gray-700 dark:text-gray-300'
              }`}>
                {notification.subject}
              </h4>
              <p className={`mt-1 text-sm ${
                !notification.is_read 
                  ? 'text-gray-700 dark:text-gray-300' 
                  : 'text-gray-500 dark:text-gray-400'
              }`}>
                {notification.message}
              </p>
            </div>

            {/* Priority Indicator */}
            {notification.priority && notification.priority !== 'medium' && (
              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>
                <i className="ri-flag-line text-xs"></i>
              </div>
            )}
          </div>

          {/* Metadata */}
          <div className="mt-2 flex items-center justify-between">
            <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <span>{formatTimeAgo(notification.created_at)}</span>
              {notification.entity_type && (
                <span className="capitalize">{notification.entity_type}</span>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              {!notification.is_read && (
                <button
                  onClick={handleMarkAsReadClick}
                  className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                  title="Mark as read"
                >
                  Mark as read
                </button>
              )}
              {notification.is_read && (
                <span className="text-xs text-green-600 dark:text-green-400 flex items-center">
                  <i className="ri-check-line mr-1"></i>
                  Read
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Unread Indicator */}
        {!notification.is_read && (
          <div className="flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
        )}
      </div>
    </div>
  );
};

export default NotificationItem;
