interface StatsCardProps {
  title: string;
  value: string;
  icon: string;
  iconColor: string;
  trend: string;
  trendColor: string;
  trendIcon: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  iconColor,
  trend,
  trendColor,
  trendIcon,
}) => {
  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className={`flex-shrink-0 ${iconColor} rounded-md p-3`}>
            <i className={`${icon} text-${trendColor.split('-')[0]}-600`}></i>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                <div className={`ml-2 flex items-baseline text-sm font-semibold ${trendColor}`}>
                  <i className={trendIcon}></i>
                  <span className="sr-only">Increased by</span>
                  {trend}
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-5 py-3">
        <a href="#" className="text-sm font-medium text-primary hover:text-primary">
          View all
        </a>
      </div>
    </div>
  );
};

export default StatsCard;