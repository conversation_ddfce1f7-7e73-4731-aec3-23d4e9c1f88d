import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  UpdateDateColumn,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Address } from './address.entity';
import { Contacts } from './contacts.entity';

@Entity('type_approved_manufacturers')
export class TypeApprovedManufacturer {
  @Column({
    type: 'uuid',
    primary: true,
    unique: true,
  })
  manufacturer_id: string;

  @Column({ type: 'varchar', length: 100 })
  manufacturer_name: string;

  @Column({ type: 'uuid', length: 100, nullable: true })
  address_id?: string;

  @Column({ type: 'varchar', length: 100 })
  manufacturer_country_origin: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  manufacturer_region?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  contact_id?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  manufacturer_email?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  manufacturer_phone?: string;

  @Column({ type: 'varchar', length: 100 })
  manufacturer_website: string;

  @Column({ type: 'varchar', length: 40 })
  manufacturer_approval_number: string;

  @Column({ type: 'timestamp' })
  manufacturer_approval_date: Date;

  @Column({ type: 'varchar', length: 50, nullable: true })
  approval_certification_standard?: string; // e.g., GSMA/CEIR Standard

  @Column({ type: 'varchar', length: 100, nullable: true })
  equipment_types?: string; // e.g., Mobile Phones, Modems

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  @ManyToOne(() => User, {onDelete: 'SET NULL', nullable: true})
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, {onDelete: 'SET NULL', nullable: true})
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToOne(() => Address, { nullable: true })
  @JoinColumn({ name: 'address_id' })
  address?: Address;

  @OneToOne(() => Contacts, { nullable: true })
  @JoinColumn({ name: 'contact_id' })
  contact?: Contacts;

  @BeforeInsert()
  generateId() {
    if (!this.manufacturer_id) {
      this.manufacturer_id = uuidv4();
    }
  }

}
