'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { authService } from '@/services/auth.service';
import Cookies from "js-cookie";
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import {
  XCircleIcon
} from '@heroicons/react/24/solid';

export default function CustomerSetup2FAPage() {
  const router = useRouter();
  const { completeTwoFactorLogin } = useAuth();
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Checking verification parameters...');
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);

  const searchParams = useSearchParams();
  const userId = searchParams.get('i') || '';
  const u = searchParams.get('unique') || '';
  const c = searchParams.get('c') || '';

  useEffect(() => {
    if (!userId || !u || !c) {
      setLoading(false);
      const missingParams = [];
      if (!userId) missingParams.push('user ID');
      if (!u) missingParams.push('unique identifier');
      if (!c) missingParams.push('verification code');

      if (missingParams.length <= 3) {
        setUnauthorizedAccess(true);
        setError('Unauthorized access. This page can only be accessed through a valid 2FA verification link sent to your email.');
        setLoadingMessage('Please check your email for the verification link or login again.');
        setLoading(true);
      } else {
        setError(`Invalid verification link. Missing parameters: ${missingParams.join(', ')}.`);
        setLoadingMessage('Invalid link. Redirecting to login...');
        setLoading(true);
      }

      setTimeout(() => {
        router.replace('/customer/auth/login');
      }, 7000);

      return;
    }

    const verify2FA = async () => {
      try {
        setLoadingMessage('Verifying your 2FA code...');
        const { access_token, user, message } = await authService.verify2FA({
          unique: u,
          user_id: userId,
          code: c
        });

        if (access_token && user) {
          setSuccess(message || 'Your account has been verified successfully!');
          setLoadingMessage('Account verified! Redirecting...');

          if (user.two_factor_enabled) {
            // Retrieve rememberMe preference from sessionStorage
            const rememberMe = sessionStorage.getItem('remember_me') === 'true';
            await completeTwoFactorLogin(access_token, user, rememberMe);
            // Clear the remember me preference from session storage
            sessionStorage.removeItem('remember_me');
            setTimeout(() => {
              router.push('/customer');
            }, 2000);
          } else {
            Cookies.set('auth_token', access_token, { expires: 1 });
            Cookies.set('auth_user', JSON.stringify(user), { expires: 1 });
            setLoadingMessage('Account verification complete. Redirecting...');
            setTimeout(() => {
              router.push('/customer/auth/login');
            }, 7000);
          }
        } else {
          setError('Verification failed. Server response incomplete. Please try again.');
          setLoadingMessage('Verification failed. Redirecting to login...');
          setTimeout(() => {
            router.replace('/customer/auth/login');
          }, 7000);
        }
      } catch (err: any) {
        setLoading(false);
        let message = 'Failed to verify 2FA. Please try again.';
        if (err?.response?.status === 400) {
          message = 'Invalid or expired verification code.';
        } else if (err?.response?.status === 401) {
          message = 'Unauthorized. The verification link may have expired or been used.';
        } else if (err?.response?.status === 404) {
          message = 'Verification request not found.';
        } else if (err?.response?.data?.message) {
          message = err.response.data.message;
        } else if (err?.message) {
          message = err.message;
        }

        setError(message);
        setLoadingMessage('Verification failed. Redirecting to login...');
        setLoading(true);
        setTimeout(() => {
          router.replace('/customer/auth/login');
        }, 4000);
      }
    };

    if (userId && u && c) {
      verify2FA();
    } else {
      setError('Missing verification information.');
      setLoadingMessage('Redirecting to login...');
      setLoading(true);
      setTimeout(() => {
        router.replace('/customer/auth/login');
      }, 3000);
    }
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={loadingMessage} />
        </div>
      </div>
    );
  }

  const alreadyEnabled = success.toLowerCase().includes('enabled');
  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image src="/images/macra-logo.png" alt="MACRA Logo" width={50} height={50} className="mx-auto h-16 w-auto" />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {success ? (
            <span className="text-green-600 dark:text-green-300">Account Verification Success!</span>
          ) : error ? (
            <span className="text-red-800 dark:text-red-300">Error</span>
          ) : (
            <span className="text-gray-600 dark:text-gray-300">Account Verification</span>
          )}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          {error && !alreadyEnabled && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md">
                <XCircleIcon className="w-10 h-10 animate-pulse text-red-600 dark:text-red-300" />
              </div>
              <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center">
                {error}
              </div>
              {unauthorizedAccess && (
                <div className="mt-4 w-full">
                  <button
                    onClick={() => router.replace('/customer/auth/login')}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          )}

          {(success || alreadyEnabled) && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                <svg
                  className="w-8 h-8 text-green-600 dark:text-green-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="text-center text-gray-600 dark:text-gray-400">
                {success}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
