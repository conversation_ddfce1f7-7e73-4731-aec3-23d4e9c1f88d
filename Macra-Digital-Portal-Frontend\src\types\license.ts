export interface LicenseType {
  license_type_id: string;
  name: string;
  code?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface LicenseCategory {
  license_category_id: string;
  name: string;
  description?: string;
  license_type_id: string;
  license_type?: LicenseType;
  parent_id?: string;
  parent?: LicenseCategory;
  children?: LicenseCategory[];
  created_at: string;
  updated_at: string;
}

export interface Applicant {
  applicant_id: string;
  name: string;
  business_registration_number: string;
  tpin: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  level_of_insurance_cover?: string;
  address_id?: string;
  contact_id?: string;
  date_incorporation: string;
  place_incorporation: string;
  created_at: string;
  updated_at: string;
}

export interface Application {
  application_id: string;
  application_number: string;
  applicant_id: string;
  license_category_id: string;
  status: ApplicationStatus;
  current_step: number;
  progress_percentage: number;
  submitted_at?: string;
  created_at: string;
  updated_at: string;
  assigned_to?: string;
  assigned_at?: string;
  application_data?: any;
  applicant?:Applicant;
  license_category?: LicenseCategory;
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export enum ApplicationStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  EVALUATION = 'evaluation',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

export interface ApplicationFilters {
  licenseTypeId?: string;
  licenseCategoryId?: string;
  status?: ApplicationStatus | '';
  dateRange?: string;
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems: number;
    currentPage: number;
    totalPages: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    filter: Record<string, string | string[]>;
  };
  links: {
    first: string;
    previous: string;
    current: string;
    next: string;
    last: string;
  };
}
