import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  Create<PERSON>ate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { LicenseCategories } from './license-categories.entity';

@Entity('license_category_documents')
export class LicenseCategoryDocument {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  license_category_document_id: string;

  @Column({ type: 'uuid' })
  license_category_id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'boolean', default: true })
  is_required: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  // Relations
  @ManyToOne(() => LicenseCategories, (licenseCategory) => licenseCategory.license_category_documents, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'license_category_id' })
  license_category: LicenseCategories;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.license_category_document_id) {
      this.license_category_document_id = uuidv4();
    }
  }
}
