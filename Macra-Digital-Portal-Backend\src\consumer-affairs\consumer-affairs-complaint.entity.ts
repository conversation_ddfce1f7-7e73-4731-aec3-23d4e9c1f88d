import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  Join<PERSON><PERSON>um<PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString } from 'class-validator';
import { User } from '../entities/user.entity';

export enum ComplaintCategory {
  BILLING_CHARGES = 'Billing & Charges',
  SERVICE_QUALITY = 'Service Quality',
  NETWORK_ISSUES = 'Network Issues',
  CUSTOMER_SERVICE = 'Customer Service',
  CONTRACT_DISPUTES = 'Contract Disputes',
  ACCESSIBILITY = 'Accessibility',
  FRAUD_SCAMS = 'Fraud & Scams',
  OTHER = 'Other',
}

export enum ComplaintStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum ComplaintPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('consumer_affairs_complaints')
export class ConsumerAffairsComplaint {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  complaint_id: string;

  @Column({ type: 'varchar', unique: true })
  @IsString()
  complaint_number: string; // Pattern: COMP-YYYY-XXX

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  complainant_id: string; // References User who submitted the complaint

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  title: string;

  @Column({ type: 'text' })
  @IsString()
  description: string;

  @Column({
    type: 'enum',
    enum: ComplaintCategory,
  })
  @IsEnum(ComplaintCategory)
  category: ComplaintCategory;

  @Column({
    type: 'enum',
    enum: ComplaintStatus,
    default: ComplaintStatus.SUBMITTED,
  })
  @IsEnum(ComplaintStatus)
  status: ComplaintStatus;

  @Column({
    type: 'enum',
    enum: ComplaintPriority,
    default: ComplaintPriority.MEDIUM,
  })
  @IsEnum(ComplaintPriority)
  priority: ComplaintPriority;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsUUID()
  assigned_to?: string; // Staff member assigned to handle the complaint

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  resolution?: string; // Resolution details when complaint is resolved

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  internal_notes?: string; // Internal notes for staff

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString()
  resolved_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'complainant_id' })
  complainant: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToMany(() => ConsumerAffairsComplaintAttachment, (attachment) => attachment.complaint)
  attachments: ConsumerAffairsComplaintAttachment[];

  @OneToMany(() => ConsumerAffairsComplaintStatusHistory, (history) => history.complaint)
  status_history: ConsumerAffairsComplaintStatusHistory[];

  @BeforeInsert()
  generateId() {
    if (!this.complaint_id) {
      this.complaint_id = uuidv4();
    }
    if (!this.complaint_number) {
      const year = new Date().getFullYear();
      const randomNum = Math.floor(Math.random() * 999) + 1;
      this.complaint_number = `COMP-${year}-${randomNum.toString().padStart(3, '0')}`;
    }
  }
}

// Attachment entity for consumer affairs complaints
@Entity('consumer_affairs_complaint_attachments')
export class ConsumerAffairsComplaintAttachment {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  attachment_id: string;

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  complaint_id: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  file_name: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  file_path: string;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  file_type: string;

  @Column({ type: 'bigint' })
  file_size: number;

  @CreateDateColumn()
  uploaded_at: Date;

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  uploaded_by: string;

  // Relations
  @ManyToOne(() => ConsumerAffairsComplaint, (complaint) => complaint.attachments)
  @JoinColumn({ name: 'complaint_id' })
  complaint: ConsumerAffairsComplaint;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'uploaded_by' })
  uploader: User;

  @BeforeInsert()
  generateId() {
    if (!this.attachment_id) {
      this.attachment_id = uuidv4();
    }
  }
}

// Status history entity for tracking complaint status changes
@Entity('consumer_affairs_complaint_status_history')
export class ConsumerAffairsComplaintStatusHistory {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  history_id: string;

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  complaint_id: string;

  @Column({
    type: 'enum',
    enum: ComplaintStatus,
  })
  @IsEnum(ComplaintStatus)
  status: ComplaintStatus;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  comment?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  created_by: string;

  // Relations
  @ManyToOne(() => ConsumerAffairsComplaint, (complaint) => complaint.status_history)
  @JoinColumn({ name: 'complaint_id' })
  complaint: ConsumerAffairsComplaint;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @BeforeInsert()
  generateId() {
    if (!this.history_id) {
      this.history_id = uuidv4();
    }
  }
}
