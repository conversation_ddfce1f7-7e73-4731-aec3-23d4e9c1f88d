import axios from 'axios';
import { createAuthenticatedAxios, getAuthToken } from '../lib/auth';
import { rolesApiClient, apiClient } from '../lib/apiClient';
import { Role, Permission, PaginatedResponse, PaginateQuery } from './userService';
import { processApiResponse } from '@/lib/authUtils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export interface CreateRoleDto {
  name: string;
  description?: string;
  permission_ids?: string[];
}

export interface UpdateRoleDto {
  name?: string;
  description?: string;
  permission_ids?: string[];
}

export interface RolesResponse extends PaginatedResponse<Role> {}


export const roleService = {
  // Get all roles with pagination
  async getRoles(query: PaginateQuery = {}): Promise<RolesResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await rolesApiClient.get(`?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get role by ID
  async getRole(id: string): Promise<Role> {
    const response = await rolesApiClient.get(`/${id}`);
    return processApiResponse(response);
  },

  // Get role by ID with permissions
  async getRoleWithPermissions(id: string): Promise<Role> {
    const response = await rolesApiClient.get(`/${id}?include=permissions`);
    return processApiResponse(response);
  },

  // Create new role
  async createRole(roleData: CreateRoleDto): Promise<Role> {
    const response = await rolesApiClient.post('', roleData);
    return processApiResponse(response);
  },

  // Update role
  async updateRole(id: string, roleData: UpdateRoleDto): Promise<Role> {
    const response = await rolesApiClient.patch(`/${id}`, roleData);
    return processApiResponse(response);
  },

  // Delete role
  async deleteRole(id: string): Promise<void> {
    await rolesApiClient.delete(`/${id}`);
  },

  // Assign permissions to role
  async assignPermissions(roleId: string, permissionIds: string[]): Promise<Role> {
    const response = await rolesApiClient.post(`/${roleId}/permissions`, {
      permission_ids: permissionIds,
    });
    return processApiResponse(response);
  },

  // Remove permissions from role
  async removePermissions(roleId: string, permissionIds: string[]): Promise<Role> {
    const response = await rolesApiClient.delete(`/${roleId}/permissions`, {
      data: { permission_ids: permissionIds },
    });
    return processApiResponse(response);
  },

  // Get all permissions
  async getPermissions(): Promise<Permission[]> {
    const response = await apiClient.get('/permissions');
    return processApiResponse(response);
  },
};
