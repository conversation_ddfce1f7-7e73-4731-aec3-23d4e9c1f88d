'use client';

import React, { useEffect } from 'react';

interface LoadingOptimizerProps {
  children: React.ReactNode;
}

const LoadingOptimizer: React.FC<LoadingOptimizerProps> = ({ children }) => {
  useEffect(() => {
    // Optimize images with lazy loading
    const optimizeImages = () => {
      const images = document.querySelectorAll('img[data-src]');
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || '';
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        });
      });

      images.forEach((img) => imageObserver.observe(img));
    };

    // Optimize page performance
    const optimizePerformance = () => {
      // Add will-change to elements that will animate
      const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');
      animatedElements.forEach((el) => {
        (el as HTMLElement).style.willChange = 'transform, background-color';
      });

      // Optimize scroll performance
      document.documentElement.style.scrollBehavior = 'smooth';
    };

    // Run optimizations after DOM is ready
    const timer = setTimeout(() => {
      optimizeImages();
      optimizePerformance();
    }, 100);

    return () => {
      clearTimeout(timer);
      // Cleanup will-change properties
      const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');
      animatedElements.forEach((el) => {
        (el as HTMLElement).style.willChange = 'auto';
      });
    };
  }, []);

  return <>{children}</>;
};

export default LoadingOptimizer;