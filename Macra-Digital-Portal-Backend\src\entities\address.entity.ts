import {
  <PERSON><PERSON><PERSON>,
  
  <PERSON>umn,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';

@Entity('addresses')
export class Address {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  address_id: string;

  @Column({ type: 'text' })
  address_line_1: string;

  @Column({ type:'varchar', default: 'postal', nullable: true })
  address_type: string;

  @Column({ type: 'varchar', default: 'user', nullable: true})
  entity_type:string;
  
  @Column({ type: 'varchar', length: 36, nullable: true })
  entity_id: string;

  @Column({ type: 'text', nullable: true })
  address_line_2?: string;

  @Column({ type: 'text', nullable: true })
  address_line_3?: string;

  @Column({ type: 'varchar', length: 9 })
  postal_code: string;

  @Column({ type: 'varchar', length: 50 })
  country: string;

  @Column({ type: 'varchar', length: 50 })
  city: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.address_id) {
      this.address_id = uuidv4();
    }
  }
}
