import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ParseEnumPipe,
  ParseIntPipe,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApplicationsService } from './applications.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { Applications } from '../entities/applications.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('Applications')
@Controller('applications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new application' })
  @ApiResponse({
    status: 201,
    description: 'Application created successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.CREATE,
    resourceType: 'Application',
    description: 'Created new application',
  })
  async create(
    @Body() createApplicationDto: CreateApplicationDto,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.create(createApplicationDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all applications with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed applications list',
  })
  async findAll(@Paginate() query: PaginateQuery, @Request() req: any): Promise<PaginatedResult<Applications>> {
    // Extract user roles and userId from the request
    const userRoles = req.user?.roles || [];
    const userId = req.user?.userId;
    const result = await this.applicationsService.findAll(query, userRoles, userId);
    return PaginationTransformer.transform<Applications>(result);
  }



  @Get('stats')
  @ApiOperation({ summary: 'Get application statistics' })
  @ApiResponse({
    status: 200,
    description: 'Application statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed application statistics',
  })
  async getStats(): Promise<any> {
    return this.applicationsService.getApplicationStats();
  }

  @Get('by-applicant/:applicantId')
  @ApiOperation({ summary: 'Get applications by applicant' })
  @ApiParam({ name: 'applicantId', description: 'Applicant UUID' })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
    type: [Applications],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed applications by applicant',
  })
  async findByApplicant(@Param('applicantId', ParseUUIDPipe) applicantId: string): Promise<Applications[]> {
    return this.applicationsService.findByApplicant(applicantId);
  }

  @Get('by-status/:status')
  @ApiOperation({ summary: 'Get applications by status' })
  @ApiParam({ name: 'status', description: 'Application status' })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
    type: [Applications],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed applications by status',
  })
  async findByStatus(
    @Param('status') status: string,
  ): Promise<Applications[]> {
    return this.applicationsService.findByStatus(status);
  }

  @Get('user-applications')
  @ApiOperation({ summary: 'Get current user\'s applications (customer-facing endpoint)' })
  // Note: Using 'user-applications' instead of 'my-applications' to avoid route conflicts
  @ApiResponse({
    status: 200,
    description: 'User applications retrieved successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - user not authenticated',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed user applications',
  })
  async getMyApplications(@Paginate() query: PaginateQuery, @Request() req: any): Promise<PaginatedResult<Applications>> {
    // This endpoint is specifically for customers to view only their own applications
    // It explicitly filters by the authenticated user's ID regardless of role
    const userId = req.user?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in request');
    }

    const userQuery: PaginateQuery = {
      ...query,
      filter: {
        ...query.filter,
        created_by: userId
      }
    };

    const result = await this.applicationsService.findUserApplications(userQuery);
    return PaginationTransformer.transform<Applications>(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get application by ID' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application retrieved successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed application details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Applications> {
    return this.applicationsService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update application' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application updated successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Updated application',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateApplicationDto: UpdateApplicationDto,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.update(id, updateApplicationDto, req.user.userId);
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update application status' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiQuery({ name: 'status', description: 'New status' })
  @ApiResponse({
    status: 200,
    description: 'Application status updated successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Updated application status',
  })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('status') status: string,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.updateStatus(id, status, req.user.userId);
  }

  @Put(':id/progress')
  @ApiOperation({ summary: 'Update application progress' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiQuery({ name: 'currentStep', description: 'Current step (1-6)' })
  @ApiQuery({ name: 'progressPercentage', description: 'Progress percentage (0-100)' })
  @ApiResponse({
    status: 200,
    description: 'Application progress updated successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Updated application progress',
  })
  async updateProgress(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('currentStep', ParseIntPipe) currentStep: number,
    @Query('progressPercentage', ParseIntPipe) progressPercentage: number,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.updateProgress(id, currentStep, progressPercentage, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete application' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Deleted application',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.applicationsService.remove(id);
    return { message: 'Application deleted successfully' };
  }

  @Put(':id/assign')
  @ApiOperation({ summary: 'Assign application to officer' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application assigned successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Assigned application to officer',
  })
  async assignApplication(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignData: { assignedTo: string },
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.assignApplication(id, assignData.assignedTo, req.user.userId);
  }

  @Get('unassigned')
  @ApiOperation({ summary: 'Get unassigned applications' })
  @ApiResponse({
    status: 200,
    description: 'Unassigned applications retrieved successfully',
  })
  async getUnassignedApplications(@Paginate() query: PaginateQuery) {
    return this.applicationsService.getUnassignedApplications(query);
  }

  @Get('assigned/me')
  @ApiOperation({ summary: 'Get applications assigned to current user' })
  @ApiResponse({
    status: 200,
    description: 'Assigned applications retrieved successfully',
  })
  async getMyAssignedApplications(@Paginate() query: PaginateQuery, @Request() req: any) {
    return this.applicationsService.getAssignedApplications(req.user.userId, query);
  }

  @Get('debug/all')
  @ApiOperation({ summary: 'Debug: Get all applications without role filtering' })
  @ApiResponse({
    status: 200,
    description: 'All applications retrieved successfully (debug)',
  })
  async getDebugApplications(@Paginate() query: PaginateQuery): Promise<PaginatedResult<Applications>> {
    const result = await this.applicationsService.findAllDebug(query);
    return PaginationTransformer.transform<Applications>(result);
  }

  @Post('debug/test-task-creation/:id')
  @ApiOperation({ summary: 'Debug: Test task creation for an application' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Task creation test completed',
  })
  async testTaskCreation(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<{ success: boolean; message: string; taskId?: string }> {
    try {
      // Force update the application status to submitted to trigger task creation
      const application = await this.applicationsService.updateStatus(id, 'submitted', req.user.userId);
      return {
        success: true,
        message: `Task creation test completed for application ${application.application_number}`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Task creation test failed: ${error.message}`,
      };
    }
  }

  @Post('debug/force-submit/:id')
  @ApiOperation({ summary: 'Debug: Force submit an application to test task creation' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application submitted and task creation attempted',
  })
  async forceSubmitApplication(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<{ success: boolean; message: string; previousStatus?: string; newStatus?: string }> {
    try {
      // Get current application to see its status
      const currentApp = await this.applicationsService.findOne(id);
      const previousStatus = currentApp.status;

      console.log(`🧪 DEBUG: Force submitting application ${currentApp.application_number} (current status: ${previousStatus})`);

      // Use the general update method to change status to submitted
      const updatedApp = await this.applicationsService.update(id, { status: 'submitted' }, req.user.userId);

      return {
        success: true,
        message: `Application ${updatedApp.application_number} force submitted successfully`,
        previousStatus,
        newStatus: updatedApp.status,
      };
    } catch (error) {
      console.error('🧪 DEBUG: Force submit failed:', error);
      return {
        success: false,
        message: `Force submit failed: ${error.message}`,
      };
    }
  }

  @Get('debug/check-notifications/:id')
  @ApiOperation({ summary: 'Debug: Check if notifications exist for an application' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Notification check completed',
  })
  async checkNotifications(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // Get the application with applicant details
      const application = await this.applicationsService.findOne(id);

      if (!application.applicant) {
        return {
          success: false,
          message: 'Application has no applicant information',
        };
      }

      console.log(`🧪 DEBUG: Checking notifications for application ${application.application_number}`);
      console.log(`🧪 DEBUG: Applicant details:`, {
        id: application.applicant_id,
        name: application.applicant.name,
        email: application.applicant.email,
        phone: application.applicant.phone,
        status: application.status
      });

      return {
        success: true,
        message: `Notification check completed for application ${application.application_number}`,
        details: {
          applicationId: application.application_id,
          applicantId: application.applicant_id,
          applicantEmail: application.applicant.email,
          applicantName: application.applicant.name,
          applicationNumber: application.application_number,
          currentStatus: application.status,
          hasApplicant: !!application.applicant,
          hasEmail: !!application.applicant?.email,
          hasPhone: !!application.applicant?.phone,
        }
      };
    } catch (error) {
      console.error('🧪 DEBUG: Notification check failed:', error);
      return {
        success: false,
        message: `Notification check failed: ${error.message}`,
      };
    }
  }
}
