import { PaymentStatus, PaymentType, Currency } from '../entities/payment.entity';
export declare class PaymentResponseDto {
    payment_id: string;
    invoice_number: string;
    amount: number;
    currency: Currency;
    status: PaymentStatus;
    payment_type: PaymentType;
    description: string;
    due_date: Date;
    issue_date: Date;
    paid_date?: Date;
    payment_method?: string;
    notes?: string;
    transaction_reference?: string;
    user_id: string;
    application_id?: string;
    created_at: Date;
    updated_at: Date;
    user: UserBasicInfoDto;
    proof_of_payments: ProofOfPaymentResponseDto[];
}
export declare class ProofOfPaymentResponseDto {
    proof_id: string;
    transaction_reference: string;
    amount: number;
    currency: string;
    payment_method: string;
    payment_date: Date;
    original_filename: string;
    file_size: number;
    mime_type: string;
    notes?: string;
    review_notes?: string;
    reviewed_by?: string;
    reviewed_at?: Date;
    payment_id: string;
    submitted_by: string;
    created_at: Date;
    updated_at: Date;
    user: UserBasicInfoDto;
    reviewer?: UserBasicInfoDto;
}
export declare class UserBasicInfoDto {
    user_id: string;
    email: string;
    first_name: string;
    last_name: string;
    middle_name?: string;
}
export declare class PaymentStatisticsDto {
    totalPayments: number;
    paidPayments: number;
    pendingPayments: number;
    overduePayments: number;
    totalAmount: number;
    paidAmount: number;
    pendingAmount: number;
}
export declare class PaginatedPaymentResponseDto {
    payments: PaymentResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export declare class PaginatedProofOfPaymentResponseDto {
    proofOfPayments: ProofOfPaymentResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
