'use client';

import { useState } from 'react';

interface NotificationSetting {
  id: string;
  label: string;
  description: string;
  enabled: boolean;
}

export default function NotificationPreferences() {
  const [notifications, setNotifications] = useState<NotificationSetting[]>([
    {
      id: 'email_notifications',
      label: 'Email Notifications',
      description: 'Receive updates via email',
      enabled: true
    },
    {
      id: 'license_expiry',
      label: 'License Expiry Alerts',
      description: 'Get notified before licenses expire',
      enabled: true
    },
    {
      id: 'payment_reminders',
      label: 'Payment Reminders',
      description: 'Receive payment due notifications',
      enabled: true
    },
    {
      id: 'application_updates',
      label: 'Application Updates',
      description: 'Get updates on application status',
      enabled: true
    },
    {
      id: 'system_maintenance',
      label: 'System Maintenance',
      description: 'Get notified about scheduled maintenance',
      enabled: false
    },
    {
      id: 'security_alerts',
      label: 'Security Alerts',
      description: 'Important security notifications',
      enabled: true
    }
  ]);

  const [isSaving, setIsSaving] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleToggle = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, enabled: !notification.enabled }
          : notification
      )
    );
    
    // Clear any previous messages
    setSuccess(null);
    setError(null);
  };

  const handleSave = async () => {
    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Here you would make an actual API call to save preferences
      // await userService.updateNotificationPreferences(notifications);
      
      setSuccess('Notification preferences updated successfully');
    } catch (err) {
      setError('Failed to update notification preferences');
      console.error('Error updating notification preferences:', err);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Notification Preferences
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Choose which notifications you want to receive and how you want to receive them.
        </p>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"></i>
            <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
            <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
          </div>
        </div>
      )}

      {/* Notification Settings */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
              <i className="ri-notification-line text-gray-500 dark:text-gray-400 mr-2"></i>
              Email Notifications
            </h4>
            
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div 
                  key={notification.id} 
                  className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center h-5">
                    <input 
                      id={notification.id}
                      type="checkbox" 
                      checked={notification.enabled}
                      onChange={() => handleToggle(notification.id)}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-primary dark:ring-offset-gray-800"
                      aria-describedby={`${notification.id}-description`}
                    />
                  </div>
                  <div className="flex-1">
                    <label 
                      htmlFor={notification.id} 
                      className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer"
                    >
                      {notification.label}
                    </label>
                    <p 
                      id={`${notification.id}-description`}
                      className="text-xs text-gray-500 dark:text-gray-400 mt-1"
                    >
                      {notification.description}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      notification.enabled 
                        ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                    }`}>
                      {notification.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Settings */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
              <i className="ri-settings-line text-gray-500 dark:text-gray-400 mr-2"></i>
              Delivery Settings
            </h4>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                <div className="flex items-center h-5">
                  <input 
                    id="digest_mode"
                    type="checkbox" 
                    defaultChecked={false}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-primary dark:ring-offset-gray-800"
                  />
                </div>
                <div className="flex-1">
                  <label 
                    htmlFor="digest_mode" 
                    className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer"
                  >
                    Daily Digest
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Receive a daily summary instead of individual notifications
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                <div className="flex items-center h-5">
                  <input 
                    id="mobile_notifications"
                    type="checkbox" 
                    defaultChecked={true}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-primary dark:ring-offset-gray-800"
                  />
                </div>
                <div className="flex-1">
                  <label 
                    htmlFor="mobile_notifications" 
                    className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer"
                  >
                    Mobile Push Notifications
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Receive notifications on your mobile device
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isSaving ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Preferences
            </>
          )}
        </button>
      </div>
    </div>
  );
}
