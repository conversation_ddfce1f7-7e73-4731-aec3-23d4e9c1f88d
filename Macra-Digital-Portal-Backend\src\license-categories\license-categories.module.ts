import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LicenseCategoriesController } from './license-categories.controller';
import { LicenseCategoriesService } from './license-categories.service';
import { LicenseCategories } from '../entities/license-categories.entity';
import { LicenseTypes } from '../entities/license-types.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LicenseCategories, LicenseTypes])],
  controllers: [LicenseCategoriesController],
  providers: [LicenseCategoriesService],
  exports: [LicenseCategoriesService],
})
export class LicenseCategoriesModule {}
