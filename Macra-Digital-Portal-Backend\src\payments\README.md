# Polymorphic Payment System

The payment system has been updated to use a polymorphic relationship structure, similar to the documents entity. This allows payments to be associated with any type of entity in the system.

## Key Changes

### 1. Entity Structure

The `Payment` entity now includes:
- `entity_type`: String field indicating the type of entity (e.g., 'application', 'license', 'procurement')
- `entity_id`: UUID field containing the ID of the related entity
- `created_by`: UUID field for audit trail
- `updated_by`: UUID field for audit trail (optional)
- `deleted_at`: Soft delete timestamp

### 2. Removed Fields

- `application_id`: Replaced with polymorphic `entity_type` and `entity_id`

### 3. New Relationships

- `creator`: ManyToOne relationship with User (who created the payment)
- `updater`: ManyToOne relationship with User (who last updated the payment)

## Usage Examples

### Creating a Payment for an Application

```typescript
const paymentDto: CreatePaymentDto = {
  invoice_number: 'INV-2024-001',
  amount: 1000.00,
  currency: Currency.MWK,
  payment_type: PaymentType.APPLICATION_FEE,
  description: 'Application fee for telecommunications license',
  due_date: '2024-02-01',
  issue_date: '2024-01-01',
  user_id: 'user-uuid',
  created_by: 'admin-uuid',
  entity_type: 'application',
  entity_id: 'application-uuid'
};

const payment = await paymentsService.createPayment(paymentDto);
```

### Creating a Payment for a License

```typescript
const paymentDto: CreatePaymentDto = {
  invoice_number: 'INV-2024-002',
  amount: 5000.00,
  currency: Currency.MWK,
  payment_type: PaymentType.LICENSE_FEE,
  description: 'Annual license fee',
  due_date: '2024-12-31',
  issue_date: '2024-01-01',
  user_id: 'user-uuid',
  created_by: 'admin-uuid',
  entity_type: 'license',
  entity_id: 'license-uuid'
};

const payment = await paymentsService.createPayment(paymentDto);
```

### Getting Payments for a Specific Entity

```typescript
// Get all payments for an application
const applicationPayments = await paymentsService.getPaymentsByEntity(
  'application',
  'application-uuid',
  { page: 1, limit: 10 }
);

// Get all payments for a license
const licensePayments = await paymentsService.getPaymentsByEntity(
  'license',
  'license-uuid',
  { page: 1, limit: 10 }
);
```

### Using the Convenience Method

```typescript
// Create payment for an entity using the helper method
const payment = await paymentsService.createPaymentForEntity(
  'application',
  'application-uuid',
  {
    invoice_number: 'INV-2024-003',
    amount: 500.00,
    currency: Currency.MWK,
    payment_type: PaymentType.INSPECTION_FEE,
    description: 'Site inspection fee',
    due_date: '2024-03-01',
    issue_date: '2024-02-01',
    user_id: 'user-uuid',
    created_by: 'admin-uuid'
  }
);
```

## API Endpoints

### Standard Payment Endpoints

- `POST /payments` - Create a new payment
- `GET /payments` - Get all payments with filters
- `GET /payments/:id` - Get payment by ID
- `PUT /payments/:id` - Update payment
- `DELETE /payments/:id` - Delete payment
- `GET /payments/statistics` - Get payment statistics
- `POST /payments/mark-overdue` - Mark overdue payments

### Polymorphic Entity Endpoints

- `GET /payments/entity/:entityType/:entityId` - Get payments for a specific entity
- `POST /payments/entity/:entityType/:entityId` - Create payment for a specific entity

## Supported Entity Types

The system supports payments for various entity types:

- `application` - Application fees, processing fees
- `license` - License fees, renewal fees
- `procurement` - Procurement-related fees
- `inspection` - Inspection fees
- `penalty` - Penalty fees
- `other` - Other miscellaneous payments

## Migration Notes

### Database Changes

When migrating existing data:

1. **Backup existing data** before running migrations
2. **Map existing application_id values** to the new polymorphic structure:
   ```sql
   UPDATE payments 
   SET entity_type = 'application', 
       entity_id = application_id 
   WHERE application_id IS NOT NULL;
   ```
3. **Set created_by values** for existing records (use system user or admin)
4. **Remove the application_id column** after migration is complete

### Code Changes

1. **Update DTOs** to use `entity_type` and `entity_id` instead of `application_id`
2. **Update service calls** to use the new polymorphic methods
3. **Update frontend code** to pass entity type and ID when creating payments

## Benefits

1. **Flexibility**: Payments can be associated with any entity type
2. **Consistency**: Follows the same pattern as the documents entity
3. **Extensibility**: Easy to add new entity types without schema changes
4. **Audit Trail**: Better tracking with created_by and updated_by fields
5. **Soft Deletes**: Payments can be soft deleted for data integrity

## Best Practices

1. **Always specify entity_type and entity_id** when creating payments
2. **Use descriptive entity types** (e.g., 'application', 'license', not 'app', 'lic')
3. **Validate entity existence** before creating payments
4. **Use the convenience methods** (`createPaymentForEntity`, `getPaymentsByEntity`) when working with specific entities
5. **Include proper audit information** (created_by, updated_by) for all operations
