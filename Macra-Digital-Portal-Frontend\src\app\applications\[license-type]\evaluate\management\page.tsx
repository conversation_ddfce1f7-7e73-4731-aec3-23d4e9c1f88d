'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';
import { EvaluationForm } from '@/components/evaluation';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { stakeholderService } from '@/services/stakeholderService';



interface EvaluateManagementPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateManagementPage: React.FC<EvaluateManagementPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [managementData, setManagementData] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Dynamic navigation hook - same as apply pages
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep,
    previousStep,
    currentStep
  } = useDynamicNavigation({
    currentStepRoute: 'management',
    licenseCategoryId,
    applicationId
  });

  // Load application and management data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }

        // Load management data if available
        if (applicationId) {
          try {
            const managementResponse = await stakeholderService.getStakeholdersByApplication(applicationId);
            setManagementData(managementResponse || []);
          } catch (err) {
            console.error('Error loading management data:', err);
            // Continue without management data
          }
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated]);

  // Navigation handlers - modified for evaluation
  const handleNext = () => {
    if (!applicationId || !nextStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${nextStep.route}?${params.toString()}`);
  };

  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Evaluation handlers
  const handleStatusUpdate = async (status: string, comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Status update:', { applicationId, status, comment });
      console.log('Status updated successfully');
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update application status');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSave = async (comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Comment save:', { applicationId, step: 'management', comment });
      console.log('Comment saved successfully');
    } catch (err) {
      console.error('Error saving comment:', err);
      setError('Failed to save comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachmentUpload = async (file: File) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Attachment upload:', { applicationId, step: 'management', fileName: file.name });
      console.log('Attachment uploaded successfully');
    } catch (err) {
      console.error('Error uploading attachment:', err);
      setError('Failed to upload attachment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 min-h-screen overflow-y-auto">
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="management"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
      {/* Management Information Display */}
      <div className="space-y-6">
        {managementData && managementData.length > 0 ? (
          <>
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Management Team ({managementData.length})
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Key management personnel and stakeholders
              </p>
            </div>

            <div className="space-y-6">
              {managementData.map((member, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {member.full_name || 'Unnamed Member'}
                    </h4>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      {member.stakeholder_type || 'Management'}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Position/Title
                      </label>
                      <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded border">
                        <p className="text-sm text-gray-900 dark:text-gray-100">
                          {member.position || 'Not provided'}
                        </p>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Email
                      </label>
                      <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded border">
                        <p className="text-sm text-gray-900 dark:text-gray-100">
                          {member.email || 'Not provided'}
                        </p>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Phone
                      </label>
                      <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded border">
                        <p className="text-sm text-gray-900 dark:text-gray-100">
                          {member.phone || 'Not provided'}
                        </p>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Ownership Percentage
                      </label>
                      <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded border">
                        <p className="text-sm text-gray-900 dark:text-gray-100">
                          {member.ownership_percentage ? `${member.ownership_percentage}%` : 'Not provided'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {member.qualifications && (
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Qualifications
                      </label>
                      <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded border">
                        <p className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                          {member.qualifications}
                        </p>
                      </div>
                    </div>
                  )}

                  {member.experience && (
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Experience
                      </label>
                      <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded border">
                        <p className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                          {member.experience}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <i className="ri-team-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Management Data</h3>
            <p className="text-gray-600 dark:text-gray-400">
              No management information has been provided for this application.
            </p>
          </div>
        )}

        {/* Evaluation Form */}
        <EvaluationForm
          applicationId={applicationId!}
          currentStep="management"
          onStatusUpdate={handleStatusUpdate}
          onCommentSave={handleCommentSave}
          onAttachmentUpload={handleAttachmentUpload}
          isSubmitting={isSubmitting}
        />
      </div>
      </EvaluationLayout>
    </div>
  );
};

export default EvaluateManagementPage;
