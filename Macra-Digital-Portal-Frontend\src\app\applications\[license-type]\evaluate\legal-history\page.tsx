'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';
import { EvaluationForm } from '@/components/evaluation';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { legalHistoryService } from '@/services/legalHistoryService';

interface EvaluateLegalHistoryPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateLegalHistoryPage: React.FC<EvaluateLegalHistoryPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [legalHistoryData, setLegalHistoryData] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Dynamic navigation hook - same as apply pages
  const {
    nextStep,
    previousStep,
  } = useDynamicNavigation({
    currentStepRoute: 'legal-history',
    licenseCategoryId,
    applicationId
  });

  // Load application and legal history data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }

        // Load legal history data if available
        if (applicationId) {
          try {
            const legalResponse = await legalHistoryService.getLegalHistoryByApplication(applicationId);
            setLegalHistoryData(legalResponse);
          } catch (err: any) {
            console.warn('Legal history data not available:', err?.response?.status || 'Unknown error');
            // Continue without legal history data - this is expected for applications without this data
            setLegalHistoryData(null);
          }
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated]);


  // Navigation handlers - modified for evaluation
  const handleNext = () => {
    if (!applicationId || !nextStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${nextStep.route}?${params.toString()}`);
  };

  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Evaluation handlers
  const handleStatusUpdate = async (status: string, comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Status update:', { applicationId, status, comment });
      console.log('Status updated successfully');
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update application status');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSave = async (comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Comment save:', { applicationId, step: 'legal-history', comment });
      console.log('Comment saved successfully');
    } catch (err) {
      console.error('Error saving comment:', err);
      setError('Failed to save comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachmentUpload = async (file: File) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Attachment upload:', { applicationId, step: 'legal-history', fileName: file.name });
      console.log('Attachment uploaded successfully');
    } catch (err) {
      console.error('Error uploading attachment:', err);
      setError('Failed to upload attachment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  const renderBooleanField = (label: string, value: boolean | undefined, details?: string) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 
                   'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          }`}>
            {value ? 'Yes' : 'No'}
          </span>
        </div>
        {value && details && (
          <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded border">
            <p className="text-sm text-gray-900 dark:text-gray-100">{details}</p>
          </div>
        )}
      </div>
    </div>
  );

  return (
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="legal-history"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
      {/* Legal History Information Display */}
      <div className="space-y-6">
        {legalHistoryData ? (
          <>
            {/* Criminal History */}
            {renderBooleanField(
              'Criminal History',
              legalHistoryData.criminal_history,
              legalHistoryData.criminal_details
            )}

            {/* Bankruptcy History */}
            {renderBooleanField(
              'Bankruptcy History',
              legalHistoryData.bankruptcy_history,
              legalHistoryData.bankruptcy_details
            )}

            {/* Regulatory Actions */}
            {renderBooleanField(
              'Regulatory Actions',
              legalHistoryData.regulatory_actions,
              legalHistoryData.regulatory_details
            )}

            {/* Litigation History */}
            {renderBooleanField(
              'Litigation History',
              legalHistoryData.litigation_history,
              legalHistoryData.litigation_details
            )}

            {/* Additional Information */}
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Compliance Record
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                    {legalHistoryData.compliance_record || 'Not provided'}
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Previous Licenses
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                  <p className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                    {legalHistoryData.previous_licenses || 'Not provided'}
                  </p>
                </div>
              </div>
            </div>

            {/* Declaration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Declaration Accepted
              </label>
              <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  legalHistoryData.declaration_accepted ? 
                    'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {legalHistoryData.declaration_accepted ? 'Accepted' : 'Not Accepted'}
                </span>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Legal History Data</h3>
            <p className="text-gray-600 dark:text-gray-400">
              No legal history information has been provided for this application.
            </p>
          </div>
        )}


      </div>

        {/* Evaluation Form */}
        <EvaluationForm
          applicationId={applicationId!}
          currentStep="legal-history"
          onStatusUpdate={handleStatusUpdate}
          onCommentSave={handleCommentSave}
          onAttachmentUpload={handleAttachmentUpload}
          isSubmitting={isSubmitting}
        />
      </EvaluationLayout>
  );
};

export default EvaluateLegalHistoryPage;
